"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6425],{66425:(e,t,a)=>{a.r(t),a.d(t,{AlertDialog:()=>T,AlertDialogAction:()=>q,AlertDialogCancel:()=>B,AlertDialogContent:()=>P,AlertDialogDescription:()=>Y,AlertDialogFooter:()=>L,AlertDialogHeader:()=>V,AlertDialogOverlay:()=>I,AlertDialogPortal:()=>H,AlertDialogTitle:()=>S,AlertDialogTrigger:()=>_});var r=a(53891),l=a(73987),o=a(80428),n=a(77310),s=a(18526),i=a(77292),d=a(7349),c="AlertDialog",[u,f]=(0,o.A)(c,[s.Hs]),g=(0,s.Hs)(),p=e=>{let{__scopeAlertDialog:t,...a}=e,l=g(t);return(0,r.jsx)(s.bL,{...l,...a,modal:!0})};p.displayName=c;var m=l.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...l}=e,o=g(a);return(0,r.jsx)(s.l9,{...o,...l,ref:t})});m.displayName="AlertDialogTrigger";var x=e=>{let{__scopeAlertDialog:t,...a}=e,l=g(t);return(0,r.jsx)(s.ZL,{...l,...a})};x.displayName="AlertDialogPortal";var v=l.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...l}=e,o=g(a);return(0,r.jsx)(s.hJ,{...o,...l,ref:t})});v.displayName="AlertDialogOverlay";var A="AlertDialogContent",[D,j]=u(A),y=(0,d.Dc)("AlertDialogContent"),b=l.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:o,...d}=e,c=g(a),u=l.useRef(null),f=(0,n.s)(t,u),p=l.useRef(null);return(0,r.jsx)(s.G$,{contentName:A,titleName:h,docsSlug:"alert-dialog",children:(0,r.jsx)(D,{scope:a,cancelRef:p,children:(0,r.jsxs)(s.UC,{role:"alertdialog",...c,...d,ref:f,onOpenAutoFocus:(0,i.m)(d.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=p.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsx)(y,{children:o}),(0,r.jsx)(E,{contentRef:u})]})})})});b.displayName=A;var h="AlertDialogTitle",N=l.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...l}=e,o=g(a);return(0,r.jsx)(s.hE,{...o,...l,ref:t})});N.displayName=h;var w="AlertDialogDescription",R=l.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...l}=e,o=g(a);return(0,r.jsx)(s.VY,{...o,...l,ref:t})});R.displayName=w;var C=l.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...l}=e,o=g(a);return(0,r.jsx)(s.bm,{...o,...l,ref:t})});C.displayName="AlertDialogAction";var k="AlertDialogCancel",O=l.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...l}=e,{cancelRef:o}=j(k,a),i=g(a),d=(0,n.s)(t,o);return(0,r.jsx)(s.bm,{...i,...l,ref:d})});O.displayName=k;var E=e=>{let{contentRef:t}=e,a="`".concat(A,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(A,"` by passing a `").concat(w,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(A,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return l.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(a)},[a,t]),null},z=a(96740),F=a(61971);function T(e){let{...t}=e;return(0,r.jsx)(p,{"data-slot":"alert-dialog",...t})}function _(e){let{...t}=e;return(0,r.jsx)(m,{"data-slot":"alert-dialog-trigger",...t})}function H(e){let{...t}=e;return(0,r.jsx)(x,{"data-slot":"alert-dialog-portal",...t})}function I(e){let{className:t,...a}=e;return(0,r.jsx)(v,{"data-slot":"alert-dialog-overlay",className:(0,F.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function P(e){let{className:t,...a}=e;return(0,r.jsxs)(H,{children:[(0,r.jsx)(I,{}),(0,r.jsx)(b,{"data-slot":"alert-dialog-content",className:(0,F.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed left-[50%] top-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function V(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,F.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function L(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,F.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function S(e){let{className:t,...a}=e;return(0,r.jsx)(N,{"data-slot":"alert-dialog-title",className:(0,F.cn)("text-lg font-semibold",t),...a})}function Y(e){let{className:t,...a}=e;return(0,r.jsx)(R,{"data-slot":"alert-dialog-description",className:(0,F.cn)("text-muted-foreground text-sm",t),...a})}function q(e){let{className:t,...a}=e;return(0,r.jsx)(C,{className:(0,F.cn)((0,z.buttonVariants)(),t),...a})}function B(e){let{className:t,...a}=e;return(0,r.jsx)(O,{className:(0,F.cn)((0,z.buttonVariants)({variant:"outline"}),t),...a})}}}]);