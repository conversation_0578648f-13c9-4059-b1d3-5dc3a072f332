# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1ModifyVolumeStatus(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'status': 'str',
        'target_volume_attributes_class_name': 'str'
    }

    attribute_map = {
        'status': 'status',
        'target_volume_attributes_class_name': 'targetVolumeAttributesClassName'
    }

    def __init__(self, status=None, target_volume_attributes_class_name=None, local_vars_configuration=None):  # noqa: E501
        """V1ModifyVolumeStatus - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._status = None
        self._target_volume_attributes_class_name = None
        self.discriminator = None

        self.status = status
        if target_volume_attributes_class_name is not None:
            self.target_volume_attributes_class_name = target_volume_attributes_class_name

    @property
    def status(self):
        """Gets the status of this V1ModifyVolumeStatus.  # noqa: E501

        status is the status of the ControllerModifyVolume operation. It can be in any of following states:  - Pending    Pending indicates that the PersistentVolumeClaim cannot be modified due to unmet requirements, such as    the specified VolumeAttributesClass not existing.  - InProgress    InProgress indicates that the volume is being modified.  - Infeasible   Infeasible indicates that the request has been rejected as invalid by the CSI driver. To    resolve the error, a valid VolumeAttributesClass needs to be specified. Note: New statuses can be added in the future. Consumers should check for unknown statuses and fail appropriately.  # noqa: E501

        :return: The status of this V1ModifyVolumeStatus.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this V1ModifyVolumeStatus.

        status is the status of the ControllerModifyVolume operation. It can be in any of following states:  - Pending    Pending indicates that the PersistentVolumeClaim cannot be modified due to unmet requirements, such as    the specified VolumeAttributesClass not existing.  - InProgress    InProgress indicates that the volume is being modified.  - Infeasible   Infeasible indicates that the request has been rejected as invalid by the CSI driver. To    resolve the error, a valid VolumeAttributesClass needs to be specified. Note: New statuses can be added in the future. Consumers should check for unknown statuses and fail appropriately.  # noqa: E501

        :param status: The status of this V1ModifyVolumeStatus.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and status is None:  # noqa: E501
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501

        self._status = status

    @property
    def target_volume_attributes_class_name(self):
        """Gets the target_volume_attributes_class_name of this V1ModifyVolumeStatus.  # noqa: E501

        targetVolumeAttributesClassName is the name of the VolumeAttributesClass the PVC currently being reconciled  # noqa: E501

        :return: The target_volume_attributes_class_name of this V1ModifyVolumeStatus.  # noqa: E501
        :rtype: str
        """
        return self._target_volume_attributes_class_name

    @target_volume_attributes_class_name.setter
    def target_volume_attributes_class_name(self, target_volume_attributes_class_name):
        """Sets the target_volume_attributes_class_name of this V1ModifyVolumeStatus.

        targetVolumeAttributesClassName is the name of the VolumeAttributesClass the PVC currently being reconciled  # noqa: E501

        :param target_volume_attributes_class_name: The target_volume_attributes_class_name of this V1ModifyVolumeStatus.  # noqa: E501
        :type: str
        """

        self._target_volume_attributes_class_name = target_volume_attributes_class_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1ModifyVolumeStatus):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1ModifyVolumeStatus):
            return True

        return self.to_dict() != other.to_dict()
