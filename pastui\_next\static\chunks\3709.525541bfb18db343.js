"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3709],{93709:(r,e,t)=>{t.r(e),t.d(e,{Badge:()=>i,badgeVariants:()=>s});var n=t(53891),a=t(52722);t(73987);var o=t(61971);let s=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(r){let{className:e,variant:t,...a}=r;return(0,n.jsx)("div",{className:(0,o.cn)(s({variant:t}),e),...a})}}}]);