#!/usr/bin/env python3
"""
测试引用功能的API调用
"""

import requests
import json
import sys

def test_chat_api():
    """测试聊天API的引用功能"""
    url = "http://localhost:8000/api/chat"
    
    payload = {
        "id": "test_citation_" + str(int(time.time())),
        "messages": [
            {
                "role": "user",
                "content": "电子发票重复报销如何防范？"
            }
        ],
        "data": {}
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("发送请求到:", url)
        print("请求数据:", json.dumps(payload, ensure_ascii=False, indent=2))
        print("-" * 50)
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print("响应内容:")
        print(response.text)
        print("-" * 50)
        
        if response.status_code == 200:
            # 检查是否包含引用数据
            response_text = response.text
            if "CITATION_DATA:" in response_text:
                print("✅ 发现引用数据!")

                # 提取引用数据
                import re
                citation_match = re.search(r'<!-- CITATION_DATA: (.*?) -->', response_text, re.DOTALL)
                if citation_match:
                    citation_json_str = citation_match.group(1)
                    print("原始JSON字符串:")
                    print(repr(citation_json_str[:200]) + "...")
                    try:
                        citation_data = json.loads(citation_json_str)
                        print("✅ 引用数据解析成功:")
                        print(json.dumps(citation_data, ensure_ascii=False, indent=2))
                    except json.JSONDecodeError as e:
                        print(f"❌ 引用数据解析失败: {e}")
                        print("尝试修复JSON...")
                        # 尝试修复常见的JSON问题
                        try:
                            # 移除可能的BOM或特殊字符
                            cleaned_json = citation_json_str.strip().lstrip('\ufeff')
                            citation_data = json.loads(cleaned_json)
                            print("✅ 修复后解析成功:")
                            print(json.dumps(citation_data, ensure_ascii=False, indent=2))
                        except json.JSONDecodeError as e2:
                            print(f"❌ 修复后仍然失败: {e2}")
                else:
                    print("❌ 未找到引用数据匹配")
            else:
                print("⚠️ 响应中未包含引用数据")
                
            # 检查是否包含引用标记
            if "[citation:" in response_text:
                print("✅ 发现引用标记!")
                citations = re.findall(r'\[citation:([a-f0-9-]+)\]', response_text)
                print(f"找到 {len(citations)} 个引用: {citations}")
            else:
                print("⚠️ 响应中未包含引用标记")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    import time
    test_chat_api()
