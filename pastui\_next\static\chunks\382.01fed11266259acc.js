"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[382],{60382:(t,e,r)=>{r.r(e),r.d(e,{Alert:()=>n,AlertDescription:()=>d,AlertTitle:()=>c});var a=r(53891),s=r(52722);r(73987);var l=r(61971);let i=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n(t){let{className:e,variant:r,...s}=t;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(i({variant:r}),e),...s})}function c(t){let{className:e,...r}=t;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,l.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...r})}function d(t){let{className:e,...r}=t;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r})}}}]);