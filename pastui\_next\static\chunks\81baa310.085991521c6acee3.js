"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{42372:(t,e,i)=>{let s;i.d(e,{$K:()=>sZ,A:()=>iU,D4:()=>so,DK:()=>sw,Eg:()=>sM,HJ:()=>sl,Lz:()=>ib,N$:()=>i3,NZ:()=>to,OP:()=>tm,S7:()=>sE,TS:()=>iE,Ux:()=>sS,VH:()=>iK,Wu:()=>s8,Z9:()=>t$,cU:()=>sF,c_:()=>tY,dz:()=>i6,ld:()=>sA,qf:()=>si,w4:()=>iO,wJ:()=>sL,xO:()=>ti});var o=i(1674),n=i(50622),r=i(95468);function l(t){let e;return(11==t.nodeType?t.getSelection?t:t.ownerDocument:t).getSelection()}function h(t,e){return!!e&&(t==e||t.contains(1!=e.nodeType?e.parentNode:e))}function a(t,e){if(!e.anchorNode)return!1;try{return h(t,e.anchorNode)}catch(t){return!1}}function c(t){return 3==t.nodeType?x(t,0,t.nodeValue.length).getClientRects():1==t.nodeType?t.getClientRects():[]}function d(t,e,i,s){return!!i&&(p(t,e,i,s,-1)||p(t,e,i,s,1))}function u(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e}function f(t){return 1==t.nodeType&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(t.nodeName)}function p(t,e,i,s,o){for(;;){if(t==i&&e==s)return!0;if(e==(o<0?0:g(t))){if("DIV"==t.nodeName)return!1;let i=t.parentNode;if(!i||1!=i.nodeType)return!1;e=u(t)+(o<0?0:1),t=i}else{if(1!=t.nodeType||1==(t=t.childNodes[e+(o<0?-1:0)]).nodeType&&"false"==t.contentEditable)return!1;e=o<0?g(t):0}}}function g(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function m(t,e){let i=e?t.left:t.right;return{left:i,right:i,top:t.top,bottom:t.bottom}}function w(t,e){let i=e.width/t.offsetWidth,s=e.height/t.offsetHeight;return(i>.995&&i<1.005||!isFinite(i)||1>Math.abs(e.width-t.offsetWidth))&&(i=1),(s>.995&&s<1.005||!isFinite(s)||1>Math.abs(e.height-t.offsetHeight))&&(s=1),{scaleX:i,scaleY:s}}class v{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?g(e):0),i,Math.min(t.focusOffset,i?g(i):0))}set(t,e,i,s){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=s}}let b=null;function y(t){if(t.setActive)return t.setActive();if(b)return t.focus(b);let e=[];for(let i=t;i&&(e.push(i,i.scrollTop,i.scrollLeft),i!=i.ownerDocument);i=i.parentNode);if(t.focus(null==b?{get preventScroll(){return b={preventScroll:!0},!0}}:void 0),!b){b=!1;for(let t=0;t<e.length;){let i=e[t++],s=e[t++],o=e[t++];i.scrollTop!=s&&(i.scrollTop=s),i.scrollLeft!=o&&(i.scrollLeft=o)}}}function x(t,e,i=e){let o=s||(s=document.createRange());return o.setEnd(t,i),o.setStart(t,e),o}function S(t,e,i,s){let o={key:e,code:e,keyCode:i,which:i,cancelable:!0};s&&({altKey:o.altKey,ctrlKey:o.ctrlKey,shiftKey:o.shiftKey,metaKey:o.metaKey}=s);let n=new KeyboardEvent("keydown",o);n.synthetic=!0,t.dispatchEvent(n);let r=new KeyboardEvent("keyup",o);return r.synthetic=!0,t.dispatchEvent(r),n.defaultPrevented||r.defaultPrevented}function M(t){for(;t.attributes.length;)t.removeAttributeNode(t.attributes[0])}function C(t){return t.scrollTop>Math.max(1,t.scrollHeight-t.clientHeight-4)}function k(t,e){for(let i=t,s=e;;)if(3==i.nodeType&&s>0)return{node:i,offset:s};else if(1==i.nodeType&&s>0){if("false"==i.contentEditable)return null;s=g(i=i.childNodes[s-1])}else{if(!i.parentNode||f(i))return null;s=u(i),i=i.parentNode}}function A(t,e){for(let i=t,s=e;;)if(3==i.nodeType&&s<i.nodeValue.length)return{node:i,offset:s};else if(1==i.nodeType&&s<i.childNodes.length){if("false"==i.contentEditable)return null;i=i.childNodes[s],s=0}else{if(!i.parentNode||f(i))return null;s=u(i)+1,i=i.parentNode}}class O{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new O(t.parentNode,u(t),e)}static after(t,e){return new O(t.parentNode,u(t)+1,e)}}let D=[];class T{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(2&this.flags){let i=this.dom,s=null,o;for(let n of this.children){if(7&n.flags){if(!n.dom&&(o=s?s.nextSibling:i.firstChild)){let t=T.get(o);(!t||!t.parent&&t.canReuseDOM(n))&&n.reuseDOM(o)}n.sync(t,e),n.flags&=-8}if(o=s?s.nextSibling:i.firstChild,e&&!e.written&&e.node==i&&o!=n.dom&&(e.written=!0),n.dom.parentNode==i)for(;o&&o!=n.dom;)o=E(o);else i.insertBefore(n.dom,o);s=n.dom}for((o=s?s.nextSibling:i.firstChild)&&e&&e.node==i&&(e.written=!0);o;)o=E(o)}else if(1&this.flags)for(let i of this.children)7&i.flags&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let s=0==g(t)?0:0==e?-1:1;for(;;){let e=t.parentNode;if(e==this.dom)break;0==s&&e.firstChild!=e.lastChild&&(s=t==e.firstChild?-1:1),t=e}i=s<0?t:t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!T.get(i);)i=i.nextSibling;if(!i)return this.length;for(let t=0,e=0;;t++){let s=this.children[t];if(s.dom==i)return e;e+=s.length+s.breakAfter}}domBoundsAround(t,e,i=0){let s=-1,o=-1,n=-1,r=-1;for(let l=0,h=i,a=i;l<this.children.length;l++){let i=this.children[l],c=h+i.length;if(h<t&&c>e)return i.domBoundsAround(t,e,h);if(c>=t&&-1==s&&(s=l,o=h),h>e&&i.dom.parentNode==this.dom){n=l,r=a;break}a=c,h=c+i.breakAfter}return{from:o,to:r<0?i+this.length:r,startDOM:(s?this.children[s-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:n<this.children.length&&n>=0?this.children[n].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),1&e.flags)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,7&this.flags&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=D){this.markDirty();for(let s=t;s<e;s++){let t=this.children[s];t.parent==this&&0>i.indexOf(t)&&t.destroy()}i.length<250?this.children.splice(t,e-t,...i):this.children=[].concat(this.children.slice(0,t),i,this.children.slice(e));for(let t=0;t<i.length;t++)i[t].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new R(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+("Text"==t?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,s,o,n){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!((this.flags|t.flags)&8)}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}function E(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}T.prototype.breakAfter=0;class R{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||0==this.i||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function B(t,e,i,s,o,n,r,l,h){let{children:a}=t,c=a.length?a[e]:null,d=n.length?n[n.length-1]:null,u=d?d.breakAfter:r;if(!(e==s&&c&&!r&&!u&&n.length<2&&c.merge(i,o,n.length?d:null,0==i,l,h))){if(s<a.length){let t=a[s];t&&(o<t.length||t.breakAfter&&(null==d?void 0:d.breakAfter))?(e==s&&(t=t.split(o),o=0),!u&&d&&t.merge(0,o,d,!0,0,h)?n[n.length-1]=t:((o||t.children.length&&!t.children[0].length)&&t.merge(0,o,null,!1,0,h),n.push(t))):(null==t?void 0:t.breakAfter)&&(d?d.breakAfter=1:r=1),s++}for(c&&(c.breakAfter=r,i>0&&(!r&&n.length&&c.merge(i,c.length,n[0],!1,l,0)?c.breakAfter=n.shift().breakAfter:(i<c.length||c.children.length&&0==c.children[c.children.length-1].length)&&c.merge(i,c.length,null,!1,l,0),e++));e<s&&n.length;)if(a[s-1].become(n[n.length-1]))s--,n.pop(),h=n.length?0:l;else if(a[e].become(n[0]))e++,n.shift(),l=n.length?0:h;else break;!n.length&&e&&s<a.length&&!a[e-1].breakAfter&&a[s].merge(0,0,a[e-1],!1,l,h)&&e--,(e<s||n.length)&&t.replaceChildren(e,s,n)}}function L(t,e,i,s,o,n){let r=t.childCursor(),{i:l,off:h}=r.findPos(i,1),{i:a,off:c}=r.findPos(e,-1),d=e-i;for(let t of s)d+=t.length;t.length+=d,B(t,a,c,l,h,s,0,o,n)}let P="undefined"!=typeof navigator?navigator:{userAgent:"",vendor:"",platform:""},H="undefined"!=typeof document?document:{documentElement:{style:{}}},N=/Edge\/(\d+)/.exec(P.userAgent),V=/MSIE \d/.test(P.userAgent),F=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(P.userAgent),W=!!(V||F||N),z=!W&&/gecko\/(\d+)/i.test(P.userAgent),K=!W&&/Chrome\/(\d+)/.exec(P.userAgent),I="webkitFontSmoothing"in H.documentElement.style,q=!W&&/Apple Computer/.test(P.vendor),j=q&&(/Mobile\/\w+/.test(P.userAgent)||P.maxTouchPoints>2);var Y={mac:j||/Mac/.test(P.platform),windows:/Win/.test(P.platform),linux:/Linux|X11/.test(P.platform),ie:W,ie_version:V?H.documentMode||6:F?+F[1]:N?+N[1]:0,gecko:z,gecko_version:z?+(/Firefox\/(\d+)/.exec(P.userAgent)||[0,0])[1]:0,chrome:!!K,chrome_version:K?+K[1]:0,ios:j,android:/Android\b/.test(P.userAgent),webkit:I,safari:q,webkit_version:I?+(/\bAppleWebKit\/(\d+)/.exec(P.userAgent)||[0,0])[1]:0,tabSize:null!=H.documentElement.style.tabSize?"tab-size":"-moz-tab-size"};class G extends T{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){3==t.nodeType&&this.createDOM(t)}merge(t,e,i){return!(8&this.flags)&&(!i||i instanceof G&&!(this.length-(e-t)+i.length>256)&&!(8&i.flags))&&(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new G(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=8&this.flags,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new O(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return function(t,e,i){let s=t.nodeValue.length;e>s&&(e=s);let o=e,n=e,r=0;0==e&&i<0||e==s&&i>=0?!(Y.chrome||Y.gecko)&&(e?(o--,r=1):n<s&&(n++,r=-1)):i<0?o--:n<s&&n++;let l=x(t,o,n).getClientRects();if(!l.length)return null;let h=l[(r?r<0:i>=0)?0:l.length-1];return Y.safari&&!r&&0==h.width&&(h=Array.prototype.find.call(l,t=>t.width)||h),r?m(h,r<0):h||null}(this.dom,t,e)}}class _ extends T{constructor(t,e=[],i=0){for(let s of(super(),this.mark=t,this.children=e,this.length=i,e))s.setParent(this)}setAttrs(t){if(M(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!((this.flags|t.flags)&8)}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?4&this.flags&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,s,o,n){return(!i||!!(i instanceof _&&i.mark.eq(this.mark))&&(!t||!(o<=0))&&(!(e<this.length)||!(n<=0)))&&(L(this,t,e,i?i.children.slice():[],o-1,n-1),this.markDirty(),!0)}split(t){let e=[],i=0,s=-1,o=0;for(let n of this.children){let r=i+n.length;r>t&&e.push(i<t?n.split(t-i):n),s<0&&i>=t&&(s=o),i=r,o++}let n=this.length-t;return this.length=t,s>-1&&(this.children.length=s,this.markDirty()),new _(this.mark,e,n)}domAtPos(t){return U(this,t)}coordsAt(t,e){return Q(this,t,e)}}class X extends T{static create(t,e,i){return new X(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=X.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,e,i,s,o,n){return(!i||i instanceof X&&!!this.widget.compare(i.widget)&&(!(t>0)||!(o<=0))&&(!(e<this.length)||!(n<=0)))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}become(t){return t instanceof X&&t.side==this.side&&this.widget.constructor==t.widget.constructor&&(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(0==this.length)return o.EY.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,s=this.posAtStart;return i?i.slice(s,s+this.length):o.EY.empty}domAtPos(t){return(this.length?0==t:this.side>0)?O.before(this.dom):O.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let s=this.dom.getClientRects(),o=null;if(!s.length)return null;let n=this.side?this.side<0:t>0;for(let e=n?s.length-1:0;o=s[e],t>0?0!=e:e!=s.length-1&&!(o.top<o.bottom);e+=n?-1:1);return m(o,!n)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class $ extends T{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof $&&t.side==this.side}split(){return new $(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?O.before(this.dom):O.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return o.EY.empty}get isHidden(){return!0}}function U(t,e){let i=t.dom,{children:s}=t,o=0;for(let t=0;o<s.length;o++){let n=s[o],r=t+n.length;if(!(r==t&&0>=n.getSide())){if(e>t&&e<r&&n.dom.parentNode==i)return n.domAtPos(e-t);if(e<=t)break;t=r}}for(let t=o;t>0;t--){let e=s[t-1];if(e.dom.parentNode==i)return e.domAtPos(e.length)}for(let t=o;t<s.length;t++){let e=s[t];if(e.dom.parentNode==i)return e.domAtPos(0)}return new O(i,0)}function Q(t,e,i){let s=null,o=-1,n=null,r=-1;!function t(e,l){for(let h=0,a=0;h<e.children.length&&a<=l;h++){let c=e.children[h],d=a+c.length;d>=l&&(c.children.length?t(c,l-a):(!n||n.isHidden&&(i>0||function(t,e){let i=t.coordsAt(0,1),s=e.coordsAt(0,1);return i&&s&&s.top<i.bottom}(n,c)))&&(d>l||a==d&&c.getSide()>0)?(n=c,r=l-a):(a<l||a==d&&0>c.getSide()&&!c.isHidden)&&(s=c,o=l-a)),a=d}}(t,e);let l=(i<0?s:n)||s||n;return l?l.coordsAt(Math.max(0,l==s?o:r),i):function(t){let e=t.dom.lastChild;if(!e)return t.dom.getBoundingClientRect();let i=c(e);return i[i.length-1]||null}(t)}function J(t,e){for(let i in t)"class"==i&&e.class?e.class+=" "+t.class:"style"==i&&e.style?e.style+=";"+t.style:e[i]=t[i];return e}G.prototype.children=X.prototype.children=$.prototype.children=D;let Z=Object.create(null);function tt(t,e,i){if(t==e)return!0;t||(t=Z),e||(e=Z);let s=Object.keys(t),o=Object.keys(e);if(s.length-(i&&s.indexOf(i)>-1?1:0)!=o.length-(i&&o.indexOf(i)>-1?1:0))return!1;for(let n of s)if(n!=i&&(-1==o.indexOf(n)||t[n]!==e[n]))return!1;return!0}function te(t,e,i){let s=!1;if(e)for(let o in e)i&&o in i||(s=!0,"style"==o?t.style.cssText="":t.removeAttribute(o));if(i)for(let o in i)e&&e[o]==i[o]||(s=!0,"style"==o?t.style.cssText=i[o]:t.setAttribute(o,i[o]));return s}class ti{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return -1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var ts=function(t){return t[t.Text=0]="Text",t[t.WidgetBefore=1]="WidgetBefore",t[t.WidgetAfter=2]="WidgetAfter",t[t.WidgetRange=3]="WidgetRange",t}(ts||(ts={}));class to extends o.FB{constructor(t,e,i,s){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=s}get heightRelevant(){return!1}static mark(t){return new tn(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new tl(t,e,e,i,t.widget||null,!1)}static replace(t){let e=!!t.block,i,s;if(t.isBlockGap)i=-5e8,s=4e8;else{let{start:o,end:n}=th(t,e);i=(o?e?-3e8:-1:5e8)-1,s=(n?e?2e8:1:-6e8)+1}return new tl(t,i,s,e,t.widget||null,!0)}static line(t){return new tr(t)}static set(t,e=!1){return o.om.of(t,e)}hasHeight(){return!!this.widget&&this.widget.estimatedHeight>-1}}to.none=o.om.empty;class tn extends to{constructor(t){let{start:e,end:i}=th(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof tn&&this.tagName==t.tagName&&(this.class||(null==(e=this.attrs)?void 0:e.class))==(t.class||(null==(i=t.attrs)?void 0:i.class))&&tt(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw RangeError("Mark decorations may not be empty");return super.range(t,e)}}tn.prototype.point=!1;class tr extends to{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof tr&&this.spec.class==t.spec.class&&tt(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}tr.prototype.mapMode=o.iR.TrackBefore,tr.prototype.point=!0;class tl extends to{constructor(t,e,i,s,n,r){super(e,i,n,t),this.block=s,this.isReplace=r,this.mapMode=s?e<=0?o.iR.TrackBefore:o.iR.TrackAfter:o.iR.TrackDel}get type(){return this.startSide!=this.endSide?ts.WidgetRange:this.startSide<=0?ts.WidgetBefore:ts.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){var e,i;return t instanceof tl&&(e=this.widget,e==(i=t.widget)||!!(e&&i&&e.compare(i)))&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}function th(t,e=!1){let{inclusiveStart:i,inclusiveEnd:s}=t;return null==i&&(i=t.inclusive),null==s&&(s=t.inclusive),{start:null!=i?i:e,end:null!=s?s:e}}function ta(t,e,i,s=0){let o=i.length-1;o>=0&&i[o]+s>=t?i[o]=Math.max(i[o],e):i.push(t,e)}tl.prototype.point=!0;class tc extends T{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,s,o,n){if(i){if(!(i instanceof tc))return!1;this.dom||i.transferDOM(this)}return s&&this.setDeco(i?i.attrs:null),L(this,t,e,i?i.children.slice():[],o,n),!0}split(t){let e=new tc;if(e.breakAfter=this.breakAfter,0==this.length)return e;let{i,off:s}=this.childPos(t);s&&(e.append(this.children[i].split(s),0),this.children[i].merge(s,this.children[i].length,null,!1,0,0),i++);for(let t=i;t<this.children.length;t++)e.append(this.children[t],0);for(;i>0&&0==this.children[i-1].length;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=void 0===this.prevAttrs?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){tt(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){!function t(e,i,s){let o,{children:n}=e;s>0&&i instanceof _&&n.length&&(o=n[n.length-1])instanceof _&&o.mark.eq(i.mark)?t(o,i.children[0],s-1):(n.push(i),i.setParent(e)),e.length+=i.length}(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=J(e,this.attrs||{})),i&&(this.attrs=J({class:i},this.attrs||{}))}domAtPos(t){return U(this,t)}reuseDOM(t){"DIV"==t.nodeName&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?4&this.flags&&(M(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),void 0!==this.prevAttrs&&(te(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let s=this.dom.lastChild;for(;s&&T.get(s)instanceof _;)s=s.lastChild;if(!s||!this.length||"BR"!=s.nodeName&&(null==(i=T.get(s))?void 0:i.isEditable)==!1&&(!Y.ios||!this.children.some(t=>t instanceof G))){let t=document.createElement("BR");t.cmIgnore=!0,this.dom.appendChild(t)}}measureTextSize(){if(0==this.children.length||this.length>20)return null;let t=0,e;for(let i of this.children){if(!(i instanceof G)||/[^ -~]/.test(i.text))return null;let s=c(i.dom);if(1!=s.length)return null;t+=s[0].width,e=s[0].height}return t?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:t/this.length,textHeight:e}:null}coordsAt(t,e){let i=Q(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:t}=this.parent.view.viewState,e=i.bottom-i.top;if(2>Math.abs(e-t.lineHeight)&&t.textHeight<e){let s=(e-t.textHeight)/2;return{top:i.top+s,bottom:i.bottom-s,left:i.left,right:i.left}}}return i}become(t){return t instanceof tc&&0==this.children.length&&0==t.children.length&&tt(this.attrs,t.attrs)&&this.breakAfter==t.breakAfter}covers(){return!0}static find(t,e){for(let i=0,s=0;i<t.children.length;i++){let o=t.children[i],n=s+o.length;if(n>=e){if(o instanceof tc)return o;if(n>e)break}s=n+o.breakAfter}return null}}class td extends T{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,s,o,n){return(!i||i instanceof td&&!!this.widget.compare(i.widget)&&(!(t>0)||!(o<=0))&&(!(e<this.length)||!(n<=0)))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}domAtPos(t){return 0==t?O.before(this.dom):O.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new td(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return D}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):o.EY.empty}domBoundsAround(){return null}become(t){return t instanceof td&&t.widget.constructor==this.widget.constructor&&(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);return i||(this.widget instanceof tu?null:m(this.dom.getBoundingClientRect(),this.length?0==t:e<=0))}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e!=i&&(t<0?e<0:i>0)}}class tu extends ti{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}class tf{constructor(t,e,i,s){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=s,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(0==this.content.length)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof td&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new tc),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(tp(new $(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,this.posCovered()||t&&this.content.length&&this.content[this.content.length-1]instanceof td||this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:e,lineBreak:i,done:s}=this.cursor.next(this.skip);if(this.skip=0,s)throw Error("Ran out of text content when drawing inline views");if(i){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}this.text=e,this.textOff=0}let s=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(tp(new G(this.text.slice(this.textOff,this.textOff+s)),e),i),this.atCursorPos=!0,this.textOff+=s,t-=s,i=0}}span(t,e,i,s){this.buildText(e-t,i,s),this.pos=e,this.openStart<0&&(this.openStart=s)}point(t,e,i,s,o,n){if(this.disallowBlockEffectsFor[n]&&i instanceof tl){if(i.block)throw RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw RangeError("Decorations that replace line breaks may not be specified via plugins")}let r=e-t;if(i instanceof tl)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new td(i.widget||tg.block,r,i));else{let n=X.create(i.widget||tg.inline,r,r?0:i.startSide),l=this.atCursorPos&&!n.isEditable&&o<=s.length&&(t<e||i.startSide>0),h=!n.isEditable&&(t<e||o>s.length||i.startSide<=0),a=this.getLine();2!=this.pendingBuffer||l||n.isEditable||(this.pendingBuffer=0),this.flushBuffer(s),l&&(a.append(tp(new $(1),s),o),o=s.length+Math.max(0,o-s.length)),a.append(tp(n,s),o),this.atCursorPos=h,this.pendingBuffer=h?t<e||o>s.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=s.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);r&&(this.textOff+r<=this.text.length?this.textOff+=r:(this.skip+=r-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=o)}static build(t,e,i,s,n){let r=new tf(t,e,i,n);return r.openEnd=o.om.spans(s,e,i,r),r.openStart<0&&(r.openStart=r.openEnd),r.finish(r.openEnd),r}}function tp(t,e){for(let i of e)t=new _(i,[t],t.length);return t}class tg extends ti{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}tg.inline=new tg("span"),tg.block=new tg("div");var tm=function(t){return t[t.LTR=0]="LTR",t[t.RTL=1]="RTL",t}(tm||(tm={}));let tw=tm.LTR,tv=tm.RTL;function tb(t){let e=[];for(let i=0;i<t.length;i++)e.push(1<<t[i]);return e}let ty=tb("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),tx=tb("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),tS=Object.create(null),tM=[];for(let t of["()","[]","{}"]){let e=t.charCodeAt(0),i=t.charCodeAt(1);tS[e]=i,tS[i]=-e}function tC(t){return t<=247?ty[t]:1424<=t&&t<=1524?2:1536<=t&&t<=1785?tx[t-1536]:1774<=t&&t<=2220?4:8192<=t&&t<=8204?256:64336<=t&&t<=65023?4:1}let tk=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class tA{get dir(){return this.level%2?tv:tw}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,s){let o=-1;for(let n=0;n<t.length;n++){let r=t[n];if(r.from<=e&&r.to>=e){if(r.level==i)return n;(o<0||(0!=s?s<0?r.from<e:r.to>e:t[o].level>r.level))&&(o=n)}}if(o<0)throw RangeError("Index out of range");return o}}let tO=[];function tD(t){return[new tA(0,t,0)]}let tT="",tE=o.sj.define(),tR=o.sj.define(),tB=o.sj.define(),tL=o.sj.define(),tP=o.sj.define(),tH=o.sj.define(),tN=o.sj.define(),tV=o.sj.define(),tF=o.sj.define(),tW=o.sj.define({combine:t=>t.some(t=>t)}),tz=o.sj.define({combine:t=>t.some(t=>t)}),tK=o.sj.define();class tI{constructor(t,e="nearest",i="nearest",s=5,o=5,n=!1){this.range=t,this.y=e,this.x=i,this.yMargin=s,this.xMargin=o,this.isSnapshot=n}map(t){return t.empty?this:new tI(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new tI(o.OF.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}let tq=o.Pe.define({map:(t,e)=>t.map(e)}),tj=o.Pe.define();function tY(t,e,i){let s=t.facet(tL);s.length?s[0](e):window.onerror&&window.onerror(String(e),i,void 0,void 0,e)||(i?console.error(i+":",e):console.error(e))}let tG=o.sj.define({combine:t=>!t.length||t[0]}),t_=0,tX=o.sj.define({combine:t=>t.filter((e,i)=>{for(let s=0;s<i;s++)if(t[s].plugin==e.plugin)return!1;return!0})});class t${constructor(t,e,i,s,o){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=s,this.baseExtensions=o(this),this.extension=this.baseExtensions.concat(tX.of({plugin:this,arg:void 0}))}of(t){return this.baseExtensions.concat(tX.of({plugin:this,arg:t}))}static define(t,e){let{eventHandlers:i,eventObservers:s,provide:o,decorations:n}=e||{};return new t$(t_++,t,i,s,t=>{let e=[];return n&&e.push(tZ.of(e=>{let i=e.plugin(t);return i?n(i):to.none})),o&&e.push(o(t)),e})}static fromClass(t,e){return t$.define((e,i)=>new t(e,i),e)}}class tU{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}get plugin(){return this.spec&&this.spec.plugin}update(t){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(e){if(tY(t.state,e,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch(t){}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.plugin.create(t,this.spec.arg)}catch(e){tY(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(null==(e=this.value)?void 0:e.destroy)try{this.value.destroy()}catch(e){tY(t.state,e,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}let tQ=o.sj.define(),tJ=o.sj.define(),tZ=o.sj.define(),t0=o.sj.define(),t1=o.sj.define(),t2=o.sj.define();function t8(t,e){let i=t.state.facet(t2);if(!i.length)return i;let s=i.map(e=>e instanceof Function?e(t):e),n=[];return o.om.spans(s,e.from,e.to,{point(){},span(t,i,s,o){let r=t-e.from,l=i-e.from,h=n;for(let t=s.length-1;t>=0;t--,o--){let i=s[t].spec.bidiIsolate,n;if(null==i&&(i=function(t,e,i){for(let s=e;s<i;s++){let e=tC(t.charCodeAt(s));if(1==e)break;if(2==e||4==e)return tv}return tw}(e.text,r,l)),o>0&&h.length&&(n=h[h.length-1]).to==r&&n.direction==i)n.to=l,h=n.inner;else{let t={from:r,to:l,direction:i,inner:[]};h.push(t),h=t.inner}}}}),n}let t3=o.sj.define();function t9(t){let e=0,i=0,s=0,o=0;for(let n of t.state.facet(t3)){let r=n(t);r&&(null!=r.left&&(e=Math.max(e,r.left)),null!=r.right&&(i=Math.max(i,r.right)),null!=r.top&&(s=Math.max(s,r.top)),null!=r.bottom&&(o=Math.max(o,r.bottom)))}return{left:e,right:i,top:s,bottom:o}}let t5=o.sj.define();class t4{constructor(t,e,i,s){this.fromA=t,this.toA=e,this.fromB=i,this.toB=s}join(t){return new t4(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let s=t[e-1];if(!(s.fromA>i.toA)){if(s.toA<i.fromA)break;i=i.join(s),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(0==e.length)return t;let i=[];for(let s=0,o=0,n=0,r=0;;s++){let l=s==t.length?null:t[s],h=n-r,a=l?l.fromB:1e9;for(;o<e.length&&e[o]<a;){let t=e[o],s=e[o+1],n=Math.max(r,t),l=Math.min(a,s);if(n<=l&&new t4(n+h,l+h,n,l).addToSet(i),s>a)break;o+=2}if(!l)return i;new t4(l.fromA,l.toA,l.fromB,l.toB).addToSet(i),n=l.toA,r=l.toB}}}class t6{constructor(t,e,i){for(let s of(this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=o.VR.empty(this.startState.doc.length),i))this.changes=this.changes.compose(s.changes);let s=[];this.changes.iterChangedRanges((t,e,i,o)=>s.push(new t4(t,e,i,o))),this.changedRanges=s}static create(t,e,i){return new t6(t,e,i)}get viewportChanged(){return(4&this.flags)>0}get viewportMoved(){return(8&this.flags)>0}get heightChanged(){return(2&this.flags)>0}get geometryChanged(){return this.docChanged||(18&this.flags)>0}get focusChanged(){return(1&this.flags)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(t=>t.selection)}get empty(){return 0==this.flags&&0==this.transactions.length}}class t7 extends T{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[!1],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.editContextFormatting=to.none,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new tc],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new t4(0,0,0,t.state.doc.length)],0,null)}update(t){var e,i,s,n,r,l;let h,a,c=t.changedRanges;this.minWidth>0&&c.length&&(c.every(({fromA:t,toA:e})=>e<this.minWidthFrom||t>this.minWidthTo)?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.updateEditContextFormatting(t);let d=-1;this.view.inputState.composing>=0&&!this.view.observer.editContext&&((null==(e=this.domChanged)?void 0:e.newSel)?d=this.domChanged.newSel.head:(i=t.changes,s=this.hasComposition,h=!1,s&&i.iterChangedRanges((t,e)=>{t<s.to&&e>s.from&&(h=!0)}),h||t.selectionSet||(d=t.state.selection.main.head)));let u=d>-1?function(t,e,i){let s=et(t,i);if(!s)return null;let{node:o,from:n,to:r}=s,l=o.nodeValue;if(/[\n\r]/.test(l)||t.state.doc.sliceString(s.from,s.to)!=l)return null;let h=e.invertedDesc,a=new t4(h.mapPos(n),h.mapPos(r),n,r),c=[];for(let e=o.parentNode;;e=e.parentNode){let i=T.get(e);if(i instanceof _)c.push({node:e,deco:i.mark});else{if(i instanceof tc||"DIV"==e.nodeName&&e.parentNode==t.contentDOM)return{range:a,text:o,marks:c,line:e};if(e==t.contentDOM)return null;c.push({node:e,deco:new tn({inclusive:!0,attributes:function(t){let e=Object.create(null);for(let i=0;i<t.attributes.length;i++){let s=t.attributes[i];e[s.name]=s.value}return e}(e),tagName:e.tagName.toLowerCase()})})}}}(this.view,t.changes,d):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:e,to:i}=this.hasComposition;c=new t4(e,i,t.changes.mapPos(e,-1),t.changes.mapPos(i,1)).addToSet(c.slice())}this.hasComposition=u?{from:u.range.fromB,to:u.range.toB}:null,(Y.ie||Y.chrome)&&!u&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let f=(n=this.decorations,r=this.updateDeco(),l=t.changes,a=new ee,o.om.compare(n,r,l,a),a.changes);return c=t4.extendWithRanges(c,f),(!!(7&this.flags)||0!=c.length)&&(this.updateInner(c,t.startState.doc.length,u),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:s}=this.view;s.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let t=Y.chrome||Y.ios?{node:s.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,t),this.flags&=-8,t&&(t.written||s.selectionRange.focusNode!=t.node)&&(this.forceSelection=!0),this.dom.style.height=""}),this.markedForComposition.forEach(t=>t.flags&=-9);let o=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let t of this.children)t instanceof td&&t.widget instanceof tu&&o.push(t.dom);s.updateGaps(o)}updateChildren(t,e,i){let s=i?i.range.addToSet(t.slice()):t,o=this.childCursor(e);for(let t=s.length-1;;t--){let e=t>=0?s[t]:null;if(!e)break;let{fromA:n,toA:r,fromB:l,toB:h}=e,a,c,d,u;if(i&&i.range.fromB<h&&i.range.toB>l){let t=tf.build(this.view.state.doc,l,i.range.fromB,this.decorations,this.dynamicDecorationMap),e=tf.build(this.view.state.doc,i.range.toB,h,this.decorations,this.dynamicDecorationMap);c=t.breakAtStart,d=t.openStart,u=e.openEnd;let s=this.compositionView(i);e.breakAtStart?s.breakAfter=1:e.content.length&&s.merge(s.length,s.length,e.content[0],!1,e.openStart,0)&&(s.breakAfter=e.content[0].breakAfter,e.content.shift()),t.content.length&&s.merge(0,0,t.content[t.content.length-1],!0,0,t.openEnd)&&t.content.pop(),a=t.content.concat(s).concat(e.content)}else({content:a,breakAtStart:c,openStart:d,openEnd:u}=tf.build(this.view.state.doc,l,h,this.decorations,this.dynamicDecorationMap));let{i:f,off:p}=o.findPos(r,1),{i:g,off:m}=o.findPos(n,-1);B(this,g,m,f,p,a,c,d,u)}i&&this.fixCompositionDOM(i)}updateEditContextFormatting(t){for(let e of(this.editContextFormatting=this.editContextFormatting.map(t.changes),t.transactions))for(let t of e.effects)t.is(tj)&&(this.editContextFormatting=t.value)}compositionView(t){let e=new G(t.text.nodeValue);for(let{deco:i}of(e.flags|=8,t.marks))e=new _(i,[e],e.length);let i=new tc;return i.append(e,0),i}fixCompositionDOM(t){let e=(t,e)=>{e.flags|=8|!!e.children.some(t=>7&t.flags),this.markedForComposition.add(e);let i=T.get(t);i&&i!=e&&(i.dom=null),e.setDOM(t)},i=this.childPos(t.range.fromB,1),s=this.children[i.i];e(t.line,s);for(let o=t.marks.length-1;o>=-1;o--)i=s.childPos(i.off,1),s=s.children[i.i],e(o>=0?t.marks[o].node:t.text,s)}updateSelection(t=!1,e=!1){var i;(t||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange();let s=this.view.root.activeElement,o=s==this.dom,n=!o&&!(this.view.state.facet(tG)||this.dom.tabIndex>-1)&&a(this.dom,this.view.observer.selectionRange)&&!(s&&this.dom.contains(s));if(!(o||e||n))return;let r=this.forceSelection;this.forceSelection=!1;let h=this.view.state.selection.main,c=this.moveToLine(this.domAtPos(h.anchor)),u=h.empty?c:this.moveToLine(this.domAtPos(h.head));if(Y.gecko&&h.empty&&!this.hasComposition&&1==(i=c).node.nodeType&&i.node.firstChild&&(0==i.offset||"false"==i.node.childNodes[i.offset-1].contentEditable)&&(i.offset==i.node.childNodes.length||"false"==i.node.childNodes[i.offset].contentEditable)){let t=document.createTextNode("");this.view.observer.ignore(()=>c.node.insertBefore(t,c.node.childNodes[c.offset]||null)),c=u=new O(t,0),r=!0}let f=this.view.observer.selectionRange;!r&&f.focusNode&&(d(c.node,c.offset,f.anchorNode,f.anchorOffset)&&d(u.node,u.offset,f.focusNode,f.focusOffset)||this.suppressWidgetCursorChange(f,h))||(this.view.observer.ignore(()=>{Y.android&&Y.chrome&&this.dom.contains(f.focusNode)&&function(t,e){for(let i=t;i&&i!=e;i=i.assignedSlot||i.parentNode)if(1==i.nodeType&&"false"==i.contentEditable)return!0;return!1}(f.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let t=l(this.view.root);if(t)if(h.empty){if(Y.gecko){var e,i;let t=(e=c.node,i=c.offset,1!=e.nodeType?0:(i&&"false"==e.childNodes[i-1].contentEditable?1:0)|2*(i<e.childNodes.length&&"false"==e.childNodes[i].contentEditable));if(t&&3!=t){let e=(1==t?k:A)(c.node,c.offset);e&&(c=new O(e.node,e.offset))}}t.collapse(c.node,c.offset),null!=h.bidiLevel&&void 0!==t.caretBidiLevel&&(t.caretBidiLevel=h.bidiLevel)}else if(t.extend){t.collapse(c.node,c.offset);try{t.extend(u.node,u.offset)}catch(t){}}else{let e=document.createRange();h.anchor>h.head&&([c,u]=[u,c]),e.setEnd(u.node,u.offset),e.setStart(c.node,c.offset),t.removeAllRanges(),t.addRange(e)}n&&this.view.root.activeElement==this.dom&&(this.dom.blur(),s&&s.focus())}),this.view.observer.setSelectionRange(c,u)),this.impreciseAnchor=c.precise?null:new O(f.anchorNode,f.anchorOffset),this.impreciseHead=u.precise?null:new O(f.focusNode,f.focusOffset)}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&d(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=l(t.root),{anchorNode:s,anchorOffset:o}=t.observer.selectionRange;if(!i||!e.empty||!e.assoc||!i.modify)return;let n=tc.find(this,e.head);if(!n)return;let r=n.posAtStart;if(e.head==r||e.head==r+n.length)return;let h=this.coordsAt(e.head,-1),a=this.coordsAt(e.head,1);if(!h||!a||h.bottom>a.top)return;let c=this.domAtPos(e.head+e.assoc);i.collapse(c.node,c.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let d=t.observer.selectionRange;t.docView.posFromDOM(d.anchorNode,d.anchorOffset)!=e.from&&i.collapse(s,o)}moveToLine(t){let e=this.dom,i;if(t.node!=e)return t;for(let s=t.offset;!i&&s<e.childNodes.length;s++){let t=T.get(e.childNodes[s]);t instanceof tc&&(i=t.domAtPos(0))}for(let s=t.offset-1;!i&&s>=0;s--){let t=T.get(e.childNodes[s]);t instanceof tc&&(i=t.domAtPos(t.length))}return i?new O(i.node,i.offset,!0):t}nearest(t){for(let e=t;e;){let t=T.get(e);if(t&&t.rootView==this)return t;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let t=this.children[e];if(i<t.length||t instanceof tc)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,s=0;for(let o=this.length,n=this.children.length-1;n>=0;n--){let r=this.children[n],l=o-r.breakAfter,h=l-r.length;if(l<t)break;if(h<=t&&(h<t||r.covers(-1))&&(l>t||r.covers(1))&&(!i||r instanceof tc&&!(i instanceof tc&&e>=0)))i=r,s=h;else if(i&&h==t&&l==t&&r instanceof td&&2>Math.abs(e))if(r.deco.startSide<0)break;else n&&(i=null);o=h}return i?i.coordsAt(t-s,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),s=this.children[e];if(!(s instanceof tc))return null;for(;s.children.length;){let{i:t,off:e}=s.childPos(i,1);for(;;t++){if(t==s.children.length)return null;if((s=s.children[t]).length)break}i=e}if(!(s instanceof G))return null;let n=(0,o.zK)(s.text,i);if(n==i)return null;let r=x(s.dom,i,n).getClientRects();for(let t=0;t<r.length;t++){let e=r[t];if(t==r.length-1||e.top<e.bottom&&e.left<e.right)return e}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:s}=t,o=this.view.contentDOM.clientWidth,n=o>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,r=-1,l=this.view.textDirection==tm.LTR;for(let t=0,h=0;h<this.children.length;h++){let a=this.children[h],d=t+a.length;if(d>s)break;if(t>=i){let i=a.dom.getBoundingClientRect();if(e.push(i.height),n){let e=a.dom.lastChild,s=e?c(e):[];if(s.length){let e=s[s.length-1],n=l?e.right-i.left:i.right-e.left;n>r&&(r=n,this.minWidth=o,this.minWidthFrom=t,this.minWidthTo=d)}}}t=d+a.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return"rtl"==getComputedStyle(this.children[e].dom).direction?tm.RTL:tm.LTR}measureTextSize(){for(let t of this.children)if(t instanceof tc){let e=t.measureTextSize();if(e)return e}let t=document.createElement("div"),e,i,s;return t.className="cm-line",t.style.width="99999px",t.style.position="absolute",t.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(t);let o=c(t.firstChild)[0];e=t.getBoundingClientRect().height,i=o?o.width/27:7,s=o?o.height:e,t.remove()}),{lineHeight:e,charWidth:i,textHeight:s}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new R(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,s=0;;s++){let o=s==e.viewports.length?null:e.viewports[s],n=o?o.from-1:this.length;if(n>i){let s=(e.lineBlockAt(n).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(to.replace({widget:new tu(s),block:!0,inclusive:!0,isBlockGap:!0}).range(i,n))}if(!o)break;i=o.to+1}return to.set(t)}updateDeco(){let t=1,e=this.view.state.facet(tZ).map(e=>(this.dynamicDecorationMap[t++]="function"==typeof e)?e(this.view):e),i=!1,s=this.view.state.facet(t0).map((t,e)=>{let s="function"==typeof t;return s&&(i=!0),s?t(this.view):t});for(s.length&&(this.dynamicDecorationMap[t++]=i,e.push(o.om.join(s))),this.decorations=[this.editContextFormatting,...e,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];t<this.decorations.length;)this.dynamicDecorationMap[t++]=!1;return this.decorations}scrollIntoView(t){if(t.isSnapshot){let e=this.view.viewState.lineBlockAt(t.range.head);this.view.scrollDOM.scrollTop=e.top-t.yMargin,this.view.scrollDOM.scrollLeft=t.xMargin;return}for(let e of this.view.state.facet(tK))try{if(e(this.view,t.range,t))return!0}catch(t){tY(this.view.state,t,"scroll handler")}let{range:e}=t,i=this.coordsAt(e.head,e.empty?e.assoc:e.head>e.anchor?-1:1),s;if(!i)return;!e.empty&&(s=this.coordsAt(e.anchor,e.anchor>e.head?-1:1))&&(i={left:Math.min(i.left,s.left),top:Math.min(i.top,s.top),right:Math.max(i.right,s.right),bottom:Math.max(i.bottom,s.bottom)});let o=t9(this.view),n={left:i.left-o.left,top:i.top-o.top,right:i.right+o.right,bottom:i.bottom+o.bottom},{offsetWidth:r,offsetHeight:l}=this.view.scrollDOM;!function(t,e,i,s,o,n,r,l){let h=t.ownerDocument,a=h.defaultView||window;for(let c=t,d=!1;c&&!d;)if(1==c.nodeType){let t,u=c==h.body,f=1,p=1;if(u)t=function(t){let e=t.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight}}(a);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(d=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let e=c.getBoundingClientRect();({scaleX:f,scaleY:p}=w(c,e)),t={left:e.left,right:e.left+c.clientWidth*f,top:e.top,bottom:e.top+c.clientHeight*p}}let g=0,m=0;if("nearest"==o)e.top<t.top?(m=e.top-(t.top+r),i>0&&e.bottom>t.bottom+m&&(m=e.bottom-t.bottom+r)):e.bottom>t.bottom&&(m=e.bottom-t.bottom+r,i<0&&e.top-m<t.top&&(m=e.top-(t.top+r)));else{let s=e.bottom-e.top,n=t.bottom-t.top;m=("center"==o&&s<=n?e.top+s/2-n/2:"start"==o||"center"==o&&i<0?e.top-r:e.bottom-n+r)-t.top}if("nearest"==s?e.left<t.left?(g=e.left-(t.left+n),i>0&&e.right>t.right+g&&(g=e.right-t.right+n)):e.right>t.right&&(g=e.right-t.right+n,i<0&&e.left<t.left+g&&(g=e.left-(t.left+n))):g=("center"==s?e.left+(e.right-e.left)/2-(t.right-t.left)/2:"start"==s==l?e.left-n:e.right-(t.right-t.left)+n)-t.left,g||m)if(u)a.scrollBy(g,m);else{let t=0,i=0;if(m){let t=c.scrollTop;c.scrollTop+=m/p,i=(c.scrollTop-t)*p}if(g){let e=c.scrollLeft;c.scrollLeft+=g/f,t=(c.scrollLeft-e)*f}e={left:e.left-t,top:e.top-i,right:e.right-t,bottom:e.bottom-i},t&&1>Math.abs(t-g)&&(s="nearest"),i&&1>Math.abs(i-m)&&(o="nearest")}if(u)break;(e.top<t.top||e.bottom>t.bottom||e.left<t.left||e.right>t.right)&&(e={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)}),c=c.assignedSlot||c.parentNode}else if(11==c.nodeType)c=c.host;else break}(this.view.scrollDOM,n,e.head<e.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,r),-r),Math.max(Math.min(t.yMargin,l),-l),this.view.textDirection==tm.LTR)}}function et(t,e){let i=t.observer.selectionRange;if(!i.focusNode)return null;let s=k(i.focusNode,i.focusOffset),o=A(i.focusNode,i.focusOffset),n=s||o;if(o&&s&&o.node!=s.node){let e=T.get(o.node);if(!e||e instanceof G&&e.text!=o.node.nodeValue)n=o;else if(t.docView.lastCompositionAfterCursor){let t=T.get(s.node);!t||t instanceof G&&t.text!=s.node.nodeValue||(n=o)}}if(t.docView.lastCompositionAfterCursor=n!=s,!n)return null;let r=e-n.offset;return{from:r,to:r+n.node.nodeValue.length,node:n.node}}let ee=class{constructor(){this.changes=[]}compareRange(t,e){ta(t,e,this.changes)}comparePoint(t,e){ta(t,e,this.changes)}boundChange(t){ta(t,t,this.changes)}};function ei(t,e){return t.top<e.bottom-1&&t.bottom>e.top+1}function es(t,e){return e<t.top?{top:e,left:t.left,right:t.right,bottom:t.bottom}:t}function eo(t,e){return e>t.bottom?{top:t.top,left:t.left,right:t.right,bottom:e}:t}function en(t,e,i){let s=t.nodeValue.length,o=-1,n=1e9,r=0;for(let l=0;l<s;l++){let s=x(t,l,l+1).getClientRects();for(let h=0;h<s.length;h++){let a=s[h];if(a.top==a.bottom)continue;r||(r=e-a.left);let c=(a.top>i?a.top-i:i-a.bottom)-1;if(a.left-1<=e&&a.right+1>=e&&c<n){let i=e>=(a.left+a.right)/2,s=i;if((Y.chrome||Y.gecko)&&x(t,l).getBoundingClientRect().left==a.right&&(s=!i),c<=0)return{node:t,offset:l+ +!!s};o=l+ +!!s,n=c}}}return{node:t,offset:o>-1?o:r>0?t.nodeValue.length:0}}function er(t,e,i,s=-1){var o,n;let r=t.contentDOM.getBoundingClientRect(),l=r.top+t.viewState.paddingTop,h,{docHeight:a}=t.viewState,{x:d,y:u}=e,f=u-l;if(f<0)return 0;if(f>a)return t.state.doc.length;for(let e=t.viewState.heightOracle.textHeight/2,o=!1;(h=t.elementAtHeight(f)).type!=ts.Text;)for(;!((f=s>0?h.bottom+e:h.top-e)>=0)||!(f<=a);){if(o)return i?null:0;o=!0,s=-s}u=l+f;let p=h.from;if(p<t.viewport.from)return 0==t.viewport.from?0:i?null:el(t,r,h,d,u);if(p>t.viewport.to)return t.viewport.to==t.state.doc.length?t.state.doc.length:i?null:el(t,r,h,d,u);let m=t.dom.ownerDocument,w=t.root.elementFromPoint?t.root:m,v=w.elementFromPoint(d,u);v&&!t.contentDOM.contains(v)&&(v=null),!v&&(d=Math.max(r.left+1,Math.min(r.right-1,d)),(v=w.elementFromPoint(d,u))&&!t.contentDOM.contains(v)&&(v=null));let b,y=-1;if(v&&(null==(o=t.docView.nearest(v))?void 0:o.isEditable)!=!1){if(m.caretPositionFromPoint){let t=m.caretPositionFromPoint(d,u);t&&({offsetNode:b,offset:y}=t)}else if(m.caretRangeFromPoint){let e=m.caretRangeFromPoint(d,u);e&&({startContainer:b,startOffset:y}=e,(!t.contentDOM.contains(b)||Y.safari&&function(t,e,i){let s;if(3!=t.nodeType||e!=(s=t.nodeValue.length))return!1;for(let e=t.nextSibling;e;e=e.nextSibling)if(1!=e.nodeType||"BR"!=e.nodeName)return!1;return x(t,s-1,s).getBoundingClientRect().left>i}(b,y,d)||Y.chrome&&function(t,e,i){if(0!=e)return!1;for(let e=t;;){let t=e.parentNode;if(!t||1!=t.nodeType||t.firstChild!=e)return!1;if(t.classList.contains("cm-line"))break;e=t}return i-(1==t.nodeType?t.getBoundingClientRect():x(t,0,Math.max(t.nodeValue.length,1)).getBoundingClientRect()).left>5}(b,y,d))&&(b=void 0))}b&&(y=Math.min(g(b),y))}if(!b||!t.docView.dom.contains(b)){let e=tc.find(t.docView,p);if(!e)return f>h.top+h.height/2?h.to:h.from;({node:b,offset:y}=function t(e,i,s){let o,n,r,l,h,a,d,u,f=!1;for(let m=e.firstChild;m;m=m.nextSibling){let e=c(m);for(let c=0;c<e.length;c++){var p,g;let w=e[c];a&&ei(a,w)&&(w=es(eo(w,a.bottom),a.top));let v=(p=w).left>i?p.left-i:Math.max(0,i-p.right),b=(g=w).top>s?g.top-s:Math.max(0,s-g.bottom);if(0==v&&0==b)return 3==m.nodeType?en(m,i,s):t(m,i,s);if(!h||u>b||u==b&&d>v){h=m,a=w,d=v,u=b;let t=b?s<w.top?-1:1:v?i<w.left?-1:1:0;f=!t||(t>0?c<e.length-1:c>0)}0==v?s>w.bottom&&(!r||r.bottom<w.bottom)?(o=m,r=w):s<w.top&&(!l||l.top>w.top)&&(n=m,l=w):r&&ei(r,w)?r=eo(r,w.bottom):l&&ei(l,w)&&(l=es(l,w.top))}}if(r&&r.bottom>=s?(h=o,a=r):l&&l.top<=s&&(h=n,a=l),!h)return{node:e,offset:0};let m=Math.max(a.left,Math.min(a.right,i));if(3==h.nodeType)return en(h,m,s);if(f&&"false"!=h.contentEditable)return t(h,m,s);let w=Array.prototype.indexOf.call(e.childNodes,h)+ +(i>=(a.left+a.right)/2);return{node:e,offset:w}}(e.dom,d,u))}let S=t.docView.nearest(b);if(!S)return null;if(!S.isWidget||(null==(n=S.dom)?void 0:n.nodeType)!=1)return S.localPosFromDOM(b,y)+S.posAtStart;{let t=S.dom.getBoundingClientRect();return e.y<t.top||e.y<=t.bottom&&e.x<=(t.left+t.right)/2?S.posAtStart:S.posAtEnd}}function el(t,e,i,s,n){let r=Math.round((s-e.left)*t.defaultCharacterWidth);if(t.lineWrapping&&i.height>1.5*t.defaultLineHeight){let e=t.viewState.heightOracle.textHeight;r+=Math.floor((n-i.top-(t.defaultLineHeight-e)*.5)/e)*t.viewState.heightOracle.lineLength}let l=t.state.sliceDoc(i.from,i.to);return i.from+(0,o.kn)(l,r,t.state.tabSize)}function eh(t,e,i){let s=t.lineBlockAt(e);if(Array.isArray(s.type)){let t;for(let o of s.type){if(o.from>e)break;if(!(o.to<e)){if(o.from<e&&o.to>e)return o;(!t||o.type==ts.Text&&(t.type!=o.type||(i<0?o.from<e:o.to>e)))&&(t=o)}}return t||s}return s}function ea(t,e,i,s){let n=t.state.doc.lineAt(e.head),r=t.bidiSpans(n),l=t.textDirectionAt(n.from);for(let h=e,a=null;;){let e=function(t,e,i,s,n){var r;let l=s.head-t.from,h=tA.find(e,l,null!=(r=s.bidiLevel)?r:-1,s.assoc),a=e[h],c=a.side(n,i);if(l==c){let t=h+=n?1:-1;if(t<0||t>=e.length)return null;l=(a=e[h=t]).side(!n,i),c=a.side(n,i)}let d=(0,o.zK)(t.text,l,a.forward(n,i));(d<a.from||d>a.to)&&(d=c),tT=t.text.slice(Math.min(l,d),Math.max(l,d));let u=h==(n?e.length-1:0)?null:e[h+(n?1:-1)];return u&&d==c&&u.level+ +!n<a.level?o.OF.cursor(u.side(!n,i)+t.from,u.forward(n,i)?1:-1,u.level):o.OF.cursor(d+t.from,a.forward(n,i)?-1:1,a.level)}(n,r,l,h,i),c=tT;if(!e){if(n.number==(i?t.state.doc.lines:1))return h;c="\n",n=t.state.doc.line(n.number+(i?1:-1)),r=t.bidiSpans(n),e=t.visualLineSide(n,!i)}if(a){if(!a(c))return h}else{if(!s)return e;a=s(c)}h=e}}function ec(t,e,i){for(;;){let s=0;for(let o of t)o.between(e-1,e+1,(t,o,n)=>{if(e>t&&e<o){let n=s||i||(e-t<o-e?-1:1);e=n<0?t:o,s=n}});if(!s)return e}}function ed(t,e,i){let s=ec(t.state.facet(t1).map(e=>e(t)),i.from,e.head>i.from?-1:1);return s==i.from?i:o.OF.cursor(s,s<i.from?1:-1)}class eu{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(o.$t.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+="￿"}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let s=t;;){this.findPointBefore(i,s);let t=this.text.length;this.readNode(s);let o=s.nextSibling;if(o==e)break;let n=T.get(s),r=T.get(o);(n&&r?n.breakAfter:(n?n.breakAfter:f(s))||f(o)&&("BR"!=s.nodeName||s.cmIgnore)&&this.text.length>t)&&this.lineBreak(),s=o}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,s=this.lineSeparator?null:/\r\n?|\n/g;;){let o=-1,n=1,r;if(this.lineSeparator?(o=e.indexOf(this.lineSeparator,i),n=this.lineSeparator.length):(r=s.exec(e))&&(o=r.index,n=r[0].length),this.append(e.slice(i,o<0?e.length:o)),o<0)break;if(this.lineBreak(),n>1)for(let e of this.points)e.node==t&&e.pos>this.text.length&&(e.pos-=n-1);i=o+n}}readNode(t){if(t.cmIgnore)return;let e=T.get(t),i=e&&e.overrideDOMText;if(null!=i){this.findPointInside(t,i.length);for(let t=i.iter();!t.next().done;)t.lineBreak?this.lineBreak():this.append(t.value)}else 3==t.nodeType?this.readTextNode(t):"BR"==t.nodeName?t.nextSibling&&this.lineBreak():1==t.nodeType&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(3==t.nodeType?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+(!function(t,e,i){for(;;){if(!e||i<g(e))return!1;if(e==t)return!0;i=u(e)+1,e=e.parentNode}}(t,i.node,i.offset)?0:e))}}class ef{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class ep{constructor(t,e,i,s){this.typeOver=s,this.bounds=null,this.text="",this.domChanged=e>-1;let{impreciseHead:n,impreciseAnchor:r}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let e=n||r?[]:function(t){let e=[];if(t.root.activeElement!=t.contentDOM)return e;let{anchorNode:i,anchorOffset:s,focusNode:o,focusOffset:n}=t.observer.selectionRange;return i&&(e.push(new ef(i,s)),(o!=i||n!=s)&&e.push(new ef(o,n))),e}(t),i=new eu(e,t.state);i.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=i.text,this.newSel=function(t,e){if(0==t.length)return null;let i=t[0].pos,s=2==t.length?t[1].pos:i;return i>-1&&s>-1?o.OF.single(i+e,s+e):null}(e,this.bounds.from)}else{let e=t.observer.selectionRange,i=n&&n.node==e.focusNode&&n.offset==e.focusOffset||!h(t.contentDOM,e.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(e.focusNode,e.focusOffset),s=r&&r.node==e.anchorNode&&r.offset==e.anchorOffset||!h(t.contentDOM,e.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(e.anchorNode,e.anchorOffset),l=t.viewport;if((Y.ios||Y.chrome)&&t.state.selection.main.empty&&i!=s&&(l.from>0||l.to<t.state.doc.length)){let e=Math.min(i,s),o=Math.max(i,s),n=l.from-e,r=l.to-o;(0==n||1==n||0==e)&&(0==r||-1==r||o==t.state.doc.length)&&(i=0,s=t.state.doc.length)}this.newSel=o.OF.single(s,i)}}}function eg(t,e){let i,{newSel:s}=e,n=t.state.selection.main,r=t.inputState.lastKeyTime>Date.now()-100?t.inputState.lastKeyCode:-1;if(e.bounds){let{from:s,to:l}=e.bounds,h=n.from,a=null;(8===r||Y.android&&e.text.length<l-s)&&(h=n.to,a="end");let c=function(t,e,i,s){let o=Math.min(t.length,e.length),n=0;for(;n<o&&t.charCodeAt(n)==e.charCodeAt(n);)n++;if(n==o&&t.length==e.length)return null;let r=t.length,l=e.length;for(;r>0&&l>0&&t.charCodeAt(r-1)==e.charCodeAt(l-1);)r--,l--;if("end"==s){let t=Math.max(0,n-Math.min(r,l));i-=r+t-n}if(r<n&&t.length<e.length){let t=i<=n&&i>=r?n-i:0;n-=t,l=n+(l-r),r=n}else if(l<n){let t=i<=n&&i>=l?n-i:0;n-=t,r=n+(r-l),l=n}return{from:n,toA:r,toB:l}}(t.state.doc.sliceString(s,l,"￿"),e.text,h-s,a);c&&(Y.chrome&&13==r&&c.toB==c.from+2&&"￿￿"==e.text.slice(c.from,c.toB)&&c.toB--,i={from:s+c.from,to:s+c.toA,insert:o.EY.of(e.text.slice(c.from,c.toB).split("￿"))})}else s&&(!t.hasFocus&&t.state.facet(tG)||s.main.eq(n))&&(s=null);if(!i&&!s)return!1;if(!i&&e.typeOver&&!n.empty&&s&&s.main.empty?i={from:n.from,to:n.to,insert:t.state.doc.slice(n.from,n.to)}:(Y.mac||Y.android)&&i&&i.from==i.to&&i.from==n.head-1&&/^\. ?$/.test(i.insert.toString())&&"off"==t.contentDOM.getAttribute("autocorrect")?(s&&2==i.insert.length&&(s=o.OF.single(s.main.anchor-1,s.main.head-1)),i={from:i.from,to:i.to,insert:o.EY.of([i.insert.toString().replace("."," ")])}):i&&i.from>=n.from&&i.to<=n.to&&(i.from!=n.from||i.to!=n.to)&&n.to-n.from-(i.to-i.from)<=4?i={from:n.from,to:n.to,insert:t.state.doc.slice(n.from,i.from).append(i.insert).append(t.state.doc.slice(i.to,n.to))}:Y.chrome&&i&&i.from==i.to&&i.from==n.head&&"\n "==i.insert.toString()&&t.lineWrapping&&(s&&(s=o.OF.single(s.main.anchor-1,s.main.head-1)),i={from:n.from,to:n.to,insert:o.EY.of([" "])}),i)return em(t,i,s,r);if(!s||s.main.eq(n))return!1;{let e=!1,i="select";return t.inputState.lastSelectionTime>Date.now()-50&&("select"==t.inputState.lastSelectionOrigin&&(e=!0),i=t.inputState.lastSelectionOrigin),t.dispatch({selection:s,scrollIntoView:e,userEvent:i}),!0}}function em(t,e,i,s=-1){let n;if(Y.ios&&t.inputState.flushIOSKey(e))return!0;let r=t.state.selection.main;if(Y.android&&(e.to==r.to&&(e.from==r.from||e.from==r.from-1&&" "==t.state.sliceDoc(e.from,r.from))&&1==e.insert.length&&2==e.insert.lines&&S(t.contentDOM,"Enter",13)||(e.from==r.from-1&&e.to==r.to&&0==e.insert.length||8==s&&e.insert.length<e.to-e.from&&e.to>r.head)&&S(t.contentDOM,"Backspace",8)||e.from==r.from&&e.to==r.to+1&&0==e.insert.length&&S(t.contentDOM,"Delete",46)))return!0;let l=e.insert.toString();t.inputState.composing>=0&&t.inputState.composing++;let h=()=>n||(n=function(t,e,i){let s,n=t.state,r=n.selection.main;if(e.from>=r.from&&e.to<=r.to&&e.to-e.from>=(r.to-r.from)/3&&(!i||i.main.empty&&i.main.from==e.from+e.insert.length)&&t.inputState.composing<0){let i=r.from<e.from?n.sliceDoc(r.from,e.from):"",o=r.to>e.to?n.sliceDoc(e.to,r.to):"";s=n.replaceSelection(t.state.toText(i+e.insert.sliceString(0,void 0,t.state.lineBreak)+o))}else{let l=n.changes(e),h=i&&i.main.to<=l.newLength?i.main:void 0;if(n.selection.ranges.length>1&&t.inputState.composing>=0&&e.to<=r.to&&e.to>=r.to-10){let a=t.state.sliceDoc(e.from,e.to),c,d=i&&et(t,i.main.head);if(d){let t=e.insert.length-(e.to-e.from);c={from:d.from,to:d.to-t}}else c=t.state.doc.lineAt(r.head);let u=r.to-e.to,f=r.to-r.from;s=n.changeByRange(i=>{if(i.from==r.from&&i.to==r.to)return{changes:l,range:h||i.map(l)};let s=i.to-u,d=s-a.length;if(i.to-i.from!=f||t.state.sliceDoc(d,s)!=a||i.to>=c.from&&i.from<=c.to)return{range:i};let p=n.changes({from:d,to:s,insert:e.insert}),g=i.to-r.to;return{changes:p,range:h?o.OF.range(Math.max(0,h.anchor+g),Math.max(0,h.head+g)):i.map(p)}})}else s={changes:l,selection:h&&n.selection.replaceRange(h)}}let l="input.type";return(t.composing||t.inputState.compositionPendingChange&&t.inputState.compositionEndedAt>Date.now()-50)&&(t.inputState.compositionPendingChange=!1,l+=".compose",t.inputState.compositionFirstChange&&(l+=".start",t.inputState.compositionFirstChange=!1)),n.update(s,{userEvent:l,scrollIntoView:!0})}(t,e,i));return t.state.facet(tH).some(i=>i(t,e.from,e.to,l,h))||t.dispatch(h()),!0}class ew{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.tabFocusMode=-1,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,Y.safari&&t.contentDOM.addEventListener("input",()=>null),Y.gecko&&function(t){eI.has(t)||(eI.add(t),t.addEventListener("copy",()=>{}),t.addEventListener("cut",()=>{}))}(t.contentDOM.ownerDocument)}handleEvent(t){!(!function(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let i=e.target,s;i!=t.contentDOM;i=i.parentNode)if(!i||11==i.nodeType||(s=T.get(i))&&s.ignoreEvent(e))return!1;return!0}(this.view,t)||this.ignoreDuringComposition(t))&&("keydown"==t.type&&this.keydown(t)||(0!=this.view.updateState?Promise.resolve().then(()=>this.runHandlers(t.type,t)):this.runHandlers(t.type,t)))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let t of i.observers)t(this.view,e);for(let t of i.handlers){if(e.defaultPrevented)break;if(t(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=function(t){let e=Object.create(null);function i(t){return e[t]||(e[t]={observers:[],handlers:[]})}for(let e of t){let t=e.spec,s=t&&t.plugin.domEventHandlers,o=t&&t.plugin.domEventObservers;if(s)for(let t in s){let o=s[t];o&&i(t).handlers.push(ev(e.value,o))}if(o)for(let t in o){let s=o[t];s&&i(t).observers.push(ev(e.value,s))}}for(let t in eC)i(t).handlers.push(eC[t]);for(let t in ek)i(t).observers.push(ek[t]);return e}(t),i=this.handlers,s=this.view.contentDOM;for(let t in e)if("scroll"!=t){let o=!e[t].handlers.length,n=i[t];n&&!n.handlers.length!=o&&(s.removeEventListener(t,this.handleEvent),n=null),n||s.addEventListener(t,this.handleEvent,{passive:o})}for(let t in i)"scroll"==t||e[t]||s.removeEventListener(t,this.handleEvent);this.handlers=e}keydown(t){let e;return this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),!!(9==t.keyCode&&this.tabFocusMode>-1&&(!this.tabFocusMode||Date.now()<=this.tabFocusMode))||((this.tabFocusMode>0&&27!=t.keyCode&&0>ex.indexOf(t.keyCode)&&(this.tabFocusMode=-1),Y.android&&Y.chrome&&!t.synthetic&&(13==t.keyCode||8==t.keyCode))?(this.view.observer.delayAndroidKey(t.key,t.keyCode),!0):Y.ios&&!t.synthetic&&!t.altKey&&!t.metaKey&&((e=eb.find(e=>e.keyCode==t.keyCode))&&!t.ctrlKey||ey.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(this.pendingIOSKey=e||t,setTimeout(()=>this.flushIOSKey(),250),!0):(229!=t.keyCode&&this.view.observer.forceFlush(),!1))}flushIOSKey(t){let e=this.pendingIOSKey;return!(!e||"Enter"==e.key&&t&&t.from<t.to&&/^\S+$/.test(t.insert.toString()))&&(this.pendingIOSKey=void 0,S(this.view.contentDOM,e.key,e.keyCode,e instanceof KeyboardEvent?e:void 0))}ignoreDuringComposition(t){return!!/^key/.test(t.type)&&(this.composing>0||!!(Y.safari&&!Y.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100)&&(this.compositionPendingKey=!1,!0))}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.view.observer.update(t),this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function ev(t,e){return(i,s)=>{try{return e.call(t,s,i)}catch(t){tY(i.state,t)}}}let eb=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],ey="dthko",ex=[16,17,18,20,91,92,224,225];function eS(t){return .7*Math.max(0,t)+8}class eM{constructor(t,e,i,s){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=s,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParents=function(t){let e=t.ownerDocument,i,s;for(let o=t.parentNode;o;)if(o==e.body||i&&s)break;else if(1==o.nodeType)!s&&o.scrollHeight>o.clientHeight&&(s=o),!i&&o.scrollWidth>o.clientWidth&&(i=o),o=o.assignedSlot||o.parentNode;else if(11==o.nodeType)o=o.host;else break;return{x:i,y:s}}(t.contentDOM),this.atoms=t.state.facet(t1).map(e=>e(t));let n=t.contentDOM.ownerDocument;n.addEventListener("mousemove",this.move=this.move.bind(this)),n.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(o.$t.allowMultipleSelections)&&function(t,e){let i=t.state.facet(tE);return i.length?i[0](e):Y.mac?e.metaKey:e.ctrlKey}(t,e),this.dragging=!!function(t,e){let{main:i}=t.state.selection;if(i.empty)return!1;let s=l(t.root);if(!s||0==s.rangeCount)return!0;let o=s.getRangeAt(0).getClientRects();for(let t=0;t<o.length;t++){let i=o[t];if(i.left<=e.clientX&&i.right>=e.clientX&&i.top<=e.clientY&&i.bottom>=e.clientY)return!0}return!1}(t,e)&&1==eN(e)&&null}start(t){!1===this.dragging&&this.select(t)}move(t){var e;if(0==t.buttons)return this.destroy();if(this.dragging||null==this.dragging&&10>(e=this.startEvent,Math.max(Math.abs(e.clientX-t.clientX),Math.abs(e.clientY-t.clientY))))return;this.select(this.lastEvent=t);let i=0,s=0,o=0,n=0,r=this.view.win.innerWidth,l=this.view.win.innerHeight;this.scrollParents.x&&({left:o,right:r}=this.scrollParents.x.getBoundingClientRect()),this.scrollParents.y&&({top:n,bottom:l}=this.scrollParents.y.getBoundingClientRect());let h=t9(this.view);t.clientX-h.left<=o+6?i=-eS(o-t.clientX):t.clientX+h.right>=r-6&&(i=eS(t.clientX-r)),t.clientY-h.top<=n+6?s=-eS(n-t.clientY):t.clientY+h.bottom>=l-6&&(s=eS(t.clientY-l)),this.setScrollSpeed(i,s)}up(t){null==this.dragging&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){let{x:t,y:e}=this.scrollSpeed;t&&this.scrollParents.x&&(this.scrollParents.x.scrollLeft+=t,t=0),e&&this.scrollParents.y&&(this.scrollParents.y.scrollTop+=e,e=0),(t||e)&&this.view.win.scrollBy(t,e),!1===this.dragging&&this.select(this.lastEvent)}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let s=t.ranges[i],n=null;if(s.empty){let t=ec(this.atoms,s.from,0);t!=s.from&&(n=o.OF.cursor(t,-1))}else{let t=ec(this.atoms,s.from,-1),e=ec(this.atoms,s.to,1);(t!=s.from||e!=s.to)&&(n=o.OF.range(s.from==s.anchor?t:e,s.from==s.head?t:e))}n&&(e||(e=t.ranges.slice()),e[i]=n)}return e?o.OF.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));(this.mustSelect||!i.eq(e.state.selection,!1===this.dragging))&&this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){t.transactions.some(t=>t.isUserEvent("input.type"))?this.destroy():this.style.update(t)&&setTimeout(()=>this.select(this.lastEvent),20)}}let eC=Object.create(null),ek=Object.create(null),eA=Y.ie&&Y.ie_version<15||Y.ios&&Y.webkit_version<604;function eO(t,e,i){for(let s of t.facet(e))i=s(i,t);return i}function eD(t,e){e=eO(t.state,tV,e);let{state:i}=t,s,n=1,r=i.toText(e),l=r.lines==i.selection.ranges.length;if(null!=eF&&i.selection.ranges.every(t=>t.empty)&&eF==r.toString()){let t=-1;s=i.changeByRange(s=>{let h=i.doc.lineAt(s.from);if(h.from==t)return{range:s};t=h.from;let a=i.toText((l?r.line(n++).text:e)+i.lineBreak);return{changes:{from:h.from,insert:a},range:o.OF.cursor(s.from+a.length)}})}else s=l?i.changeByRange(t=>{let e=r.line(n++);return{changes:{from:t.from,to:t.to,insert:e.text},range:o.OF.cursor(t.from+e.length)}}):i.replaceSelection(r);t.dispatch(s,{userEvent:"input.paste",scrollIntoView:!0})}function eT(t,e,i,s){if(1==s)return o.OF.cursor(e,i);{if(2==s)return function(t,e,i=1){let s=t.charCategorizer(e),n=t.doc.lineAt(e),r=e-n.from;if(0==n.length)return o.OF.cursor(e);0==r?i=1:r==n.length&&(i=-1);let l=r,h=r;i<0?l=(0,o.zK)(n.text,r,!1):h=(0,o.zK)(n.text,r);let a=s(n.text.slice(l,h));for(;l>0;){let t=(0,o.zK)(n.text,l,!1);if(s(n.text.slice(t,l))!=a)break;l=t}for(;h<n.length;){let t=(0,o.zK)(n.text,h);if(s(n.text.slice(h,t))!=a)break;h=t}return o.OF.range(l+n.from,h+n.from)}(t.state,e,i);let n=tc.find(t.docView,e),r=t.state.doc.lineAt(n?n.posAtEnd:e),l=n?n.posAtStart:r.from,h=n?n.posAtEnd:r.to;return h<t.state.doc.length&&h==r.to&&h++,o.OF.range(l,h)}}ek.scroll=t=>{t.inputState.lastScrollTop=t.scrollDOM.scrollTop,t.inputState.lastScrollLeft=t.scrollDOM.scrollLeft},eC.keydown=(t,e)=>(t.inputState.setSelectionOrigin("select"),27==e.keyCode&&0!=t.inputState.tabFocusMode&&(t.inputState.tabFocusMode=Date.now()+2e3),!1),ek.touchstart=(t,e)=>{t.inputState.lastTouchTime=Date.now(),t.inputState.setSelectionOrigin("select.pointer")},ek.touchmove=t=>{t.inputState.setSelectionOrigin("select.pointer")},eC.mousedown=(t,e)=>{if(t.observer.flush(),t.inputState.lastTouchTime>Date.now()-2e3)return!1;let i=null;for(let s of t.state.facet(tB))if(i=s(t,e))break;if(i||0!=e.button||(i=function(t,e){let i=eR(t,e),s=eN(e),n=t.state.selection;return{update(t){t.docChanged&&(i.pos=t.changes.mapPos(i.pos),n=n.map(t.changes))},get(e,r,l){let h=eR(t,e),a,c=eT(t,h.pos,h.bias,s);if(i.pos!=h.pos&&!r){let e=eT(t,i.pos,i.bias,s),n=Math.min(e.from,c.from),r=Math.max(e.to,c.to);c=n<c.from?o.OF.range(n,r):o.OF.range(r,n)}return r?n.replaceRange(n.main.extend(c.from,c.to)):l&&1==s&&n.ranges.length>1&&(a=function(t,e){for(let i=0;i<t.ranges.length;i++){let{from:s,to:n}=t.ranges[i];if(s<=e&&n>=e)return o.OF.create(t.ranges.slice(0,i).concat(t.ranges.slice(i+1)),t.mainIndex==i?0:t.mainIndex-(t.mainIndex>i))}return null}(n,h.pos))?a:l?n.addRange(c):o.OF.create([c])}}}(t,e)),i){let s=!t.hasFocus;t.inputState.startMouseSelection(new eM(t,e,i,s)),s&&t.observer.ignore(()=>{y(t.contentDOM);let e=t.root.activeElement;e&&!e.contains(t.contentDOM)&&e.blur()});let o=t.inputState.mouseSelection;if(o)return o.start(e),!1===o.dragging}return!1};let eE=(t,e,i)=>e>=i.top&&e<=i.bottom&&t>=i.left&&t<=i.right;function eR(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:i,bias:function(t,e,i,s){let o=tc.find(t.docView,e);if(!o)return 1;let n=e-o.posAtStart;if(0==n)return 1;if(n==o.length)return -1;let r=o.coordsAt(n,-1);if(r&&eE(i,s,r))return -1;let l=o.coordsAt(n,1);return l&&eE(i,s,l)?1:r&&r.bottom>=s?-1:1}(t,i,e.clientX,e.clientY)}}let eB=Y.ie&&Y.ie_version<=11,eL=null,eP=0,eH=0;function eN(t){if(!eB)return t.detail;let e=eL,i=eH;return eL=t,eH=Date.now(),eP=!e||i>Date.now()-400&&2>Math.abs(e.clientX-t.clientX)&&2>Math.abs(e.clientY-t.clientY)?(eP+1)%3:1}function eV(t,e,i,s){let o;if(!(i=eO(t.state,tV,i)))return;let n=t.posAtCoords({x:e.clientX,y:e.clientY},!1),{draggedContent:r}=t.inputState,l=s&&r&&((o=t.state.facet(tR)).length?o[0](e):Y.mac?!e.altKey:!e.ctrlKey)?{from:r.from,to:r.to}:null,h={from:n,insert:i},a=t.state.changes(l?[l,h]:h);t.focus(),t.dispatch({changes:a,selection:{anchor:a.mapPos(n,-1),head:a.mapPos(n,1)},userEvent:l?"move.drop":"input.drop"}),t.inputState.draggedContent=null}eC.dragstart=(t,e)=>{let{selection:{main:i}}=t.state;if(e.target.draggable){let s=t.docView.nearest(e.target);if(s&&s.isWidget){let t=s.posAtStart,e=t+s.length;(t>=i.to||e<=i.from)&&(i=o.OF.range(t,e))}}let{inputState:s}=t;return s.mouseSelection&&(s.mouseSelection.dragging=!0),s.draggedContent=i,e.dataTransfer&&(e.dataTransfer.setData("Text",eO(t.state,tF,t.state.sliceDoc(i.from,i.to))),e.dataTransfer.effectAllowed="copyMove"),!1},eC.dragend=t=>(t.inputState.draggedContent=null,!1),eC.drop=(t,e)=>{if(!e.dataTransfer)return!1;if(t.state.readOnly)return!0;let i=e.dataTransfer.files;if(i&&i.length){let s=Array(i.length),o=0,n=()=>{++o==i.length&&eV(t,e,s.filter(t=>null!=t).join(t.state.lineBreak),!1)};for(let t=0;t<i.length;t++){let e=new FileReader;e.onerror=n,e.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(e.result)||(s[t]=e.result),n()},e.readAsText(i[t])}return!0}{let i=e.dataTransfer.getData("Text");if(i)return eV(t,e,i,!0),!0}return!1},eC.paste=(t,e)=>{if(t.state.readOnly)return!0;t.observer.flush();let i=eA?null:e.clipboardData;return i?(eD(t,i.getData("text/plain")||i.getData("text/uri-list")),!0):(!function(t){let e=t.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout(()=>{t.focus(),i.remove(),eD(t,i.value)},50)}(t),!1)};let eF=null;eC.copy=eC.cut=(t,e)=>{let{text:i,ranges:s,linewise:o}=function(t){let e=[],i=[],s=!1;for(let s of t.selection.ranges)s.empty||(e.push(t.sliceDoc(s.from,s.to)),i.push(s));if(!e.length){let o=-1;for(let{from:s}of t.selection.ranges){let n=t.doc.lineAt(s);n.number>o&&(e.push(n.text),i.push({from:n.from,to:Math.min(t.doc.length,n.to+1)})),o=n.number}s=!0}return{text:eO(t,tF,e.join(t.lineBreak)),ranges:i,linewise:s}}(t.state);if(!i&&!o)return!1;eF=o?i:null,"cut"!=e.type||t.state.readOnly||t.dispatch({changes:s,scrollIntoView:!0,userEvent:"delete.cut"});let n=eA?null:e.clipboardData;return n?(n.clearData(),n.setData("text/plain",i),!0):(!function(t,e){let i=t.dom.parentNode;if(!i)return;let s=i.appendChild(document.createElement("textarea"));s.style.cssText="position: fixed; left: -10000px; top: 10px",s.value=e,s.focus(),s.selectionEnd=e.length,s.selectionStart=0,setTimeout(()=>{s.remove(),t.focus()},50)}(t,i),!1)};let eW=o.YH.define();function ez(t,e){let i=[];for(let s of t.facet(tN)){let o=s(t,e);o&&i.push(o)}return i.length?t.update({effects:i,annotations:eW.of(!0)}):null}function eK(t){setTimeout(()=>{let e=t.hasFocus;if(e!=t.inputState.notifiedFocused){let i=ez(t.state,e);i?t.dispatch(i):t.update([])}},10)}ek.focus=t=>{t.inputState.lastFocusTime=Date.now(),!t.scrollDOM.scrollTop&&(t.inputState.lastScrollTop||t.inputState.lastScrollLeft)&&(t.scrollDOM.scrollTop=t.inputState.lastScrollTop,t.scrollDOM.scrollLeft=t.inputState.lastScrollLeft),eK(t)},ek.blur=t=>{t.observer.clearSelectionRange(),eK(t)},ek.compositionstart=ek.compositionupdate=t=>{!t.observer.editContext&&(null==t.inputState.compositionFirstChange&&(t.inputState.compositionFirstChange=!0),t.inputState.composing<0&&(t.inputState.composing=0))},ek.compositionend=t=>{t.observer.editContext||(t.inputState.composing=-1,t.inputState.compositionEndedAt=Date.now(),t.inputState.compositionPendingKey=!0,t.inputState.compositionPendingChange=t.observer.pendingRecords().length>0,t.inputState.compositionFirstChange=null,Y.chrome&&Y.android?t.observer.flushSoon():t.inputState.compositionPendingChange?Promise.resolve().then(()=>t.observer.flush()):setTimeout(()=>{t.inputState.composing<0&&t.docView.hasComposition&&t.update([])},50))},ek.contextmenu=t=>{t.inputState.lastContextMenu=Date.now()},eC.beforeinput=(t,e)=>{var i,s;let o;if("insertReplacementText"==e.inputType&&t.observer.editContext){let s=null==(i=e.dataTransfer)?void 0:i.getData("text/plain"),o=e.getTargetRanges();if(s&&o.length){let e=o[0],i=t.posAtDOM(e.startContainer,e.startOffset),n=t.posAtDOM(e.endContainer,e.endOffset);return em(t,{from:i,to:n,insert:t.state.toText(s)},null),!0}}if(Y.chrome&&Y.android&&(o=eb.find(t=>t.inputType==e.inputType))&&(t.observer.delayAndroidKey(o.key,o.keyCode),"Backspace"==o.key||"Delete"==o.key)){let e=(null==(s=window.visualViewport)?void 0:s.height)||0;setTimeout(()=>{var i;((null==(i=window.visualViewport)?void 0:i.height)||0)>e+10&&t.hasFocus&&(t.contentDOM.blur(),t.focus())},100)}return Y.ios&&"deleteContentForward"==e.inputType&&t.observer.flushSoon(),Y.safari&&"insertText"==e.inputType&&t.inputState.composing>=0&&setTimeout(()=>ek.compositionend(t,e),20),!1};let eI=new Set,eq=["pre-wrap","normal","pre-line","break-spaces"],ej=!1;class eY{constructor(t){this.lineWrapping=t,this.doc=o.EY.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return eq.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let s=t[i];s<0?i++:this.heightSamples[Math.floor(10*s)]||(e=!0,this.heightSamples[Math.floor(10*s)]=!0)}return e}refresh(t,e,i,s,o,n){let r=eq.indexOf(t)>-1,l=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=r;if(this.lineWrapping=r,this.lineHeight=e,this.charWidth=i,this.textHeight=s,this.lineLength=o,l){this.heightSamples={};for(let t=0;t<n.length;t++){let e=n[t];e<0?t++:this.heightSamples[Math.floor(10*e)]=!0}}return l}}class eG{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class e_{constructor(t,e,i,s,o){this.from=t,this.length=e,this.top=i,this.height=s,this._content=o}get type(){return"number"==typeof this._content?ts.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof tl?this._content.widget:null}get widgetLineBreaks(){return"number"==typeof this._content?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new e_(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var eX=function(t){return t[t.ByPos=0]="ByPos",t[t.ByHeight=1]="ByHeight",t[t.ByPosNoHeight=2]="ByPosNoHeight",t}(eX||(eX={}));class e${constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(2&this.flags)>0}set outdated(t){this.flags=2*!!t|-3&this.flags}setHeight(t){this.height!=t&&(Math.abs(this.height-t)>.001&&(ej=!0),this.height=t)}replace(t,e,i){return e$.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,s){let o=this,n=i.doc;for(let r=s.length-1;r>=0;r--){let{fromA:l,toA:h,fromB:a,toB:c}=s[r],d=o.lineAt(l,eX.ByPosNoHeight,i.setDoc(e),0,0),u=d.to>=h?d:o.lineAt(h,eX.ByPosNoHeight,i,0,0);for(c+=u.to-h,h=u.to;r>0&&d.from<=s[r-1].toA;)l=s[r-1].fromA,a=s[r-1].fromB,r--,l<d.from&&(d=o.lineAt(l,eX.ByPosNoHeight,i,0,0));a+=d.from-l,l=d.from;let f=e2.build(i.setDoc(n),t,a,c);o=eU(o,o.replace(l,h,f))}return o.updateHeight(i,0)}static empty(){return new eJ(0,0)}static of(t){if(1==t.length)return t[0];let e=0,i=t.length,s=0,o=0;for(;;)if(e==i)if(s>2*o){let o=t[e-1];o.break?t.splice(--e,1,o.left,null,o.right):t.splice(--e,1,o.left,o.right),i+=1+o.break,s-=o.size}else if(o>2*s){let e=t[i];e.break?t.splice(i,1,e.left,null,e.right):t.splice(i,1,e.left,e.right),i+=2+e.break,o-=e.size}else break;else if(s<o){let i=t[e++];i&&(s+=i.size)}else{let e=t[--i];e&&(o+=e.size)}let n=0;return null==t[e-1]?(n=1,e--):null==t[e]&&(n=1,i++),new e0(e$.of(t.slice(0,e)),n,e$.of(t.slice(i)))}}function eU(t,e){return t==e?t:(t.constructor!=e.constructor&&(ej=!0),e)}e$.prototype.size=1;class eQ extends e${constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,s){return new e_(s,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,s,o){return this.blockAt(0,i,s,o)}forEachLine(t,e,i,s,o,n){t<=o+this.length&&e>=o&&n(this.blockAt(0,i,s,o))}updateHeight(t,e=0,i=!1,s){return s&&s.from<=e&&s.more&&this.setHeight(s.heights[s.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class eJ extends eQ{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,s){return new e_(s,this.length,i,this.height,this.breaks)}replace(t,e,i){let s=i[0];return 1==i.length&&(s instanceof eJ||s instanceof eZ&&4&s.flags)&&10>Math.abs(this.length-s.length)?(s instanceof eZ?s=new eJ(s.length,this.height):s.height=this.height,this.outdated||(s.outdated=!1),s):e$.of(i)}updateHeight(t,e=0,i=!1,s){return s&&s.from<=e&&s.more?this.setHeight(s.heights[s.index++]):(i||this.outdated)&&this.setHeight(Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class eZ extends e${constructor(t){super(t,0)}heightMetrics(t,e){let i=t.doc.lineAt(e).number,s=t.doc.lineAt(e+this.length).number,o=s-i+1,n,r=0;if(t.lineWrapping){let e=Math.min(this.height,t.lineHeight*o);n=e/o,this.length>o+1&&(r=(this.height-e)/(this.length-o-1))}else n=this.height/o;return{firstLine:i,lastLine:s,perLine:n,perChar:r}}blockAt(t,e,i,s){let{firstLine:o,lastLine:n,perLine:r,perChar:l}=this.heightMetrics(e,s);if(e.lineWrapping){let o=s+(t<e.lineHeight?0:Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length)),n=e.doc.lineAt(o),h=r+n.length*l,a=Math.max(i,t-h/2);return new e_(n.from,n.length,a,h,0)}{let s=Math.max(0,Math.min(n-o,Math.floor((t-i)/r))),{from:l,length:h}=e.doc.line(o+s);return new e_(l,h,i+r*s,r,0)}}lineAt(t,e,i,s,o){if(e==eX.ByHeight)return this.blockAt(t,i,s,o);if(e==eX.ByPosNoHeight){let{from:e,to:s}=i.doc.lineAt(t);return new e_(e,s-e,0,0,0)}let{firstLine:n,perLine:r,perChar:l}=this.heightMetrics(i,o),h=i.doc.lineAt(t),a=r+h.length*l,c=h.number-n,d=s+r*c+l*(h.from-o-c);return new e_(h.from,h.length,Math.max(s,Math.min(d,s+this.height-a)),a,0)}forEachLine(t,e,i,s,o,n){t=Math.max(t,o),e=Math.min(e,o+this.length);let{firstLine:r,perLine:l,perChar:h}=this.heightMetrics(i,o);for(let a=t,c=s;a<=e;){let e=i.doc.lineAt(a);if(a==t){let i=e.number-r;c+=l*i+h*(t-o-i)}let s=l+h*e.length;n(new e_(e.from,e.length,c,s,0)),c+=s,a=e.to+1}}replace(t,e,i){let s=this.length-e;if(s>0){let t=i[i.length-1];t instanceof eZ?i[i.length-1]=new eZ(t.length+s):i.push(null,new eZ(s-1))}if(t>0){let e=i[0];e instanceof eZ?i[0]=new eZ(t+e.length):i.unshift(new eZ(t-1),null)}return e$.of(i)}decomposeLeft(t,e){e.push(new eZ(t-1),null)}decomposeRight(t,e){e.push(null,new eZ(this.length-t-1))}updateHeight(t,e=0,i=!1,s){let o=e+this.length;if(s&&s.from<=e+this.length&&s.more){let i=[],n=Math.max(e,s.from),r=-1;for(s.from>e&&i.push(new eZ(s.from-e-1).updateHeight(t,e));n<=o&&s.more;){let e=t.doc.lineAt(n).length;i.length&&i.push(null);let o=s.heights[s.index++];-1==r?r=o:Math.abs(o-r)>=.001&&(r=-2);let l=new eJ(e,o);l.outdated=!1,i.push(l),n+=e+1}n<=o&&i.push(null,new eZ(o-n).updateHeight(t,n));let l=e$.of(i);return(r<0||Math.abs(l.height-this.height)>=.001||Math.abs(r-this.heightMetrics(t,e).perLine)>=.001)&&(ej=!0),eU(this,l)}return(i||this.outdated)&&(this.setHeight(t.heightForGap(e,e+this.length)),this.outdated=!1),this}toString(){return`gap(${this.length})`}}class e0 extends e${constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return 1&this.flags}blockAt(t,e,i,s){let o=i+this.left.height;return t<o?this.left.blockAt(t,e,i,s):this.right.blockAt(t,e,o,s+this.left.length+this.break)}lineAt(t,e,i,s,o){let n=s+this.left.height,r=o+this.left.length+this.break,l=e==eX.ByHeight?t<n:t<r,h=l?this.left.lineAt(t,e,i,s,o):this.right.lineAt(t,e,i,n,r);if(this.break||(l?h.to<r:h.from>r))return h;let a=e==eX.ByPosNoHeight?eX.ByPosNoHeight:eX.ByPos;return l?h.join(this.right.lineAt(r,a,i,n,r)):this.left.lineAt(r,a,i,s,o).join(h)}forEachLine(t,e,i,s,o,n){let r=s+this.left.height,l=o+this.left.length+this.break;if(this.break)t<l&&this.left.forEachLine(t,e,i,s,o,n),e>=l&&this.right.forEachLine(t,e,i,r,l,n);else{let h=this.lineAt(l,eX.ByPos,i,s,o);t<h.from&&this.left.forEachLine(t,h.from-1,i,s,o,n),h.to>=t&&h.from<=e&&n(h),e>h.to&&this.right.forEachLine(h.to+1,e,i,r,l,n)}}replace(t,e,i){let s=this.left.length+this.break;if(e<s)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-s,e-s,i));let o=[];t>0&&this.decomposeLeft(t,o);let n=o.length;for(let t of i)o.push(t);if(t>0&&e1(o,n-1),e<this.length){let t=o.length;this.decomposeRight(e,o),e1(o,t)}return e$.of(o)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&t>=++i&&e.push(null),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,s=i+this.break;if(t>=s)return this.right.decomposeRight(t-s,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<s&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?e$.of(this.break?[t,null,e]:[t,e]):(this.left=eU(this.left,t),this.right=eU(this.right,e),this.setHeight(t.height+e.height),this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,s){let{left:o,right:n}=this,r=e+o.length+this.break,l=null;return(s&&s.from<=e+o.length&&s.more?l=o=o.updateHeight(t,e,i,s):o.updateHeight(t,e,i),s&&s.from<=r+n.length&&s.more?l=n=n.updateHeight(t,r,i,s):n.updateHeight(t,r,i),l)?this.balanced(o,n):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function e1(t,e){let i,s;null==t[e]&&(i=t[e-1])instanceof eZ&&(s=t[e+1])instanceof eZ&&t.splice(e-1,3,new eZ(i.length+1+s.length))}class e2{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let t=Math.min(e,this.lineEnd),i=this.nodes[this.nodes.length-1];i instanceof eJ?i.length+=t-this.pos:(t>this.pos||!this.isCovered)&&this.nodes.push(new eJ(t-this.pos,-1)),this.writtenTo=t,e>t&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let s=i.widget?i.widget.estimatedHeight:0,o=i.widget?i.widget.lineBreaks:0;s<0&&(s=this.oracle.lineHeight);let n=e-t;i.block?this.addBlock(new eQ(n,s,i)):(n||o||s>=5)&&this.addLineDeco(s,o,n)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||null==this.nodes[this.nodes.length-1])&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new eJ(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new eZ(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof eJ)return t;let e=new eJ(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let s=this.ensureLine();s.length+=i,s.collapsed+=i,s.widgetHeight=Math.max(s.widgetHeight,t),s.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=0==this.nodes.length?null:this.nodes[this.nodes.length-1];!(this.lineStart>-1)||e instanceof eJ||this.isCovered?(this.writtenTo<this.pos||null==e)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos)):this.nodes.push(new eJ(0,-1));let i=t;for(let t of this.nodes)t instanceof eJ&&t.updateHeight(this.oracle,i),i+=t?t.length:1;return this.nodes}static build(t,e,i,s){let n=new e2(i,t);return o.om.spans(e,i,s,n,0),n.finish(i)}}class e8{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,s){(t<e||i&&i.heightRelevant||s&&s.heightRelevant)&&ta(t,e,this.changes,5)}}class e3{constructor(t,e,i,s){this.from=t,this.to=e,this.size=i,this.displaySize=s}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let s=t[i],o=e[i];if(s.from!=o.from||s.to!=o.to||s.size!=o.size)return!1}return!0}draw(t,e){return to.replace({widget:new e9(this.displaySize*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class e9 extends ti{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class e5{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=it,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=tm.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(tJ).some(t=>"function"!=typeof t&&"cm-lineWrapping"==t.class);this.heightOracle=new eY(e),this.stateDeco=t.facet(tZ).filter(t=>"function"!=typeof t),this.heightMap=e$.empty().applyChanges(this.stateDeco,o.EY.empty,this.heightOracle.setDoc(t.doc),[new t4(0,0,0,t.doc.length)]);for(let t=0;t<2&&(this.viewport=this.getViewport(0,null),this.updateForViewport());t++);this.updateViewportLines(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=to.set(this.lineGaps.map(t=>t.draw(this,!1))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let s=i?e.head:e.anchor;if(!t.some(({from:t,to:e})=>s>=t&&s<=e)){let{from:e,to:i}=this.lineBlockAt(s);t.push(new e4(e,i))}}return this.viewports=t.sort((t,e)=>t.from-e.from),this.updateScaler()}updateScaler(){let t=this.scaler;return this.scaler=this.heightMap.height<=7e6?it:new ie(this.heightOracle,this.heightMap,this.viewports),2*!t.eq(this.scaler)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,t=>{this.viewportLines.push(ii(t,this.scaler))})}update(t,e=null){var i,s;let n;this.state=t.state;let r=this.stateDeco;this.stateDeco=this.state.facet(tZ).filter(t=>"function"!=typeof t);let l=t.changedRanges,h=t4.extendWithRanges(l,(i=this.stateDeco,s=t?t.changes:o.VR.empty(this.state.doc.length),n=new e8,o.om.compare(r,i,s,n,0),n.changes)),a=this.heightMap.height,c=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);ej=!1,this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),h),(this.heightMap.height!=a||ej)&&(t.flags|=2),c?(this.scrollAnchorPos=t.changes.mapPos(c.from,-1),this.scrollAnchorHeight=c.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=a);let d=h.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<d.from||e.range.head>d.to)||!this.viewportIsAppropriate(d))&&(d=this.getViewport(0,e));let u=d.from!=this.viewport.from||d.to!=this.viewport.to;this.viewport=d,t.flags|=this.updateForViewport(),(u||!t.changes.empty||2&t.flags)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(t.changes),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(tz)&&(this.mustEnforceCursorAssoc=!0)}measure(t){var e;let i,s,n=t.contentDOM,r=window.getComputedStyle(n),l=this.heightOracle,h=r.whiteSpace;this.defaultTextDirection="rtl"==r.direction?tm.RTL:tm.LTR;let a=this.heightOracle.mustRefreshForWrapping(h),c=n.getBoundingClientRect(),d=a||this.mustMeasureContent||this.contentDOMHeight!=c.height;this.contentDOMHeight=c.height,this.mustMeasureContent=!1;let u=0,f=0;if(c.width&&c.height){let{scaleX:t,scaleY:e}=w(n,c);(t>.005&&Math.abs(this.scaleX-t)>.005||e>.005&&Math.abs(this.scaleY-e)>.005)&&(this.scaleX=t,this.scaleY=e,u|=16,a=d=!0)}let p=(parseInt(r.paddingTop)||0)*this.scaleY,g=(parseInt(r.paddingBottom)||0)*this.scaleY;(this.paddingTop!=p||this.paddingBottom!=g)&&(this.paddingTop=p,this.paddingBottom=g,u|=18),this.editorWidth!=t.scrollDOM.clientWidth&&(l.lineWrapping&&(d=!0),this.editorWidth=t.scrollDOM.clientWidth,u|=16);let m=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=m&&(this.scrollAnchorHeight=-1,this.scrollTop=m),this.scrolledToBottom=C(t.scrollDOM);let v=(this.printing?function(t,e){let i=t.getBoundingClientRect();return{left:0,right:i.right-i.left,top:e,bottom:i.bottom-(i.top+e)}}:function(t,e){let i=t.getBoundingClientRect(),s=t.ownerDocument,o=s.defaultView||window,n=Math.max(0,i.left),r=Math.min(o.innerWidth,i.right),l=Math.max(0,i.top),h=Math.min(o.innerHeight,i.bottom);for(let e=t.parentNode;e&&e!=s.body;)if(1==e.nodeType){let i=e,s=window.getComputedStyle(i);if((i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)&&"visible"!=s.overflow){let s=i.getBoundingClientRect();n=Math.max(n,s.left),r=Math.min(r,s.right),l=Math.max(l,s.top),h=Math.min(e==t.parentNode?o.innerHeight:h,s.bottom)}e="absolute"==s.position||"fixed"==s.position?i.offsetParent:i.parentNode}else if(11==e.nodeType)e=e.host;else break;return{left:n-i.left,right:Math.max(n,r)-i.left,top:l-(i.top+e),bottom:Math.max(l,h)-(i.top+e)}})(n,this.paddingTop),b=v.top-this.pixelViewport.top,y=v.bottom-this.pixelViewport.bottom;this.pixelViewport=v;let x=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(x!=this.inView&&(this.inView=x,x&&(d=!0)),!this.inView&&!this.scrollTarget&&(i=(e=t.dom).getBoundingClientRect(),s=e.ownerDocument.defaultView||window,!(i.left<s.innerWidth)||!(i.right>0)||!(i.top<s.innerHeight)||!(i.bottom>0)))return 0;let S=c.width;if((this.contentDOMWidth!=S||this.editorHeight!=t.scrollDOM.clientHeight)&&(this.contentDOMWidth=c.width,this.editorHeight=t.scrollDOM.clientHeight,u|=16),d){let e=t.docView.measureVisibleLineHeights(this.viewport);if(l.mustRefreshForHeights(e)&&(a=!0),a||l.lineWrapping&&Math.abs(S-this.contentDOMWidth)>l.charWidth){let{lineHeight:i,charWidth:s,textHeight:o}=t.docView.measureTextSize();(a=i>0&&l.refresh(h,i,s,o,S/s,e))&&(t.docView.minWidth=0,u|=16)}for(let i of(b>0&&y>0?f=Math.max(b,y):b<0&&y<0&&(f=Math.min(b,y)),ej=!1,this.viewports)){let s=i.from==this.viewport.from?e:t.docView.measureVisibleLineHeights(i);this.heightMap=(a?e$.empty().applyChanges(this.stateDeco,o.EY.empty,this.heightOracle,[new t4(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(l,0,a,new eG(i.from,s))}ej&&(u|=2)}let M=!this.viewportIsAppropriate(this.viewport,f)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return M&&(2&u&&(u|=this.updateScaler()),this.viewport=this.getViewport(f,this.scrollTarget),u|=this.updateForViewport()),(2&u||M)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(a?[]:this.lineGaps,t)),u|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),u}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),s=this.heightMap,o=this.heightOracle,{visibleTop:n,visibleBottom:r}=this,l=new e4(s.lineAt(n-1e3*i,eX.ByHeight,o,0,0).from,s.lineAt(r+(1-i)*1e3,eX.ByHeight,o,0,0).to);if(e){let{head:t}=e.range;if(t<l.from||t>l.to){let i=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),n=s.lineAt(t,eX.ByPos,o,0,0),r;r="center"==e.y?(n.top+n.bottom)/2-i/2:"start"==e.y||"nearest"==e.y&&t<l.from?n.top:n.bottom-i,l=new e4(s.lineAt(r-500,eX.ByHeight,o,0,0).from,s.lineAt(r+i+500,eX.ByHeight,o,0,0).to)}}return l}mapViewport(t,e){let i=e.mapPos(t.from,-1),s=e.mapPos(t.to,1);return new e4(this.heightMap.lineAt(i,eX.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(s,eX.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:s}=this.heightMap.lineAt(t,eX.ByPos,this.heightOracle,0,0),{bottom:o}=this.heightMap.lineAt(e,eX.ByPos,this.heightOracle,0,0),{visibleTop:n,visibleBottom:r}=this;return(0==t||s<=n-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||o>=r+Math.max(10,Math.min(i,250)))&&s>n-2e3&&o<r+2e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let s of t)e.touchesRange(s.from,s.to)||i.push(new e3(e.mapPos(s.from),e.mapPos(s.to),s.size,s.displaySize));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,s=i?1e4:2e3,n=s>>1,r=s<<1;if(this.defaultTextDirection!=tm.LTR&&!i)return[];let l=[],h=(s,r,a,c)=>{if(r-s<n)return;let d=this.state.selection.main,u=[d.from];for(let t of(d.empty||u.push(d.to),u))if(t>s&&t<r){h(s,t-10,a,c),h(t+10,r,a,c);return}let f=function(t,e){for(let i of t)if(e(i))return i}(t,t=>t.from>=a.from&&t.to<=a.to&&Math.abs(t.from-s)<n&&Math.abs(t.to-r)<n&&!u.some(e=>t.from<e&&t.to>e));if(!f){if(r<a.to&&e&&i&&e.visibleRanges.some(t=>t.from<=r&&t.to>=r)){let t=e.moveToLineBoundary(o.OF.cursor(r),!1,!0).head;t>s&&(r=t)}let t=this.gapSize(a,s,r,c),n=i||t<2e6?t:2e6;f=new e3(s,r,t,n)}l.push(f)},a=e=>{var n,l,a;let c,d,u,f,p;if(e.length<r||e.type!=ts.Text)return;let g=(n=e.from,l=e.to,a=this.stateDeco,u=[],f=n,p=0,o.om.spans(a,n,l,{span(){},point(t,e){t>f&&(u.push({from:f,to:t}),p+=t-f),f=e}},20),f<l&&(u.push({from:f,to:l}),p+=l-f),{total:p,ranges:u});if(g.total<r)return;let m=this.scrollTarget?this.scrollTarget.range.head:null;if(i){let t,i,o=s/this.heightOracle.lineLength*this.heightOracle.lineHeight;if(null!=m){let s=e7(g,m),n=((this.visibleBottom-this.visibleTop)/2+o)/e.height;t=s-n,i=s+n}else t=(this.visibleTop-e.top-o)/e.height,i=(this.visibleBottom-e.top+o)/e.height;c=e6(g,t),d=e6(g,i)}else{let i,o,n=g.total*this.heightOracle.charWidth,r=s*this.heightOracle.charWidth,l=0;if(n>2e6)for(let i of t)i.from>=e.from&&i.from<e.to&&i.size!=i.displaySize&&i.from*this.heightOracle.charWidth+l<this.pixelViewport.left&&(l=i.size-i.displaySize);let h=this.pixelViewport.left+l,a=this.pixelViewport.right+l;if(null!=m){let t=e7(g,m),e=((a-h)/2+r)/n;i=t-e,o=t+e}else i=(h-r)/n,o=(a+r)/n;c=e6(g,i),d=e6(g,o)}c>e.from&&h(e.from,c,e,g),d<e.to&&h(d,e.to,e,g)};for(let t of this.viewportLines)Array.isArray(t.type)?t.type.forEach(a):a(t);return l}gapSize(t,e,i,s){let o=e7(s,i)-e7(s,e);return this.heightOracle.lineWrapping?t.height*o:s.total*this.heightOracle.charWidth*o}updateLineGaps(t){e3.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=to.set(t.map(t=>t.draw(this,this.heightOracle.lineWrapping))))}computeVisibleRanges(t){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let i=[];o.om.spans(e,this.viewport.from,this.viewport.to,{span(t,e){i.push({from:t,to:e})},point(){}},20);let s=0;if(i.length!=this.visibleRanges.length)s=12;else for(let e=0;e<i.length&&!(8&s);e++){let o=this.visibleRanges[e],n=i[e];(o.from!=n.from||o.to!=n.to)&&(s|=4,t&&t.mapPos(o.from,-1)==n.from&&t.mapPos(o.to,1)==n.to||(s|=8))}return this.visibleRanges=i,s}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find(e=>e.from<=t&&e.to>=t)||ii(this.heightMap.lineAt(t,eX.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return t>=this.viewportLines[0].top&&t<=this.viewportLines[this.viewportLines.length-1].bottom&&this.viewportLines.find(e=>e.top<=t&&e.bottom>=t)||ii(this.heightMap.lineAt(this.scaler.fromDOM(t),eX.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return ii(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class e4{constructor(t,e){this.from=t,this.to=e}}function e6({total:t,ranges:e},i){if(i<=0)return e[0].from;if(i>=1)return e[e.length-1].to;let s=Math.floor(t*i);for(let t=0;;t++){let{from:i,to:o}=e[t],n=o-i;if(s<=n)return i+s;s-=n}}function e7(t,e){let i=0;for(let{from:s,to:o}of t.ranges){if(e<=o){i+=e-s;break}i+=o-s}return i/t.total}let it={toDOM:t=>t,fromDOM:t=>t,scale:1,eq(t){return t==this}};class ie{constructor(t,e,i){let s=0,o=0,n=0;for(let r of(this.viewports=i.map(({from:i,to:o})=>{let n=e.lineAt(i,eX.ByPos,t,0,0).top,r=e.lineAt(o,eX.ByPos,t,0,0).bottom;return s+=r-n,{from:i,to:o,top:n,bottom:r,domTop:0,domBottom:0}}),this.scale=(7e6-s)/(e.height-s),this.viewports))r.domTop=n+(r.top-o)*this.scale,n=r.domBottom=r.domTop+(r.bottom-r.top),o=r.bottom}toDOM(t){for(let e=0,i=0,s=0;;e++){let o=e<this.viewports.length?this.viewports[e]:null;if(!o||t<o.top)return s+(t-i)*this.scale;if(t<=o.bottom)return o.domTop+(t-o.top);i=o.bottom,s=o.domBottom}}fromDOM(t){for(let e=0,i=0,s=0;;e++){let o=e<this.viewports.length?this.viewports[e]:null;if(!o||t<o.domTop)return i+(t-s)/this.scale;if(t<=o.domBottom)return o.top+(t-o.domTop);i=o.bottom,s=o.domBottom}}eq(t){return t instanceof ie&&this.scale==t.scale&&this.viewports.length==t.viewports.length&&this.viewports.every((e,i)=>e.from==t.viewports[i].from&&e.to==t.viewports[i].to)}}function ii(t,e){if(1==e.scale)return t;let i=e.toDOM(t.top),s=e.toDOM(t.bottom);return new e_(t.from,t.length,i,s-i,Array.isArray(t._content)?t._content.map(t=>ii(t,e)):t._content)}let is=o.sj.define({combine:t=>t.join(" ")}),io=o.sj.define({combine:t=>t.indexOf(!0)>-1}),ir=n.G.newName(),il=n.G.newName(),ih=n.G.newName(),ia={"&light":"."+il,"&dark":"."+ih};function ic(t,e,i){return new n.G(e,{finish:e=>/&/.test(e)?e.replace(/&\w*/,e=>{if("&"==e)return t;if(!i||!i[e])throw RangeError(`Unsupported selector: ${e}`);return i[e]}):t+" "+e})}let id=ic("."+ir,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0,overflowAnchor:"none"},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#ddd"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0,zIndex:300},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-dialog":{padding:"2px 19px 4px 6px",position:"relative","& label":{fontSize:"80%"}},".cm-dialog-close":{position:"absolute",top:"3px",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",fontSize:"14px",padding:"0"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top",userSelect:"none"},".cm-highlightSpace":{backgroundImage:"radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%)",backgroundPosition:"center"},".cm-highlightTab":{backgroundImage:'url(\'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>\')',backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},ia),iu={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},ip=Y.ie&&Y.ie_version<=11;class ig{constructor(t){this.view=t,this.active=!1,this.editContext=null,this.selectionRange=new v,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver(e=>{for(let t of e)this.queue.push(t);(Y.ie&&Y.ie_version<=11||Y.ios&&t.composing)&&e.some(t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length)?this.flushSoon():this.flush()}),window.EditContext&&!1!==t.constructor.EDIT_CONTEXT&&!(Y.chrome&&Y.chrome_version<126)&&(this.editContext=new iv(t),t.state.facet(tG)&&(t.contentDOM.editContext=this.editContext.editContext)),ip&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),"function"==typeof ResizeObserver&&(this.resizeScroll=new ResizeObserver(()=>{var t;(null==(t=this.view.docView)?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()}),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),"function"==typeof IntersectionObserver&&(this.intersection=new IntersectionObserver(t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.editContext&&this.view.requestMeasure(this.editContext.measureReq),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(t){("change"!=t.type&&t.type||t.matches)&&(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500))}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some((e,i)=>e!=t[i]))){for(let e of(this.gapIntersection.disconnect(),t))this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,s=this.selectionRange;if(i.state.facet(tG)?i.root.activeElement!=this.dom:!a(this.dom,s))return;let o=s.anchorNode&&i.docView.nearest(s.anchorNode);if(o&&o.ignoreEvent(t)){e||(this.selectionChanged=!1);return}(Y.ie&&Y.ie_version<=11||Y.android&&Y.chrome)&&!i.state.selection.main.empty&&s.focusNode&&d(s.focusNode,s.focusOffset,s.anchorNode,s.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=l(t.root);if(!e)return!1;let i=Y.safari&&11==t.root.nodeType&&t.root.activeElement==this.dom&&function(t,e){if(e.getComposedRanges){let i=e.getComposedRanges(t.root)[0];if(i)return iw(t,i)}let i=null;function s(t){t.preventDefault(),t.stopImmediatePropagation(),i=t.getTargetRanges()[0]}return t.contentDOM.addEventListener("beforeinput",s,!0),t.dom.ownerDocument.execCommand("indent"),t.contentDOM.removeEventListener("beforeinput",s,!0),i?iw(t,i):null}(this.view,e)||e;if(!i||this.selectionRange.eq(i))return!1;let s=a(this.dom,i);return s&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&function(t,e){let i=e.focusNode,s=e.focusOffset;if(!i||e.anchorNode!=i||e.anchorOffset!=s)return!1;for(s=Math.min(s,g(i));;)if(s){if(1!=i.nodeType)return!1;let t=i.childNodes[s-1];"false"==t.contentEditable?s--:s=g(i=t)}else{if(i==t)return!0;s=u(i),i=i.parentNode}}(this.dom,i)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(i),s&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(1==i.nodeType)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else if(11==i.nodeType)i=i.host;else break;if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);for(let t of this.scrollTargets=e)t.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,iu),ip&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),ip&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;this.delayedAndroidKey||(this.flushingAndroidKey=this.view.win.requestAnimationFrame(()=>{let t=this.delayedAndroidKey;t&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=t.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&t.force&&S(this.dom,t.key,t.keyCode))})),this.delayedAndroidKey&&"Enter"!=t||(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(null==(i=this.delayedAndroidKey)?void 0:i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,s=!1;for(let o of t){let t=this.readMutation(o);t&&(t.typeOver&&(s=!0),-1==e?{from:e,to:i}=t:(e=Math.min(t.from,e),i=Math.max(t.to,i)))}return{from:e,to:i,typeOver:s}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),s=this.selectionChanged&&a(this.dom,this.selectionRange);if(t<0&&!s)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let o=new ep(this.view,t,e,i);return this.view.docView.domChanged={newSel:o.newSel?o.newSel.main:null},o}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,s=eg(this.view,e);return this.view.state==i&&(e.domChanged||e.newSel&&!e.newSel.main.eq(this.view.state.selection.main))&&this.view.update([]),s}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty("attributes"==t.type),"attributes"==t.type&&(e.flags|=4),"childList"==t.type){let i=im(e,t.previousSibling||t.target.previousSibling,-1),s=im(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:s?e.posBefore(s):e.posAtEnd,typeOver:!1}}return"characterData"==t.type?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener?this.printQuery.addEventListener("change",this.onPrint):this.printQuery.addListener(this.onPrint):t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener?this.printQuery.removeEventListener("change",this.onPrint):this.printQuery.removeListener(this.onPrint):t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}update(t){this.editContext&&(this.editContext.update(t),t.startState.facet(tG)!=t.state.facet(tG)&&(t.view.contentDOM.editContext=t.state.facet(tG)?this.editContext.editContext:null))}destroy(){var t,e,i;for(let s of(this.stop(),null==(t=this.intersection)||t.disconnect(),null==(e=this.gapIntersection)||e.disconnect(),null==(i=this.resizeScroll)||i.disconnect(),this.scrollTargets))s.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey),this.editContext&&(this.view.contentDOM.editContext=null,this.editContext.destroy())}}function im(t,e,i){for(;e;){let s=T.get(e);if(s&&s.parent==t)return s;let o=e.parentNode;e=o!=t.dom?o:i>0?e.nextSibling:e.previousSibling}return null}function iw(t,e){let i=e.startContainer,s=e.startOffset,o=e.endContainer,n=e.endOffset,r=t.docView.domAtPos(t.state.selection.main.anchor);return d(r.node,r.offset,o,n)&&([i,s,o,n]=[o,n,i,s]),{anchorNode:i,anchorOffset:s,focusNode:o,focusOffset:n}}class iv{constructor(t){this.from=0,this.to=0,this.pendingContextChange=null,this.handlers=Object.create(null),this.composing=null,this.resetRange(t.state);let e=this.editContext=new window.EditContext({text:t.state.doc.sliceString(this.from,this.to),selectionStart:this.toContextPos(Math.max(this.from,Math.min(this.to,t.state.selection.main.anchor))),selectionEnd:this.toContextPos(t.state.selection.main.head)});for(let i in this.handlers.textupdate=e=>{let i=t.state.selection.main,{anchor:s,head:n}=i,r=this.toEditorPos(e.updateRangeStart),l=this.toEditorPos(e.updateRangeEnd);t.inputState.composing>=0&&!this.composing&&(this.composing={contextBase:e.updateRangeStart,editorBase:r,drifted:!1});let h={from:r,to:l,insert:o.EY.of(e.text.split("\n"))};if(h.from==this.from&&s<this.from?h.from=s:h.to==this.to&&s>this.to&&(h.to=s),h.from==h.to&&!h.insert.length){let s=o.OF.single(this.toEditorPos(e.selectionStart),this.toEditorPos(e.selectionEnd));s.main.eq(i)||t.dispatch({selection:s,userEvent:"select"});return}if((Y.mac||Y.android)&&h.from==n-1&&/^\. ?$/.test(e.text)&&"off"==t.contentDOM.getAttribute("autocorrect")&&(h={from:r,to:l,insert:o.EY.of([e.text.replace("."," ")])}),this.pendingContextChange=h,!t.state.readOnly){let i=this.to-this.from+(h.to-h.from+h.insert.length);em(t,h,o.OF.single(this.toEditorPos(e.selectionStart,i),this.toEditorPos(e.selectionEnd,i)))}this.pendingContextChange&&(this.revertPending(t.state),this.setSelection(t.state))},this.handlers.characterboundsupdate=i=>{let s=[],o=null;for(let e=this.toEditorPos(i.rangeStart),n=this.toEditorPos(i.rangeEnd);e<n;e++){let i=t.coordsForChar(e);o=i&&new DOMRect(i.left,i.top,i.right-i.left,i.bottom-i.top)||o||new DOMRect,s.push(o)}e.updateCharacterBounds(i.rangeStart,s)},this.handlers.textformatupdate=e=>{let i=[];for(let t of e.getTextFormats()){let e=t.underlineStyle,s=t.underlineThickness;if("None"!=e&&"None"!=s){let o=this.toEditorPos(t.rangeStart),n=this.toEditorPos(t.rangeEnd);if(o<n){let t=`text-decoration: underline ${"Dashed"==e?"dashed ":"Squiggle"==e?"wavy ":""}${"Thin"==s?1:2}px`;i.push(to.mark({attributes:{style:t}}).range(o,n))}}}t.dispatch({effects:tj.of(to.set(i))})},this.handlers.compositionstart=()=>{t.inputState.composing<0&&(t.inputState.composing=0,t.inputState.compositionFirstChange=!0)},this.handlers.compositionend=()=>{if(t.inputState.composing=-1,t.inputState.compositionFirstChange=null,this.composing){let{drifted:e}=this.composing;this.composing=null,e&&this.reset(t.state)}},this.handlers)e.addEventListener(i,this.handlers[i]);this.measureReq={read:t=>{this.editContext.updateControlBounds(t.contentDOM.getBoundingClientRect());let e=l(t.root);e&&e.rangeCount&&this.editContext.updateSelectionBounds(e.getRangeAt(0).getBoundingClientRect())}}}applyEdits(t){let e=0,i=!1,s=this.pendingContextChange;return t.changes.iterChanges((o,n,r,l,h)=>{if(i)return;let a=h.length-(n-o);if(s&&n>=s.to)if(s.from==o&&s.to==n&&s.insert.eq(h)){s=this.pendingContextChange=null,e+=a,this.to+=a;return}else s=null,this.revertPending(t.state);if(o+=e,(n+=e)<=this.from)this.from+=a,this.to+=a;else if(o<this.to){if(o<this.from||n>this.to||this.to-this.from+h.length>3e4){i=!0;return}this.editContext.updateText(this.toContextPos(o),this.toContextPos(n),h.toString()),this.to+=a}e+=a}),s&&!i&&this.revertPending(t.state),!i}update(t){let e=this.pendingContextChange,i=t.startState.selection.main;this.composing&&(this.composing.drifted||!t.changes.touchesRange(i.from,i.to)&&t.transactions.some(t=>!t.isUserEvent("input.type")&&t.changes.touchesRange(this.from,this.to)))?(this.composing.drifted=!0,this.composing.editorBase=t.changes.mapPos(this.composing.editorBase)):this.applyEdits(t)&&this.rangeIsValid(t.state)?(t.docChanged||t.selectionSet||e)&&this.setSelection(t.state):(this.pendingContextChange=null,this.reset(t.state)),(t.geometryChanged||t.docChanged||t.selectionSet)&&t.view.requestMeasure(this.measureReq)}resetRange(t){let{head:e}=t.selection.main;this.from=Math.max(0,e-1e4),this.to=Math.min(t.doc.length,e+1e4)}reset(t){this.resetRange(t),this.editContext.updateText(0,this.editContext.text.length,t.doc.sliceString(this.from,this.to)),this.setSelection(t)}revertPending(t){let e=this.pendingContextChange;this.pendingContextChange=null,this.editContext.updateText(this.toContextPos(e.from),this.toContextPos(e.from+e.insert.length),t.doc.sliceString(e.from,e.to))}setSelection(t){let{main:e}=t.selection,i=this.toContextPos(Math.max(this.from,Math.min(this.to,e.anchor))),s=this.toContextPos(e.head);(this.editContext.selectionStart!=i||this.editContext.selectionEnd!=s)&&this.editContext.updateSelection(i,s)}rangeIsValid(t){let{head:e}=t.selection.main;return!(this.from>0&&e-this.from<500||this.to<t.doc.length&&this.to-e<500||this.to-this.from>3e4)}toEditorPos(t,e=this.to-this.from){t=Math.min(t,e);let i=this.composing;return i&&i.drifted?i.editorBase+(t-i.contextBase):t+this.from}toContextPos(t){let e=this.composing;return e&&e.drifted?e.contextBase+(t-e.editorBase):t-this.from}destroy(){for(let t in this.handlers)this.editContext.removeEventListener(t,this.handlers[t])}}class ib{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return!!this.inputState&&this.inputState.composing>0}get compositionStarted(){return!!this.inputState&&this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){var e;this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:i}=t;for(let e of(this.dispatchTransactions=t.dispatchTransactions||i&&(t=>t.forEach(t=>i(t,this)))||(t=>this.update(t)),this.dispatch=this.dispatch.bind(this),this._root=t.root||function(t){for(;t;){if(t&&(9==t.nodeType||11==t.nodeType&&t.host))return t;t=t.assignedSlot||t.parentNode}return null}(t.parent)||document,this.viewState=new e5(t.state||o.$t.create(t)),t.scrollTo&&t.scrollTo.is(tq)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(tX).map(t=>new tU(t)),this.plugins))e.update(this);this.observer=new ig(this),this.inputState=new ew(this),this.inputState.ensureHandlers(this.plugins),this.docView=new t7(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),(null==(e=document.fonts)?void 0:e.ready)&&document.fonts.ready.then(()=>this.requestMeasure())}dispatch(...t){let e=1==t.length&&t[0]instanceof o.ZX?t:1==t.length&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(0!=this.updateState)throw Error("Calls to EditorView.update are not allowed while an update is in progress");let e=!1,i=!1,s,n=this.state;for(let e of t){if(e.startState!=n)throw RangeError("Trying to update state with a transaction that doesn't start from the previous state.");n=e.state}if(this.destroyed){this.viewState.state=n;return}let r=this.hasFocus,l=0,h=null;t.some(t=>t.annotation(eW))?(this.inputState.notifiedFocused=r,l=1):r!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=r,(h=ez(n,r))||(l=1));let a=this.observer.delayedAndroidKey,c=null;if(a?(this.observer.clearDelayedAndroidKey(),((c=this.observer.readChange())&&!this.state.doc.eq(n.doc)||!this.state.selection.eq(n.selection))&&(c=null)):this.observer.clear(),n.facet(o.$t.phrases)!=this.state.facet(o.$t.phrases))return this.setState(n);s=t6.create(this,n,t),s.flags|=l;let d=this.viewState.scrollTarget;try{for(let e of(this.updateState=2,t)){if(d&&(d=d.map(e.changes)),e.scrollIntoView){let{main:t}=e.state.selection;d=new tI(t.empty?t:o.OF.cursor(t.head,t.head>t.anchor?-1:1))}for(let t of e.effects)t.is(tq)&&(d=t.value.clip(this.state))}this.viewState.update(s,d),this.bidiCache=iS.update(this.bidiCache,s.changes),s.empty||(this.updatePlugins(s),this.inputState.update(s)),e=this.docView.update(s),this.state.facet(t5)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(e,t.some(t=>t.isUserEvent("select.pointer")))}finally{this.updateState=0}if(s.startState.facet(is)!=s.state.facet(is)&&(this.viewState.mustMeasureContent=!0),(e||i||d||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),e&&this.docViewUpdate(),!s.empty)for(let t of this.state.facet(tP))try{t(s)}catch(t){tY(this.state,t,"update listener")}(h||c)&&Promise.resolve().then(()=>{h&&this.state==h.startState&&this.dispatch(h),c&&!eg(this,c)&&a.force&&S(this.contentDOM,a.key,a.keyCode)})}setState(t){if(0!=this.updateState)throw Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=t;return}this.updateState=2;let e=this.hasFocus;try{for(let t of this.plugins)t.destroy(this);for(let e of(this.viewState=new e5(t),this.plugins=t.facet(tX).map(t=>new tU(t)),this.pluginMap.clear(),this.plugins))e.update(this);this.docView.destroy(),this.docView=new t7(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(tX),i=t.state.facet(tX);if(e!=i){let s=[];for(let o of i){let i=e.indexOf(o);if(i<0)s.push(new tU(o));else{let e=this.plugins[i];e.mustUpdate=t,s.push(e)}}for(let e of this.plugins)e.mustUpdate!=t&&e.destroy(this);this.plugins=s,this.pluginMap.clear()}else for(let e of this.plugins)e.mustUpdate=t;for(let t=0;t<this.plugins.length;t++)this.plugins[t].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let t of this.plugins){let e=t.value;if(e&&e.docViewUpdate)try{e.docViewUpdate(this)}catch(t){tY(this.state,t,"doc view update listener")}}}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey){this.measureScheduled=-1,this.requestMeasure();return}this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,s=i.scrollTop*this.scaleY,{scrollAnchorPos:o,scrollAnchorHeight:n}=this.viewState;Math.abs(s-this.viewState.scrollTop)>1&&(n=-1),this.viewState.scrollAnchorHeight=-1;try{for(let t=0;;t++){if(n<0)if(C(i))o=-1,n=this.viewState.heightMap.height;else{let t=this.viewState.scrollAnchorAt(s);o=t.from,n=t.top}this.updateState=1;let r=this.viewState.measure(this);if(!r&&!this.measureRequests.length&&null==this.viewState.scrollTarget)break;if(t>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let l=[];4&r||([this.measureRequests,l]=[l,this.measureRequests]);let h=l.map(t=>{try{return t.read(this)}catch(t){return tY(this.state,t),ix}}),a=t6.create(this,this.state,[]),c=!1;a.flags|=r,e?e.flags|=r:e=a,this.updateState=2,!a.empty&&(this.updatePlugins(a),this.inputState.update(a),this.updateAttrs(),(c=this.docView.update(a))&&this.docViewUpdate());for(let t=0;t<l.length;t++)if(h[t]!=ix)try{let e=l[t];e.write&&e.write(h[t],this)}catch(t){tY(this.state,t)}if(c&&this.docView.updateSelection(!0),!a.viewportChanged&&0==this.measureRequests.length){if(this.viewState.editorHeight)if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,n=-1;continue}else{let t=(o<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(o).top)-n;if(t>1||t<-1){i.scrollTop=(s+=t)/this.scaleY,n=-1;continue}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let t of this.state.facet(tP))t(e)}get themeClasses(){return ir+" "+(this.state.facet(io)?ih:il)+" "+this.state.facet(is)}updateAttrs(){let t=iM(this,tQ,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",writingsuggestions:"false",translate:"no",contenteditable:this.state.facet(tG)?"true":"false",class:"cm-content",style:`${Y.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),iM(this,tJ,e);let i=this.observer.ignore(()=>{let i=te(this.contentDOM,this.contentAttrs,e),s=te(this.dom,this.editorAttrs,t);return i||s});return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let t of i.effects)t.is(ib.announce)&&(e&&(this.announceDOM.textContent=""),e=!1,this.announceDOM.appendChild(document.createElement("div")).textContent=t.value)}mountStyles(){this.styleModules=this.state.facet(t5);let t=this.state.facet(ib.cspNonce);n.G.mount(this.root,this.styleModules.concat(id).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(2==this.updateState)throw Error("Reading the editor layout isn't allowed during an update");0==this.updateState&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if((this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),t)&&!(this.measureRequests.indexOf(t)>-1)){if(null!=t.key){for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key){this.measureRequests[e]=t;return}}this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(void 0===e||e&&e.plugin!=t)&&this.pluginMap.set(t,e=this.plugins.find(e=>e.plugin==t)||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return ed(this,t,ea(this,t,e,i))}moveByGroup(t,e){return ed(this,t,ea(this,t,e,e=>{var i;let s,n;return i=t.head,n=(s=this.state.charCategorizer(i))(e),t=>{let e=s(t);return n==o.Je.Space&&(n=e),n==e}}))}visualLineSide(t,e){let i=this.bidiSpans(t),s=this.textDirectionAt(t.from),n=i[e?i.length-1:0];return o.OF.cursor(n.side(e,s)+t.from,n.forward(!e,s)?1:-1)}moveToLineBoundary(t,e,i=!0){return function(t,e,i,s){let n=eh(t,e.head,e.assoc||-1),r=s&&n.type==ts.Text&&(t.lineWrapping||n.widgetLineBreaks)?t.coordsAtPos(e.assoc<0&&e.head>n.from?e.head-1:e.head):null;if(r){let e=t.dom.getBoundingClientRect(),s=t.textDirectionAt(n.from),l=t.posAtCoords({x:i==(s==tm.LTR)?e.right-1:e.left+1,y:(r.top+r.bottom)/2});if(null!=l)return o.OF.cursor(l,i?-1:1)}return o.OF.cursor(i?n.to:n.from,i?-1:1)}(this,t,e,i)}moveVertically(t,e,i){return ed(this,t,function(t,e,i,s){let n=e.head,r=i?1:-1;if(n==(i?t.state.doc.length:0))return o.OF.cursor(n,e.assoc);let l=e.goalColumn,h,a=t.contentDOM.getBoundingClientRect(),c=t.coordsAtPos(n,e.assoc||-1),d=t.documentTop;if(c)null==l&&(l=c.left-a.left),h=r<0?c.top:c.bottom;else{let e=t.viewState.lineBlockAt(n);null==l&&(l=Math.min(a.right-a.left,t.defaultCharacterWidth*(n-e.from))),h=(r<0?e.top:e.bottom)+d}let u=a.left+l,f=null!=s?s:t.viewState.heightOracle.textHeight>>1;for(let e=0;;e+=10){let i=h+(f+e)*r,s=er(t,{x:u,y:i},!1,r);if(i<a.top||i>a.bottom||(r<0?s<n:s>n)){let e=t.docView.coordsForChar(s),n=!e||i<e.top?-1:1;return o.OF.cursor(s,n,void 0,l)}}}(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),er(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let s=this.state.doc.lineAt(t),o=this.bidiSpans(s);return m(i,o[tA.find(o,t-s.from,-1,e)].dir==tm.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(tW)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>iy)return tD(t.length);let e=this.textDirectionAt(t.from),i;for(let s of this.bidiCache)if(s.from==t.from&&s.dir==e&&(s.fresh||function t(e,i){if(e.length!=i.length)return!1;for(let s=0;s<e.length;s++){let o=e[s],n=i[s];if(o.from!=n.from||o.to!=n.to||o.direction!=n.direction||!t(o.inner,n.inner))return!1}return!0}(s.isolates,i=t8(this,t))))return s.order;i||(i=t8(this,t));let s=function(t,e,i){if(!t)return[new tA(0,0,+(e==tv))];if(e==tw&&!i.length&&!tk.test(t))return tD(t.length);if(i.length)for(;t.length>tO.length;)tO[tO.length]=256;let s=[],o=+(e!=tw);return function t(e,i,s,o,n,r,l){let h=i%2?2:1;!function(t,e,i,s,o){for(let n=0;n<=s.length;n++){let r=n?s[n-1].to:e,l=n<s.length?s[n].from:i,h=n?256:o;for(let e=r,i=h,s=h;e<l;e++){let o=tC(t.charCodeAt(e));512==o?o=i:8==o&&4==s&&(o=16),tO[e]=4==o?2:o,7&o&&(s=o),i=o}for(let t=r,e=h,s=h;t<l;t++){let o=tO[t];if(128==o)t<l-1&&e==tO[t+1]&&24&e?o=tO[t]=e:tO[t]=256;else if(64==o){let o=t+1;for(;o<l&&64==tO[o];)o++;let n=t&&8==e||o<i&&8==tO[o]?1==s?1:8:256;for(let e=t;e<o;e++)tO[e]=n;t=o-1}else 8==o&&1==s&&(tO[t]=1);e=o,7&o&&(s=o)}}}(e,n,r,o,h),function(t,e,i,s,o){let n=1==o?2:1;for(let r=0,l=0,h=0;r<=s.length;r++){let a=r?s[r-1].to:e,c=r<s.length?s[r].from:i;for(let e=a,i,s,r;e<c;e++)if(s=tS[i=t.charCodeAt(e)])if(s<0){for(let t=l-3;t>=0;t-=3)if(tM[t+1]==-s){let i=tM[t+2],s=2&i?o:4&i?1&i?n:o:0;s&&(tO[e]=tO[tM[t]]=s),l=t;break}}else if(189==tM.length)break;else tM[l++]=e,tM[l++]=i,tM[l++]=h;else if(2==(r=tO[e])||1==r){let t=r==o;h=+!t;for(let e=l-3;e>=0;e-=3){let i=tM[e+2];if(2&i)break;if(t)tM[e+2]|=2;else{if(4&i)break;tM[e+2]|=4}}}}}(e,n,r,o,h),function(t,e,i,s){for(let o=0,n=s;o<=i.length;o++){let r=o?i[o-1].to:t,l=o<i.length?i[o].from:e;for(let h=r;h<l;){let r=tO[h];if(256==r){let r=h+1;for(;;)if(r==l){if(o==i.length)break;r=i[o++].to,l=o<i.length?i[o].from:e}else if(256==tO[r])r++;else break;let a=1==n,c=a==((r<e?tO[r]:s)==1)?a?1:2:s;for(let e=r,s=o,n=s?i[s-1].to:t;e>h;)e==n&&(e=i[--s].from,n=s?i[s-1].to:t),tO[--e]=c;h=r}else n=r,h++}}}(n,r,o,h),function e(i,s,o,n,r,l,h){let a=n%2?2:1;if(n%2==r%2)for(let c=s,d=0;c<o;){let s=!0,u=!1;if(d==l.length||c<l[d].from){let t=tO[c];t!=a&&(s=!1,u=16==t)}let f=s||1!=a?null:[],p=s?n:n+1,g=c;t:for(;;)if(d<l.length&&g==l[d].from){if(u)break;let e=l[d];if(!s)for(let t=e.to,i=d+1;;){if(t==o)break t;if(i<l.length&&l[i].from==t)t=l[i++].to;else if(tO[t]==a)break t;else break}d++,f?f.push(e):(e.from>c&&h.push(new tA(c,e.from,p)),t(i,e.direction==tw!=!(p%2)?n+1:n,r,e.inner,e.from,e.to,h),c=e.to),g=e.to}else if(g==o||(s?tO[g]!=a:tO[g]==a))break;else g++;f?e(i,c,g,n+1,r,f,h):c<g&&h.push(new tA(c,g,p)),c=g}else for(let c=o,d=l.length;c>s;){let o=!0,u=!1;if(!d||c>l[d-1].to){let t=tO[c-1];t!=a&&(o=!1,u=16==t)}let f=o||1!=a?null:[],p=o?n:n+1,g=c;t:for(;;)if(d&&g==l[d-1].to){if(u)break;let e=l[--d];if(!o)for(let t=e.from,i=d;;){if(t==s)break t;if(i&&l[i-1].to==t)t=l[--i].from;else if(tO[t-1]==a)break t;else break}f?f.push(e):(e.to<c&&h.push(new tA(e.to,c,p)),t(i,e.direction==tw!=!(p%2)?n+1:n,r,e.inner,e.from,e.to,h),c=e.from),g=e.from}else if(g==s||(o?tO[g-1]!=a:tO[g-1]==a))break;else g--;f?e(i,g,c,n+1,r,f,h):g<c&&h.push(new tA(g,c,p)),c=g}}(e,n,r,i,s,o,l)}(t,o,o,i,0,t.length,s),s}(t.text,e,i);return this.bidiCache.push(new iS(t.from,t.to,e,i,!0,s)),s}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||Y.safari&&(null==(t=this.inputState)?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{y(this.contentDOM),this.docView.updateSelection()})}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((9==t.nodeType?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){for(let t of(this.root.activeElement==this.contentDOM&&this.contentDOM.blur(),this.plugins))t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return tq.of(new tI("number"==typeof t?o.OF.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return tq.of(new tI(o.OF.cursor(i.from),"start","start",i.top-t,e,!0))}setTabFocusMode(t){null==t?this.inputState.tabFocusMode=this.inputState.tabFocusMode<0?0:-1:"boolean"==typeof t?this.inputState.tabFocusMode=t?0:-1:0!=this.inputState.tabFocusMode&&(this.inputState.tabFocusMode=Date.now()+t)}static domEventHandlers(t){return t$.define(()=>({}),{eventHandlers:t})}static domEventObservers(t){return t$.define(()=>({}),{eventObservers:t})}static theme(t,e){let i=n.G.newName(),s=[is.of(i),t5.of(ic(`.${i}`,t))];return e&&e.dark&&s.push(io.of(!0)),s}static baseTheme(t){return o.Nb.lowest(t5.of(ic("."+ir,t,ia)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),s=i&&T.get(i)||T.get(t);return(null==(e=null==s?void 0:s.rootView)?void 0:e.view)||null}}ib.styleModule=t5,ib.inputHandler=tH,ib.clipboardInputFilter=tV,ib.clipboardOutputFilter=tF,ib.scrollHandler=tK,ib.focusChangeEffect=tN,ib.perLineTextDirection=tW,ib.exceptionSink=tL,ib.updateListener=tP,ib.editable=tG,ib.mouseSelectionStyle=tB,ib.dragMovesSelection=tR,ib.clickAddsSelectionRange=tE,ib.decorations=tZ,ib.outerDecorations=t0,ib.atomicRanges=t1,ib.bidiIsolatedRanges=t2,ib.scrollMargins=t3,ib.darkTheme=io,ib.cspNonce=o.sj.define({combine:t=>t.length?t[0]:""}),ib.contentAttributes=tJ,ib.editorAttributes=tQ,ib.lineWrapping=ib.contentAttributes.of({class:"cm-lineWrapping"}),ib.announce=o.Pe.define();let iy=4096,ix={};class iS{constructor(t,e,i,s,o,n){this.from=t,this.to=e,this.dir=i,this.isolates=s,this.fresh=o,this.order=n}static update(t,e){if(e.empty&&!t.some(t=>t.fresh))return t;let i=[],s=t.length?t[t.length-1].dir:tm.LTR;for(let o=Math.max(0,t.length-10);o<t.length;o++){let n=t[o];n.dir!=s||e.touchesRange(n.from,n.to)||i.push(new iS(e.mapPos(n.from,1),e.mapPos(n.to,-1),n.dir,n.isolates,!1,n.order))}return i}}function iM(t,e,i){for(let s=t.state.facet(e),o=s.length-1;o>=0;o--){let e=s[o],n="function"==typeof e?e(t):e;n&&J(n,i)}return i}let iC=Y.mac?"mac":Y.windows?"win":Y.linux?"linux":"key";function ik(t,e,i){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),!1!==i&&e.shiftKey&&(t="Shift-"+t),t}let iA=o.Nb.default(ib.domEventHandlers({keydown:(t,e)=>iL(iT(e.state),t,e,"editor")})),iO=o.sj.define({enables:iA}),iD=new WeakMap;function iT(t){let e=t.facet(iO),i=iD.get(e);return i||iD.set(e,i=function(t,e=iC){let i=Object.create(null),s=Object.create(null),o=(t,e)=>{let i=s[t];if(null==i)s[t]=e;else if(i!=e)throw Error("Key binding "+t+" is used both as a regular binding and as a multi-stroke prefix")},n=(t,s,n,r,l)=>{var h,a;let c=i[t]||(i[t]=Object.create(null)),d=s.split(/ (?!$)/).map(t=>(function(t,e){let i,s,o,n,r=t.split(/-(?!$)/),l=r[r.length-1];"Space"==l&&(l=" ");for(let t=0;t<r.length-1;++t){let l=r[t];if(/^(cmd|meta|m)$/i.test(l))n=!0;else if(/^a(lt)?$/i.test(l))i=!0;else if(/^(c|ctrl|control)$/i.test(l))s=!0;else if(/^s(hift)?$/i.test(l))o=!0;else if(/^mod$/i.test(l))"mac"==e?n=!0:s=!0;else throw Error("Unrecognized modifier name: "+l)}return i&&(l="Alt-"+l),s&&(l="Ctrl-"+l),n&&(l="Meta-"+l),o&&(l="Shift-"+l),l})(t,e));for(let e=1;e<d.length;e++){let i=d.slice(0,e).join(" ");o(i,!0),c[i]||(c[i]={preventDefault:!0,stopPropagation:!1,run:[e=>{let s=iR={view:e,prefix:i,scope:t};return setTimeout(()=>{iR==s&&(iR=null)},4e3),!0}]})}let u=d.join(" ");o(u,!1);let f=c[u]||(c[u]={preventDefault:!1,stopPropagation:!1,run:(null==(a=null==(h=c._any)?void 0:h.run)?void 0:a.slice())||[]});n&&f.run.push(n),r&&(f.preventDefault=!0),l&&(f.stopPropagation=!0)};for(let s of t){let t=s.scope?s.scope.split(" "):["editor"];if(s.any)for(let e of t){let t=i[e]||(i[e]=Object.create(null));t._any||(t._any={preventDefault:!1,stopPropagation:!1,run:[]});let{any:o}=s;for(let e in t)t[e].run.push(t=>o(t,iB))}let o=s[e]||s.key;if(o)for(let e of t)n(e,o,s.run,s.preventDefault,s.stopPropagation),s.shift&&n(e,"Shift-"+o,s.shift,s.preventDefault,s.stopPropagation)}return i}(e.reduce((t,e)=>t.concat(e),[]))),i}function iE(t,e,i){return iL(iT(t.state),e,t,i)}let iR=null,iB=null;function iL(t,e,i,s){iB=e;let n=(0,r.xT)(e),l=(0,o.vS)(n,0),h=(0,o.Fh)(l)==n.length&&" "!=n,a="",c=!1,d=!1,u=!1;iR&&iR.view==i&&iR.scope==s&&(a=iR.prefix+" ",0>ex.indexOf(e.keyCode)&&(d=!0,iR=null));let f=new Set,p=t=>{if(t){for(let e of t.run)if(!f.has(e)&&(f.add(e),e(i)))return t.stopPropagation&&(u=!0),!0;t.preventDefault&&(t.stopPropagation&&(u=!0),d=!0)}return!1},g=t[s],m,w;return g&&(p(g[a+ik(n,e,!h)])?c=!0:h&&(e.altKey||e.metaKey||e.ctrlKey)&&!(Y.windows&&e.ctrlKey&&e.altKey)&&(m=r.E3[e.keyCode])&&m!=n?p(g[a+ik(m,e,!0)])?c=!0:e.shiftKey&&(w=r.BN[e.keyCode])!=n&&w!=m&&p(g[a+ik(w,e,!1)])&&(c=!0):h&&e.shiftKey&&p(g[a+ik(n,e,!0)])&&(c=!0),!c&&p(g._any)&&(c=!0)),d&&(c=!0),c&&u&&e.stopPropagation(),iB=null,c}class iP{constructor(t,e,i,s,o){this.className=t,this.left=e,this.top=i,this.width=s,this.height=o}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className==this.className&&(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",null!=this.width&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(!i.empty)return function(t,e,i){if(i.to<=t.viewport.from||i.from>=t.viewport.to)return[];let s=Math.max(i.from,t.viewport.from),o=Math.min(i.to,t.viewport.to),n=t.textDirection==tm.LTR,r=t.contentDOM,l=r.getBoundingClientRect(),h=iH(t),a=r.querySelector(".cm-line"),c=a&&window.getComputedStyle(a),d=l.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=l.right-(c?parseInt(c.paddingRight):0),f=eh(t,s,1),p=eh(t,o,-1),g=f.type==ts.Text?f:null,m=p.type==ts.Text?p:null;if(g&&(t.lineWrapping||f.widgetLineBreaks)&&(g=iN(t,s,1,g)),m&&(t.lineWrapping||p.widgetLineBreaks)&&(m=iN(t,o,-1,m)),g&&m&&g.from==m.from&&g.to==m.to)return v(b(i.from,i.to,g));{let e=g?b(i.from,null,g):y(f,!1),s=m?b(null,i.to,m):y(p,!0),o=[];return(g||f).to<(m||p).from-(g&&m?1:0)||f.widgetLineBreaks>1&&e.bottom+t.defaultLineHeight/2<s.top?o.push(w(d,e.bottom,u,s.top)):e.bottom<s.top&&t.elementAtHeight((e.bottom+s.top)/2).type==ts.Text&&(e.bottom=s.top=(e.bottom+s.top)/2),v(e).concat(o).concat(v(s))}function w(t,i,s,o){return new iP(e,t-h.left,i-h.top,s-t,o-i)}function v({top:t,bottom:e,horizontal:i}){let s=[];for(let o=0;o<i.length;o+=2)s.push(w(i[o],t,i[o+1],e));return s}function b(e,i,s){let o=1e9,r=-1e9,l=[];function h(e,i,h,a,c){let f=t.coordsAtPos(e,e==s.to?-2:2),p=t.coordsAtPos(h,h==s.from?2:-2);f&&p&&(o=Math.min(f.top,p.top,o),r=Math.max(f.bottom,p.bottom,r),c==tm.LTR?l.push(n&&i?d:f.left,n&&a?u:p.right):l.push(!n&&a?d:p.left,!n&&i?u:f.right))}let a=null!=e?e:s.from,c=null!=i?i:s.to;for(let s of t.visibleRanges)if(s.to>a&&s.from<c)for(let o=Math.max(s.from,a),n=Math.min(s.to,c);;){let s=t.state.doc.lineAt(o);for(let r of t.bidiSpans(s)){let t=r.from+s.from,l=r.to+s.from;if(t>=n)break;l>o&&h(Math.max(t,o),null==e&&t<=a,Math.min(l,n),null==i&&l>=c,r.dir)}if((o=s.to+1)>=n)break}return 0==l.length&&h(a,null==e,c,null==i,t.textDirection),{top:o,bottom:r,horizontal:l}}function y(t,e){let i=l.top+(e?t.top:t.bottom);return{top:i,bottom:i,horizontal:[]}}}(t,e,i);{let s=t.coordsAtPos(i.head,i.assoc||1);if(!s)return[];let o=iH(t);return[new iP(e,s.left-o.left,s.top-o.top,null,s.bottom-s.top)]}}}function iH(t){let e=t.scrollDOM.getBoundingClientRect();return{left:(t.textDirection==tm.LTR?e.left:e.right-t.scrollDOM.clientWidth*t.scaleX)-t.scrollDOM.scrollLeft*t.scaleX,top:e.top-t.scrollDOM.scrollTop*t.scaleY}}function iN(t,e,i,s){let o=t.coordsAtPos(e,2*i);if(!o)return s;let n=t.dom.getBoundingClientRect(),r=(o.top+o.bottom)/2,l=t.posAtCoords({x:n.left+1,y:r}),h=t.posAtCoords({x:n.right-1,y:r});return null==l||null==h?s:{from:Math.max(s.from,Math.min(l,h)),to:Math.min(s.to,Math.max(l,h))}}class iV{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t)}update(t){t.startState.facet(iF)!=t.state.facet(iF)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq))}docViewUpdate(t){!1!==this.layer.updateOnDocViewUpdate&&t.requestMeasure(this.measureReq)}setOrder(t){let e=0,i=t.facet(iF);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;(t!=this.scaleX||e!=this.scaleY)&&(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`)}draw(t){if(t.length!=this.drawn.length||t.some((t,e)=>{var i;return i=this.drawn[e],!(t.constructor==i.constructor&&t.eq(i))})){let e=this.dom.firstChild,i=0;for(let s of t)s.update&&e&&s.constructor&&this.drawn[i].constructor&&s.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(s.draw(),e);for(;e;){let t=e.nextSibling;e.remove(),e=t}this.drawn=t}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}let iF=o.sj.define();function iW(t){return[t$.define(e=>new iV(e,t)),iF.of(t)]}let iz=o.sj.define({combine:t=>(0,o.QR)(t,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})});function iK(t={}){return[iz.of(t),iq,iY,iG,tz.of(!0)]}function iI(t){return t.startState.facet(iz)!=t.state.facet(iz)}let iq=iW({above:!0,markers(t){let{state:e}=t,i=e.facet(iz),s=[];for(let n of e.selection.ranges){let r=n==e.selection.main;if(n.empty||i.drawRangeCursor){let e=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",i=n.empty?n:o.OF.cursor(n.head,n.head>n.anchor?-1:1);for(let o of iP.forRange(t,e,i))s.push(o)}}return s},update(t,e){t.transactions.some(t=>t.selection)&&(e.style.animationName="cm-blink"==e.style.animationName?"cm-blink2":"cm-blink");let i=iI(t);return i&&ij(t.state,e),t.docChanged||t.selectionSet||i},mount(t,e){ij(e.state,t)},class:"cm-cursorLayer"});function ij(t,e){e.style.animationDuration=t.facet(iz).cursorBlinkRate+"ms"}let iY=iW({above:!1,markers:t=>t.state.selection.ranges.map(e=>e.empty?[]:iP.forRange(t,"cm-selectionBackground",e)).reduce((t,e)=>t.concat(e)),update:(t,e)=>t.docChanged||t.selectionSet||t.viewportChanged||iI(t),class:"cm-selectionLayer"}),iG=o.Nb.highest(ib.theme({".cm-line":{"& ::selection, &::selection":{backgroundColor:"transparent !important"},caretColor:"transparent !important"},".cm-content":{caretColor:"transparent !important","& :focus":{caretColor:"initial !important","&::selection, & ::selection":{backgroundColor:"Highlight !important"}}}})),i_=o.Pe.define({map:(t,e)=>null==t?null:e.mapPos(t)}),iX=o.sU.define({create:()=>null,update:(t,e)=>(null!=t&&(t=e.changes.mapPos(t)),e.effects.reduce((t,e)=>e.is(i_)?e.value:t,t))}),i$=t$.fromClass(class{constructor(t){this.view=t,this.cursor=null,this.measureReq={read:this.readPos.bind(this),write:this.drawCursor.bind(this)}}update(t){var e;let i=t.state.field(iX);null==i?null!=this.cursor&&(null==(e=this.cursor)||e.remove(),this.cursor=null):(this.cursor||(this.cursor=this.view.scrollDOM.appendChild(document.createElement("div")),this.cursor.className="cm-dropCursor"),(t.startState.field(iX)!=i||t.docChanged||t.geometryChanged)&&this.view.requestMeasure(this.measureReq))}readPos(){let{view:t}=this,e=t.state.field(iX),i=null!=e&&t.coordsAtPos(e);if(!i)return null;let s=t.scrollDOM.getBoundingClientRect();return{left:i.left-s.left+t.scrollDOM.scrollLeft*t.scaleX,top:i.top-s.top+t.scrollDOM.scrollTop*t.scaleY,height:i.bottom-i.top}}drawCursor(t){if(this.cursor){let{scaleX:e,scaleY:i}=this.view;t?(this.cursor.style.left=t.left/e+"px",this.cursor.style.top=t.top/i+"px",this.cursor.style.height=t.height/i+"px"):this.cursor.style.left="-100000px"}}destroy(){this.cursor&&this.cursor.remove()}setDropPos(t){this.view.state.field(iX)!=t&&this.view.dispatch({effects:i_.of(t)})}},{eventObservers:{dragover(t){this.setDropPos(this.view.posAtCoords({x:t.clientX,y:t.clientY}))},dragleave(t){t.target!=this.view.contentDOM&&this.view.contentDOM.contains(t.relatedTarget)||this.setDropPos(null)},dragend(){this.setDropPos(null)},drop(){this.setDropPos(null)}}});function iU(){return[iX,i$]}function iQ(t,e,i,s,o){e.lastIndex=0;for(let n=t.iterRange(i,s),r=i,l;!n.next().done;r+=n.value.length)if(!n.lineBreak)for(;l=e.exec(n.value);)o(r+l.index,l)}class iJ{constructor(t){let{regexp:e,decoration:i,decorate:s,boundary:o,maxLength:n=1e3}=t;if(!e.global)throw RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,s)this.addMatch=(t,e,i,o)=>s(o,i,i+t[0].length,t,e);else if("function"==typeof i)this.addMatch=(t,e,s,o)=>{let n=i(t,e,s);n&&o(s,s+t[0].length,n)};else if(i)this.addMatch=(t,e,s,o)=>o(s,s+t[0].length,i);else throw RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=o,this.maxLength=n}createDeco(t){let e=new o.vB,i=e.add.bind(e);for(let{from:e,to:s}of function(t,e){let i=t.visibleRanges;if(1==i.length&&i[0].from==t.viewport.from&&i[0].to==t.viewport.to)return i;let s=[];for(let{from:o,to:n}of i)o=Math.max(t.state.doc.lineAt(o).from,o-e),n=Math.min(t.state.doc.lineAt(n).to,n+e),s.length&&s[s.length-1].to>=o?s[s.length-1].to=n:s.push({from:o,to:n});return s}(t,this.maxLength))iQ(t.state.doc,this.regexp,e,s,(e,s)=>this.addMatch(s,t,e,i));return e.finish()}updateDeco(t,e){let i=1e9,s=-1;return(t.docChanged&&t.changes.iterChanges((e,o,n,r)=>{r>=t.view.viewport.from&&n<=t.view.viewport.to&&(i=Math.min(n,i),s=Math.max(r,s))}),t.viewportMoved||s-i>1e3)?this.createDeco(t.view):s>-1?this.updateRange(t.view,e.map(t.changes),i,s):e}updateRange(t,e,i,s){for(let o of t.visibleRanges){let n=Math.max(o.from,i),r=Math.min(o.to,s);if(r>=n){let i=t.state.doc.lineAt(n),s=i.to<r?t.state.doc.lineAt(r):i,l=Math.max(o.from,i.from),h=Math.min(o.to,s.to);if(this.boundary){for(;n>i.from;n--)if(this.boundary.test(i.text[n-1-i.from])){l=n;break}for(;r<s.to;r++)if(this.boundary.test(s.text[r-s.from])){h=r;break}}let a=[],c,d=(t,e,i)=>a.push(i.range(t,e));if(i==s)for(this.regexp.lastIndex=l-i.from;(c=this.regexp.exec(i.text))&&c.index<h-i.from;)this.addMatch(c,t,c.index+i.from,d);else iQ(t.state.doc,this.regexp,l,h,(e,i)=>this.addMatch(i,t,e,d));e=e.update({filterFrom:l,filterTo:h,filter:(t,e)=>t<l||e>h,add:a})}}return e}}let iZ=null!=/x/.unicode?"gu":"g",i0=RegExp("[\0-\b\n-\x1f\x7f-\x9f\xad؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]",iZ),i1={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"},i2=null,i8=o.sj.define({combine(t){let e=(0,o.QR)(t,{render:null,specialChars:i0,addSpecialChars:null});return(e.replaceTabs=!function(){var t;if(null==i2&&"undefined"!=typeof document&&document.body){let e=document.body.style;i2=(null!=(t=e.tabSize)?t:e.MozTabSize)!=null}return i2||!1}())&&(e.specialChars=RegExp("	|"+e.specialChars.source,iZ)),e.addSpecialChars&&(e.specialChars=RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,iZ)),e}});function i3(t={}){return[i8.of(t),i9||(i9=t$.fromClass(class{constructor(t){this.view=t,this.decorations=to.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(t.state.facet(i8)),this.decorations=this.decorator.createDeco(t)}makeDecorator(t){return new iJ({regexp:t.specialChars,decoration:(e,i,s)=>{let{doc:n}=i.state,r=(0,o.vS)(e[0],0);if(9==r){let t=n.lineAt(s),e=i.state.tabSize,r=(0,o.y$)(t.text,e,s-t.from);return to.replace({widget:new i4((e-r%e)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=to.replace({widget:new i5(t,r)}))},boundary:t.replaceTabs?void 0:/[^]/})}update(t){let e=t.state.facet(i8);t.startState.facet(i8)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(t.view)):this.decorations=this.decorator.updateDeco(t,this.decorations)}},{decorations:t=>t.decorations}))]}let i9=null;class i5 extends ti{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){var e;let i=(e=this.code)>=32?"•":10==e?"␤":String.fromCharCode(9216+e),s=t.state.phrase("Control character")+" "+(i1[this.code]||"0x"+this.code.toString(16)),o=this.options.render&&this.options.render(this.code,s,i);if(o)return o;let n=document.createElement("span");return n.textContent=i,n.title=s,n.setAttribute("aria-label",s),n.className="cm-specialChar",n}ignoreEvent(){return!1}}class i4 extends ti{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="	",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}function i6(){return st}let i7=to.line({class:"cm-activeLine"}),st=t$.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){(t.docChanged||t.selectionSet)&&(this.decorations=this.getDeco(t.view))}getDeco(t){let e=-1,i=[];for(let s of t.state.selection.ranges){let o=t.lineBlockAt(s.head);o.from>e&&(i.push(i7.range(o.from)),e=o.from)}return to.set(i)}},{decorations:t=>t.decorations});class se extends ti{constructor(t){super(),this.content=t}toDOM(t){let e=document.createElement("span");return e.className="cm-placeholder",e.style.pointerEvents="none",e.appendChild("string"==typeof this.content?document.createTextNode(this.content):"function"==typeof this.content?this.content(t):this.content.cloneNode(!0)),e.setAttribute("aria-hidden","true"),e}coordsAt(t){let e=t.firstChild?c(t.firstChild):[];if(!e.length)return null;let i=window.getComputedStyle(t.parentNode),s=m(e[0],"rtl"!=i.direction),o=parseInt(i.lineHeight);return s.bottom-s.top>1.5*o?{left:s.left,right:s.right,top:s.top,bottom:s.top+o}:s}ignoreEvent(){return!1}}function si(t){let e=t$.fromClass(class{constructor(e){this.view=e,this.placeholder=t?to.set([to.widget({widget:new se(t),side:1}).range(0)]):to.none}get decorations(){return this.view.state.doc.length?to.none:this.placeholder}},{decorations:t=>t.decorations});return"string"==typeof t?[e,ib.contentAttributes.of({"aria-placeholder":t})]:e}function ss(t,e){var i;let s,n=t.posAtCoords({x:e.clientX,y:e.clientY},!1),r=t.state.doc.lineAt(n),l=n-r.from,h=l>2e3?-1:l==r.length?(i=e.clientX,(s=t.coordsAtPos(t.viewport.from))?Math.round(Math.abs((s.left-i)/t.defaultCharacterWidth)):-1):(0,o.y$)(r.text,t.state.tabSize,n-r.from);return{line:r.number,col:h,off:l}}function so(t){let e=(null==t?void 0:t.eventFilter)||(t=>t.altKey&&0==t.button);return ib.mouseSelectionStyle.of((t,i)=>{let s,n;return e(i)?(s=ss(t,i),n=t.state.selection,s?{update(t){if(t.docChanged){let e=t.changes.mapPos(t.startState.doc.line(s.line).from),i=t.state.doc.lineAt(e);s={line:i.number,col:s.col,off:Math.min(s.off,i.length)},n=n.map(t.changes)}},get(e,i,r){let l=ss(t,e);if(!l)return n;let h=function(t,e,i){let s=Math.min(e.line,i.line),n=Math.max(e.line,i.line),r=[];if(e.off>2e3||i.off>2e3||e.col<0||i.col<0){let l=Math.min(e.off,i.off),h=Math.max(e.off,i.off);for(let e=s;e<=n;e++){let i=t.doc.line(e);i.length<=h&&r.push(o.OF.range(i.from+l,i.to+h))}}else{let l=Math.min(e.col,i.col),h=Math.max(e.col,i.col);for(let e=s;e<=n;e++){let i=t.doc.line(e),s=(0,o.kn)(i.text,l,t.tabSize,!0);if(s<0)r.push(o.OF.cursor(i.to));else{let e=(0,o.kn)(i.text,h,t.tabSize);r.push(o.OF.range(i.from+s,i.from+e))}}}return r}(t.state,s,l);return h.length?r?o.OF.create(h.concat(n.ranges)):o.OF.create(h):n}}:null):null})}let sn={Alt:[18,t=>!!t.altKey],Control:[17,t=>!!t.ctrlKey],Shift:[16,t=>!!t.shiftKey],Meta:[91,t=>!!t.metaKey]},sr={style:"cursor: crosshair"};function sl(t={}){let[e,i]=sn[t.key||"Alt"],s=t$.fromClass(class{constructor(t){this.view=t,this.isDown=!1}set(t){this.isDown!=t&&(this.isDown=t,this.view.update([]))}},{eventObservers:{keydown(t){this.set(t.keyCode==e||i(t))},keyup(t){t.keyCode!=e&&i(t)||this.set(!1)},mousemove(t){this.set(i(t))}}});return[s,ib.contentAttributes.of(t=>{var e;return(null==(e=t.plugin(s))?void 0:e.isDown)?sr:null})]}let sh="-10000px";class sa{constructor(t,e,i,s){this.facet=e,this.createTooltipView=i,this.removeTooltipView=s,this.input=t.state.facet(e),this.tooltips=this.input.filter(t=>t);let o=null;this.tooltipViews=this.tooltips.map(t=>o=i(t,o))}update(t,e){var i;let s=t.state.facet(this.facet),o=s.filter(t=>t);if(s===this.input){for(let e of this.tooltipViews)e.update&&e.update(t);return!1}let n=[],r=e?[]:null;for(let i=0;i<o.length;i++){let s=o[i],l=-1;if(s){for(let t=0;t<this.tooltips.length;t++){let e=this.tooltips[t];e&&e.create==s.create&&(l=t)}if(l<0)n[i]=this.createTooltipView(s,i?n[i-1]:null),r&&(r[i]=!!s.above);else{let s=n[i]=this.tooltipViews[l];r&&(r[i]=e[l]),s.update&&s.update(t)}}}for(let t of this.tooltipViews)0>n.indexOf(t)&&(this.removeTooltipView(t),null==(i=t.destroy)||i.call(t));return e&&(r.forEach((t,i)=>e[i]=t),e.length=r.length),this.input=s,this.tooltips=o,this.tooltipViews=n,!0}}function sc(t){let e=t.dom.ownerDocument.documentElement;return{top:0,left:0,bottom:e.clientHeight,right:e.clientWidth}}let sd=o.sj.define({combine:t=>{var e,i,s;return{position:Y.ios?"absolute":(null==(e=t.find(t=>t.position))?void 0:e.position)||"fixed",parent:(null==(i=t.find(t=>t.parent))?void 0:i.parent)||null,tooltipSpace:(null==(s=t.find(t=>t.tooltipSpace))?void 0:s.tooltipSpace)||sc}}}),su=new WeakMap,sf=t$.fromClass(class{constructor(t){this.view=t,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let e=t.state.facet(sd);this.position=e.position,this.parent=e.parent,this.classes=t.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.resizeObserver="function"==typeof ResizeObserver?new ResizeObserver(()=>this.measureSoon()):null,this.manager=new sa(t,sw,(t,e)=>this.createTooltip(t,e),t=>{this.resizeObserver&&this.resizeObserver.unobserve(t.dom),t.dom.remove()}),this.above=this.manager.tooltips.map(t=>!!t.above),this.intersectionObserver="function"==typeof IntersectionObserver?new IntersectionObserver(t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),t.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver)for(let t of(this.intersectionObserver.disconnect(),this.manager.tooltipViews))this.intersectionObserver.observe(t.dom)}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(t){t.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(t,this.above);e&&this.observeIntersection();let i=e||t.geometryChanged,s=t.state.facet(sd);if(s.position!=this.position&&!this.madeAbsolute){for(let t of(this.position=s.position,this.manager.tooltipViews))t.dom.style.position=this.position;i=!0}if(s.parent!=this.parent){for(let t of(this.parent&&this.container.remove(),this.parent=s.parent,this.createContainer(),this.manager.tooltipViews))this.container.appendChild(t.dom);i=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);i&&this.maybeMeasure()}createTooltip(t,e){let i=t.create(this.view),s=e?e.dom:null;if(i.dom.classList.add("cm-tooltip"),t.arrow&&!i.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow",i.dom.appendChild(t)}return i.dom.style.position=this.position,i.dom.style.top=sh,i.dom.style.left="0px",this.container.insertBefore(i.dom,s),i.mount&&i.mount(this.view),this.resizeObserver&&this.resizeObserver.observe(i.dom),i}destroy(){var t,e,i;for(let e of(this.view.win.removeEventListener("resize",this.measureSoon),this.manager.tooltipViews))e.dom.remove(),null==(t=e.destroy)||t.call(e);this.parent&&this.container.remove(),null==(e=this.resizeObserver)||e.disconnect(),null==(i=this.intersectionObserver)||i.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let t=1,e=1,i=!1;if("fixed"==this.position&&this.manager.tooltipViews.length){let{dom:t}=this.manager.tooltipViews[0];if(Y.gecko)i=t.offsetParent!=this.container.ownerDocument.body;else if(t.style.top==sh&&"0px"==t.style.left){let e=t.getBoundingClientRect();i=Math.abs(e.top+1e4)>1||Math.abs(e.left)>1}}if(i||"absolute"==this.position)if(this.parent){let i=this.parent.getBoundingClientRect();i.width&&i.height&&(t=i.width/this.parent.offsetWidth,e=i.height/this.parent.offsetHeight)}else({scaleX:t,scaleY:e}=this.view.viewState);let s=this.view.scrollDOM.getBoundingClientRect(),o=t9(this.view);return{visible:{left:s.left+o.left,top:s.top+o.top,right:s.right-o.right,bottom:s.bottom-o.bottom},parent:this.parent?this.container.getBoundingClientRect():this.view.dom.getBoundingClientRect(),pos:this.manager.tooltips.map((t,e)=>{let i=this.manager.tooltipViews[e];return i.getCoords?i.getCoords(t.pos):this.view.coordsAtPos(t.pos)}),size:this.manager.tooltipViews.map(({dom:t})=>t.getBoundingClientRect()),space:this.view.state.facet(sd).tooltipSpace(this.view),scaleX:t,scaleY:e,makeAbsolute:i}}writeMeasure(t){var e;if(t.makeAbsolute)for(let t of(this.madeAbsolute=!0,this.position="absolute",this.manager.tooltipViews))t.dom.style.position="absolute";let{visible:i,space:s,scaleX:o,scaleY:n}=t,r=[];for(let l=0;l<this.manager.tooltips.length;l++){let h=this.manager.tooltips[l],a=this.manager.tooltipViews[l],{dom:c}=a,d=t.pos[l],u=t.size[l];if(!d||!1!==h.clip&&(d.bottom<=Math.max(i.top,s.top)||d.top>=Math.min(i.bottom,s.bottom)||d.right<Math.max(i.left,s.left)-.1||d.left>Math.min(i.right,s.right)+.1)){c.style.top=sh;continue}let f=h.arrow?a.dom.querySelector(".cm-tooltip-arrow"):null,p=7*!!f,g=u.right-u.left,m=null!=(e=su.get(a))?e:u.bottom-u.top,w=a.offset||sm,v=this.view.textDirection==tm.LTR,b=u.width>s.right-s.left?v?s.left:s.right-u.width:v?Math.max(s.left,Math.min(d.left-14*!!f+w.x,s.right-g)):Math.min(Math.max(s.left,d.left-g+14*!!f-w.x),s.right-g),y=this.above[l];!h.strictSide&&(y?d.top-m-p-w.y<s.top:d.bottom+m+p+w.y>s.bottom)&&y==s.bottom-d.bottom>d.top-s.top&&(y=this.above[l]=!y);let x=(y?d.top-s.top:s.bottom-d.bottom)-p;if(x<m&&!1!==a.resize){if(x<this.view.defaultLineHeight){c.style.top=sh;continue}su.set(a,m),c.style.height=(m=x)/n+"px"}else c.style.height&&(c.style.height="");let S=y?d.top-m-p-w.y:d.bottom+p+w.y,M=b+g;if(!0!==a.overlap)for(let t of r)t.left<M&&t.right>b&&t.top<S+m&&t.bottom>S&&(S=y?t.top-m-2-p:t.bottom+p+2);if("absolute"==this.position?(c.style.top=(S-t.parent.top)/n+"px",sp(c,(b-t.parent.left)/o)):(c.style.top=S/n+"px",sp(c,b/o)),f){let t=d.left+(v?w.x:-w.x)-(b+14-7);f.style.left=t/o+"px"}!0!==a.overlap&&r.push({left:b,top:S,right:M,bottom:S+m}),c.classList.toggle("cm-tooltip-above",y),c.classList.toggle("cm-tooltip-below",!y),a.positioned&&a.positioned(t.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView)&&(this.inView=this.view.inView,!this.inView))for(let t of this.manager.tooltipViews)t.dom.style.top=sh}},{eventObservers:{scroll(){this.maybeMeasure()}}});function sp(t,e){let i=parseInt(t.style.left,10);(isNaN(i)||Math.abs(e-i)>1)&&(t.style.left=e+"px")}let sg=ib.baseTheme({".cm-tooltip":{zIndex:500,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:"14px",position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),sm={x:0,y:0},sw=o.sj.define({enables:[sf,sg]}),sv=o.sj.define({combine:t=>t.reduce((t,e)=>t.concat(e),[])});class sb{static create(t){return new sb(t)}constructor(t){this.view=t,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new sa(t,sv,(t,e)=>this.createHostedView(t,e),t=>t.dom.remove())}createHostedView(t,e){let i=t.create(this.view);return i.dom.classList.add("cm-tooltip-section"),this.dom.insertBefore(i.dom,e?e.dom.nextSibling:this.dom.firstChild),this.mounted&&i.mount&&i.mount(this.view),i}mount(t){for(let e of this.manager.tooltipViews)e.mount&&e.mount(t);this.mounted=!0}positioned(t){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned(t)}update(t){this.manager.update(t)}destroy(){var t;for(let e of this.manager.tooltipViews)null==(t=e.destroy)||t.call(e)}passProp(t){let e;for(let i of this.manager.tooltipViews){let s=i[t];if(void 0!==s){if(void 0===e)e=s;else if(e!==s)return}}return e}get offset(){return this.passProp("offset")}get getCoords(){return this.passProp("getCoords")}get overlap(){return this.passProp("overlap")}get resize(){return this.passProp("resize")}}let sy=sw.compute([sv],t=>{let e=t.facet(sv);return 0===e.length?null:{pos:Math.min(...e.map(t=>t.pos)),end:Math.max(...e.map(t=>{var e;return null!=(e=t.end)?e:t.pos})),create:sb.create,above:e[0].above,arrow:e.some(t=>t.arrow)}});class sx{constructor(t,e,i,s,o){this.view=t,this.source=e,this.field=i,this.setHover=s,this.hoverTime=o,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:t.dom,time:0},this.checkHover=this.checkHover.bind(this),t.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),t.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active.length)return;let t=Date.now()-this.lastMove.time;t<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-t):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{view:t,lastMove:e}=this,i=t.docView.nearest(e.target);if(!i)return;let s,o=1;if(i instanceof X)s=i.posAtStart;else{if(null==(s=t.posAtCoords(e)))return;let i=t.coordsAtPos(s);if(!i||e.y<i.top||e.y>i.bottom||e.x<i.left-t.defaultCharacterWidth||e.x>i.right+t.defaultCharacterWidth)return;let n=t.bidiSpans(t.state.doc.lineAt(s)).find(t=>t.from<=s&&t.to>=s),r=n&&n.dir==tm.RTL?-1:1;o=e.x<i.left?-r:r}let n=this.source(t,s,o);if(null==n?void 0:n.then){let e=this.pending={pos:s};n.then(i=>{this.pending==e&&(this.pending=null,i&&!(Array.isArray(i)&&!i.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(i)?i:[i])}))},e=>tY(t.state,e,"hover tooltip"))}else n&&!(Array.isArray(n)&&!n.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(n)?n:[n])})}get tooltip(){let t=this.view.plugin(sf),e=t?t.manager.tooltips.findIndex(t=>t.create==sb.create):-1;return e>-1?t.manager.tooltipViews[e]:null}mousemove(t){var e,i;this.lastMove={x:t.clientX,y:t.clientY,target:t.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let{active:s,tooltip:o}=this;if(s.length&&o&&!function(t,e){let{left:i,right:s,top:o,bottom:n}=t.getBoundingClientRect(),r;if(r=t.querySelector(".cm-tooltip-arrow")){let t=r.getBoundingClientRect();o=Math.min(t.top,o),n=Math.max(t.bottom,n)}return e.clientX>=i-4&&e.clientX<=s+4&&e.clientY>=o-4&&e.clientY<=n+4}(o.dom,t)||this.pending){let{pos:o}=s[0]||this.pending,n=null!=(i=null==(e=s[0])?void 0:e.end)?i:o;(o==n?this.view.posAtCoords(this.lastMove)!=o:!function(t,e,i,s,o,n){let r=t.scrollDOM.getBoundingClientRect(),l=t.documentTop+t.documentPadding.top+t.contentHeight;if(r.left>s||r.right<s||r.top>o||Math.min(r.bottom,l)<o)return!1;let h=t.posAtCoords({x:s,y:o},!1);return h>=e&&h<=i}(this.view,o,n,t.clientX,t.clientY))&&(this.view.dispatch({effects:this.setHover.of([])}),this.pending=null)}}mouseleave(t){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1;let{active:e}=this;if(e.length){let{tooltip:e}=this;e&&e.dom.contains(t.relatedTarget)?this.watchTooltipLeave(e.dom):this.view.dispatch({effects:this.setHover.of([])})}}watchTooltipLeave(t){let e=i=>{t.removeEventListener("mouseleave",e),this.active.length&&!this.view.dom.contains(i.relatedTarget)&&this.view.dispatch({effects:this.setHover.of([])})};t.addEventListener("mouseleave",e)}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}function sS(t,e={}){let i=o.Pe.define(),s=o.sU.define({create:()=>[],update(t,s){if(t.length&&(e.hideOnChange&&(s.docChanged||s.selection)?t=[]:e.hideOn&&(t=t.filter(t=>!e.hideOn(s,t))),s.docChanged)){let e=[];for(let i of t){let t=s.changes.mapPos(i.pos,-1,o.iR.TrackDel);if(null!=t){let o=Object.assign(Object.create(null),i);o.pos=t,null!=o.end&&(o.end=s.changes.mapPos(o.end)),e.push(o)}}t=e}for(let e of s.effects)e.is(i)&&(t=e.value),e.is(sC)&&(t=[]);return t},provide:t=>sv.from(t)});return{active:s,extension:[s,t$.define(o=>new sx(o,t,s,i,e.hoverTime||300)),sy]}}function sM(t,e){let i=t.plugin(sf);if(!i)return null;let s=i.manager.tooltips.indexOf(e);return s<0?null:i.manager.tooltipViews[s]}let sC=o.Pe.define(),sk=o.sj.define({combine(t){let e,i;for(let s of t)e=e||s.topContainer,i=i||s.bottomContainer;return{topContainer:e,bottomContainer:i}}});function sA(t,e){let i=t.plugin(sO),s=i?i.specs.indexOf(e):-1;return s>-1?i.panels[s]:null}let sO=t$.fromClass(class{constructor(t){this.input=t.state.facet(sE),this.specs=this.input.filter(t=>t),this.panels=this.specs.map(e=>e(t));let e=t.state.facet(sk);for(let i of(this.top=new sD(t,!0,e.topContainer),this.bottom=new sD(t,!1,e.bottomContainer),this.top.sync(this.panels.filter(t=>t.top)),this.bottom.sync(this.panels.filter(t=>!t.top)),this.panels))i.dom.classList.add("cm-panel"),i.mount&&i.mount()}update(t){let e=t.state.facet(sk);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new sD(t.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new sD(t.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let i=t.state.facet(sE);if(i!=this.input){let e=i.filter(t=>t),s=[],o=[],n=[],r=[];for(let i of e){let e=this.specs.indexOf(i),l;e<0?(l=i(t.view),r.push(l)):(l=this.panels[e]).update&&l.update(t),s.push(l),(l.top?o:n).push(l)}for(let t of(this.specs=e,this.panels=s,this.top.sync(o),this.bottom.sync(n),r))t.dom.classList.add("cm-panel"),t.mount&&t.mount()}else for(let e of this.panels)e.update&&e.update(t)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:t=>ib.scrollMargins.of(e=>{let i=e.plugin(t);return i&&{top:i.top.scrollMargin(),bottom:i.bottom.scrollMargin()}})});class sD{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&0>t.indexOf(e)&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(0==this.panels.length){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=sT(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=sT(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(this.container&&this.classes!=this.view.themeClasses){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function sT(t){let e=t.nextSibling;return t.remove(),e}let sE=o.sj.define({enables:sO}),sR=o.Pe.define(),sB=o.Pe.define();class sL extends o.FB{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}sL.prototype.elementClass="",sL.prototype.toDOM=void 0,sL.prototype.mapMode=o.iR.TrackBefore,sL.prototype.startSide=sL.prototype.endSide=-1,sL.prototype.point=!0;let sP=o.sj.define(),sH=o.sj.define(),sN={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>o.om.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},sV=o.sj.define();function sF(t){return[sz(),sV.of({...sN,...t})]}let sW=o.sj.define({combine:t=>t.some(t=>t)});function sz(t){let e=[sK];return t&&!1===t.fixed&&e.push(sW.of(!0)),e}let sK=t$.fromClass(class{constructor(t){for(let e of(this.view=t,this.prevViewport=t.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=t.state.facet(sV).map(e=>new sY(t,e)),this.gutters))this.dom.appendChild(e.dom);this.fixed=!t.state.facet(sW),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),t.scrollDOM.insertBefore(this.dom,t.contentDOM)}update(t){if(this.updateGutters(t)){let e=this.prevViewport,i=t.view.viewport,s=Math.min(e.to,i.to)-Math.max(e.from,i.from);this.syncGutters(s<(i.to-i.from)*.8)}t.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px"),this.view.state.facet(sW)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=t.view.viewport}syncGutters(t){let e=this.dom.nextSibling;t&&this.dom.remove();let i=o.om.iter(this.view.state.facet(sP),this.view.viewport.from),s=[],n=this.gutters.map(t=>new sj(t,this.view.viewport,-this.view.documentPadding.top));for(let t of this.view.viewportLineBlocks)if(s.length&&(s=[]),Array.isArray(t.type)){let e=!0;for(let o of t.type)if(o.type==ts.Text&&e){for(let t of(sq(i,s,o.from),n))t.line(this.view,o,s);e=!1}else if(o.widget)for(let t of n)t.widget(this.view,o)}else if(t.type==ts.Text)for(let e of(sq(i,s,t.from),n))e.line(this.view,t,s);else if(t.widget)for(let e of n)e.widget(this.view,t);for(let t of n)t.finish();t&&this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(t){let e=t.startState.facet(sV),i=t.state.facet(sV),s=t.docChanged||t.heightChanged||t.viewportChanged||!o.om.eq(t.startState.facet(sP),t.state.facet(sP),t.view.viewport.from,t.view.viewport.to);if(e==i)for(let e of this.gutters)e.update(t)&&(s=!0);else{s=!0;let o=[];for(let s of i){let i=e.indexOf(s);i<0?o.push(new sY(this.view,s)):(this.gutters[i].update(t),o.push(this.gutters[i]))}for(let t of this.gutters)t.dom.remove(),0>o.indexOf(t)&&t.destroy();for(let t of o)this.dom.appendChild(t.dom);this.gutters=o}return s}destroy(){for(let t of this.gutters)t.destroy();this.dom.remove()}},{provide:t=>ib.scrollMargins.of(e=>{let i=e.plugin(t);return i&&0!=i.gutters.length&&i.fixed?e.textDirection==tm.LTR?{left:i.dom.offsetWidth*e.scaleX}:{right:i.dom.offsetWidth*e.scaleX}:null})});function sI(t){return Array.isArray(t)?t:[t]}function sq(t,e,i){for(;t.value&&t.from<=i;)t.from==i&&e.push(t.value),t.next()}class sj{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=o.om.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:s}=this,o=(e.top-this.height)/t.scaleY,n=e.height/t.scaleY;if(this.i==s.elements.length){let e=new sG(t,n,o,i);s.elements.push(e),s.dom.appendChild(e.dom)}else s.elements[this.i].update(t,n,o,i);this.height=e.bottom,this.i++}line(t,e,i){let s=[];sq(this.cursor,s,e.from),i.length&&(s=s.concat(i));let o=this.gutter.config.lineMarker(t,e,s);o&&s.unshift(o);let n=this.gutter;(0!=s.length||n.config.renderEmptyElements)&&this.addElement(t,e,s)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e),s=i?[i]:null;for(let i of t.state.facet(sH)){let o=i(t,e.widget,e);o&&(s||(s=[])).push(o)}s&&this.addElement(t,e,s)}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class sY{constructor(t,e){for(let i in this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:""),e.domEventHandlers)this.dom.addEventListener(i,s=>{let o=s.target,n;if(o!=this.dom&&this.dom.contains(o)){for(;o.parentNode!=this.dom;)o=o.parentNode;let t=o.getBoundingClientRect();n=(t.top+t.bottom)/2}else n=s.clientY;let r=t.lineBlockAtHeight(n-t.documentTop);e.domEventHandlers[i](t,r,s)&&s.preventDefault()});this.markers=sI(e.markers(t)),e.initialSpacer&&(this.spacer=new sG(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=sI(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let e=this.config.updateSpacer(this.spacer.markers[0],t);e!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[e])}let i=t.view.viewport;return!o.om.eq(this.markers,e,i.from,i.to)||!!this.config.lineMarkerChange&&this.config.lineMarkerChange(t)}destroy(){for(let t of this.elements)t.destroy()}}class sG{constructor(t,e,i,s){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,s)}update(t,e,i,s){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),!function(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!t[i].compare(e[i]))return!1;return!0}(this.markers,s)&&this.setMarkers(t,s)}setMarkers(t,e){let i="cm-gutterElement",s=this.dom.firstChild;for(let o=0,n=0;;){let r=n,l=o<e.length?e[o++]:null,h=!1;if(l){let t=l.elementClass;t&&(i+=" "+t);for(let t=n;t<this.markers.length;t++)if(this.markers[t].compare(l)){r=t,h=!0;break}}else r=this.markers.length;for(;n<r;){let t=this.markers[n++];if(t.toDOM){t.destroy(s);let e=s.nextSibling;s.remove(),s=e}}if(!l)break;l.toDOM&&(h?s=s.nextSibling:this.dom.insertBefore(l.toDOM(t),s)),h&&n++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}let s_=o.sj.define(),sX=o.sj.define(),s$=o.sj.define({combine:t=>(0,o.QR)(t,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let t in e){let s=i[t],o=e[t];i[t]=s?(t,e,i)=>s(t,e,i)||o(t,e,i):o}return i}})});class sU extends sL{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function sQ(t,e){return t.state.facet(s$).formatNumber(e,t.state)}let sJ=sV.compute([s$],t=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers:t=>t.state.facet(s_),lineMarker:(t,e,i)=>i.some(t=>t.toDOM)?null:new sU(sQ(t,t.state.doc.lineAt(e.from).number)),widgetMarker:(t,e,i)=>{for(let s of t.state.facet(sX)){let o=s(t,e,i);if(o)return o}return null},lineMarkerChange:t=>t.startState.facet(s$)!=t.state.facet(s$),initialSpacer:t=>new sU(sQ(t,s0(t.state.doc.lines))),updateSpacer(t,e){let i=sQ(e.view,s0(e.view.state.doc.lines));return i==t.number?t:new sU(i)},domEventHandlers:t.facet(s$).domEventHandlers}));function sZ(t={}){return[s$.of(t),sz(),sJ]}function s0(t){let e=9;for(;e<t;)e=10*e+9;return e}let s1=new class extends sL{constructor(){super(...arguments),this.elementClass="cm-activeLineGutter"}},s2=sP.compute(["selection"],t=>{let e=[],i=-1;for(let s of t.selection.ranges){let o=t.doc.lineAt(s.head).from;o>i&&(i=o,e.push(s1.range(o)))}return o.om.of(e)});function s8(){return s2}function s3(t){return t$.define(e=>({decorations:t.createDeco(e),update(e){this.decorations=t.updateDeco(e,this.decorations)}}),{decorations:t=>t.decorations})}let s9=to.mark({class:"cm-highlightTab"}),s5=to.mark({class:"cm-highlightSpace"})}}]);