"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6143],{66143:(a,r,t)=>{t.r(r),t.d(r,{Separator:()=>c});var o=t(53891),e=t(73987),i=t(4513),n="horizontal",l=["horizontal","vertical"],s=e.forwardRef((a,r)=>{var t;let{decorative:e,orientation:s=n,...d}=a,c=(t=s,l.includes(t))?s:n;return(0,o.jsx)(i.sG.div,{"data-orientation":c,...e?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:r})});s.displayName="Separator";var d=t(61971);function c(a){let{className:r,orientation:t="horizontal",decorative:e=!0,...i}=a;return(0,o.jsx)(s,{"data-slot":"separator-root",decorative:e,orientation:t,className:(0,d.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=vertical]:h-full data-[orientation=horizontal]:w-full data-[orientation=vertical]:w-px",r),...i})}}}]);