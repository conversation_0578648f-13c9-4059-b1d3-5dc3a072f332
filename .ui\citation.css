/* 引用系统样式 - 独立模块 */

/* 引用数字徽章 */
.citation {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  margin: 0 2px;
  position: relative;
  transition: all 0.3s ease;
  text-decoration: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 20px;
  height: 20px;
  line-height: 16px;
  text-align: center;
  vertical-align: baseline;
}

.citation:hover {
  transform: translateY(-1px) scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 引用悬浮窗 */
.citation-tooltip {
  visibility: hidden;
  opacity: 0;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.95) !important;
  color: white !important;
  padding: 20px;
  border-radius: 12px;
  font-size: 14px !important;
  z-index: 10000;
  transition: all 0.3s ease;
  width: 500px;
  max-width: 90vw;
  max-height: 70vh;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
  line-height: 1.5 !important;
  white-space: normal !important;
  pointer-events: none;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 强制所有子元素继承颜色 */
.citation-tooltip * {
  color: inherit !important;
  font-family: inherit !important;
}

.citation-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.95);
}

.citation:hover .citation-tooltip {
  visibility: visible;
  opacity: 1;
  transform: translateX(-50%) translateY(-4px);
  pointer-events: auto;
}

/* 悬浮窗内容结构 */
.tooltip-header {
  font-weight: bold !important;
  font-size: 16px !important;
  color: #4fc3f7 !important;
  margin-bottom: 10px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 8px;
  text-align: center;
}

.tooltip-meta {
  font-size: 12px !important;
  color: #b0bec5 !important;
  margin-bottom: 15px;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px;
  border-radius: 6px;
}

.tooltip-content {
  font-size: 13px !important;
  line-height: 1.6 !important;
  color: #e0e0e0 !important;
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  white-space: pre-wrap !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 滚动条样式 */
.tooltip-content::-webkit-scrollbar {
  width: 8px;
}

.tooltip-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.tooltip-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 4px;
}

.tooltip-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.6);
}

/* 关闭按钮 */
.tooltip-close {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  color: #ff6b6b !important;
  font-size: 20px !important;
  cursor: pointer;
  padding: 0;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.tooltip-close:hover {
  background: rgba(255, 107, 107, 0.2);
  transform: scale(1.1);
}

/* 调试样式 */
.citation-debug {
  border: 2px solid red !important;
  background: red !important;
}

.tooltip-debug {
  border: 2px solid yellow !important;
  background: rgba(0, 0, 0, 0.95) !important;
  color: white !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.tooltip-debug * {
  color: white !important;
}
