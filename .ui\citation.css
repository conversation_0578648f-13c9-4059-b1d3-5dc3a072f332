/* 引用系统样式 - 独立模块 */

/* 引用数字徽章 */
.citation {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  margin: 0 2px;
  position: relative;
  transition: all 0.3s ease;
  text-decoration: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 20px;
  height: 20px;
  line-height: 16px;
  text-align: center;
  vertical-align: baseline;
}

.citation:hover {
  transform: translateY(-1px) scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 引用提示框 */
.citation-tooltip {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.95);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 1000;
  transition: all 0.3s ease;
  margin-bottom: 8px;
  min-width: 300px;
  max-width: 400px;
  max-height: 300px;
  overflow: visible;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  line-height: 1.4;
  white-space: normal;
  pointer-events: none;
}

.citation-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.95);
}

.citation:hover .citation-tooltip {
  visibility: visible;
  opacity: 1;
  transform: translateX(-50%) translateY(-4px);
  pointer-events: auto;
}

/* 提示框内容结构 */
.tooltip-header {
  font-weight: bold;
  font-size: 13px;
  color: #4fc3f7;
  margin-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 4px;
}

.tooltip-meta {
  font-size: 10px;
  color: #b0bec5;
  margin-bottom: 8px;
}

.tooltip-content {
  font-size: 11px;
  line-height: 1.5;
  color: #e0e0e0;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 4px;
  white-space: pre-wrap;
}

/* 滚动条样式 */
.tooltip-content::-webkit-scrollbar {
  width: 4px;
}

.tooltip-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.tooltip-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.tooltip-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 调试样式 */
.citation-debug {
  border: 2px solid red !important;
  background: red !important;
}

.tooltip-debug {
  border: 2px solid yellow !important;
  background: yellow !important;
  color: black !important;
}
