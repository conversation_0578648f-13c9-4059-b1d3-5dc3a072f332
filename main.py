import logging

from app.settings import init_settings
from app.workflow import create_workflow
from dotenv import load_dotenv
from llama_index.server import LlamaIndexServer, UIConfig
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

logger = logging.getLogger("uvicorn")

# A path to a directory where the customized UI code is stored
COMPONENT_DIR = "components"


def create_app():
    app = LlamaIndexServer(
        workflow_factory=create_workflow,
        ui_config=UIConfig(
            component_dir=COMPONENT_DIR,
            dev_mode=True,
            layout_dir="layout",
        ),
        logger=logger,
        env="dev",
    )

    # 添加静态文件服务
    app.mount("/static", StaticFiles(directory=".ui"), name="static")

    # 保留原有的健康检查路由
    app.add_api_route("/api/health", lambda: {"message": "OK"}, status_code=200)

    return app


load_dotenv()
init_settings()
app = create_app()
