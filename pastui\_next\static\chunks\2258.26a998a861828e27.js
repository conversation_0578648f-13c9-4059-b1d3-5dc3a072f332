(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2258],{59634:(e,t,n)=>{var r=0/0,o=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,a=/^0o[0-7]+$/i,l=parseInt,c="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,u="object"==typeof self&&self&&self.Object===Object&&self,d=c||u||Function("return this")(),f=Object.prototype.toString,h=Math.max,p=Math.min,g=function(){return d.Date.now()};function m(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function v(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==f.call(t))return r;if(m(e)){var t,n="function"==typeof e.valueOf?e.valueOf():e;e=m(n)?n+"":n}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(o,"");var c=s.test(e);return c||a.test(e)?l(e.slice(2),c?2:8):i.test(e)?r:+e}e.exports=function(e,t,n){var r,o,i,s,a,l,c=0,u=!1,d=!1,f=!0;if("function"!=typeof e)throw TypeError("Expected a function");function y(t){var n=r,i=o;return r=o=void 0,c=t,s=e.apply(i,n)}function x(e){var n=e-l,r=e-c;return void 0===l||n>=t||n<0||d&&r>=i}function E(){var e,n,r,o=g();if(x(o))return S(o);a=setTimeout(E,(e=o-l,n=o-c,r=t-e,d?p(r,i-n):r))}function S(e){return(a=void 0,f&&r)?y(e):(r=o=void 0,s)}function b(){var e,n=g(),i=x(n);if(r=arguments,o=this,l=n,i){if(void 0===a)return c=e=l,a=setTimeout(E,t),u?y(e):s;if(d)return a=setTimeout(E,t),y(l)}return void 0===a&&(a=setTimeout(E,t)),s}return t=v(t)||0,m(n)&&(u=!!n.leading,i=(d="maxWait"in n)?h(v(n.maxWait)||0,t):i,f="trailing"in n?!!n.trailing:f),b.cancel=function(){void 0!==a&&clearTimeout(a),c=0,r=l=o=a=void 0},b.flush=function(){return void 0===a?s:S(g())},b}},78993:e=>{"use strict";e.exports=function(){}},82258:(e,t,n)=>{"use strict";n.r(t),n.d(t,{MemoizedVirtualizedPDF:()=>tV,PDFOptionsBar:()=>tZ,PDFViewer:()=>tJ,PdfFocusProvider:()=>l});var r,o,i=n(53891),s=n(73987);let a=(0,s.createContext)(void 0),l=({children:e})=>{let[t,n]=(0,s.useState)({documentId:"",pageNumber:0});return(0,i.jsx)(a.Provider,{value:{pdfFocusState:t,setPdfFocusState:n},children:e})},c=()=>{let e=(0,s.useContext)(a);if(void 0===e)throw Error("usePDF must be used within a PDFProvider");return e},u=["50%","80%","100%","130%","200%","300%","400%"],d=e=>{let t,[n,r]=(0,s.useState)(1),[o,i]=(0,s.useState)(1),[a,l]=(0,s.useState)(1),[d,f]=(0,s.useState)(0),[h,p]=(0,s.useState)(!1),[g,m]=(0,s.useState)(2),{pdfFocusState:v}=c(),y=s.useRef(null),x=e=>{y.current&&y.current.scrollToPage(e)};(0,s.useEffect)(()=>{v.documentId===e.id&&v.pageNumber&&x(v.pageNumber-1)},[e,v]);let E=(0,s.useCallback)(e=>{r(e)},[]),S=(0,s.useCallback)(()=>{let e=g+1;e>=u.length||w(u[e]||"100%")},[g,n,y]),b=(0,s.useCallback)(()=>{let e=g-1;e<0||w(u[e]||"100%")},[g,n,y]),w=(0,s.useCallback)(e=>{let t=u.indexOf(e);i(parseInt(e,10)/100+a-1),setTimeout(()=>{x(n)},30),m(t)},[n]),R=(t=Math.round((o-a)*10)/10,`${100+100*t}%`);return(0,s.useEffect)(()=>{i(a)},[a]),{scrolledIndex:n,setCurrentPageNumber:E,scale:o,setScaleFit:l,numPages:d,setNumPages:f,handleZoomIn:S,handleZoomOut:b,nextPage:()=>{x(n+1)},prevPage:()=>{x(n-1)},scaleText:R,isPdfRendered:h,setIsPdfRendered:p,pdfFocusRef:y,goToPage:x,setZoomLevel:w,zoomInEnabled:g<u.length-1,zoomOutEnabled:g>0}};function f(e,t,n){(0,s.useEffect)(function(){if(e)return e.addEventListener(t,n),function(){e.removeEventListener(t,n)}},[e,t,n])}var h="undefined"!=typeof document,p="undefined"!=typeof document,g=n(59634),m=new Map,v=new WeakMap,y=0,x=void 0;s.Component;var E=n(6243);function S(e){var t=!1;return{promise:new Promise(function(n,r){e.then(function(e){return!t&&n(e)}).catch(function(e){return!t&&r(e)})}),cancel:function(){t=!0}}}var b=function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},w=b(b(b(b(b(b(b(b(b(b(b(b(b(b(b(b(b(b([],["onCopy","onCut","onPaste"],!0),["onCompositionEnd","onCompositionStart","onCompositionUpdate"],!0),["onFocus","onBlur"],!0),["onInput","onInvalid","onReset","onSubmit"],!0),["onLoad","onError"],!0),["onKeyDown","onKeyPress","onKeyUp"],!0),["onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onError","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting"],!0),["onClick","onContextMenu","onDoubleClick","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp"],!0),["onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop"],!0),["onSelect"],!0),["onTouchCancel","onTouchEnd","onTouchMove","onTouchStart"],!0),["onPointerDown","onPointerMove","onPointerUp","onPointerCancel","onGotPointerCapture","onLostPointerCapture","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut"],!0),["onScroll"],!0),["onWheel"],!0),["onAnimationStart","onAnimationEnd","onAnimationIteration"],!0),["onTransitionEnd"],!0),["onChange"],!0),["onToggle"],!0);function R(e,t){var n={};return w.forEach(function(r){var o=e[r];o&&(t?n[r]=function(e){return o(e,t(r))}:n[r]=o)}),n}var I=n(53184);function _(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.filter(Boolean);return n.length<=1?n[0]||null:function(e){n.forEach(function(t){"function"==typeof t?t(e):t&&(t.current=e)})}}function C(e,t){if(!e)throw Error("Invariant failed")}var T=n(78993);let P=(0,s.createContext)(null);function M({children:e,type:t}){return(0,i.jsx)("div",{className:`react-pdf__message react-pdf__message--${t}`,children:e})}let O={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null},j=/^H(\d+)$/;function k(e){return"children"in e}function L(e){return!!k(e)&&1===e.children.length&&0 in e.children&&"id"in e.children[0]}function A({className:e,node:t}){let n=(0,s.useMemo)(()=>t?Object.assign(Object.assign({},function(e){let t={};if(k(e)){let{role:n}=e,r=n.match(j);if(r)t.role="heading",t["aria-level"]=Number(r[1]);else if(n in O){let e=O[n];e&&(t.role=e)}}return t}(t)),function e(t){let n={};if(k(t)){if(void 0!==t.alt&&(n["aria-label"]=t.alt),void 0!==t.lang&&(n.lang=t.lang),L(t)){let[r]=t.children;if(r){let t=e(r);return Object.assign(Object.assign({},n),t)}}}else"id"in t&&(n["aria-owns"]=t.id);return n}(t)):null,[t]),r=(0,s.useMemo)(()=>!k(t)||L(t)?null:t.children.map((e,t)=>(0,i.jsx)(A,{node:e},t)),[t]);return(0,i.jsx)("span",Object.assign({className:e},n,{children:r}))}function D(){return(0,s.useContext)(P)}function N(e,t){switch(t.type){case"RESOLVE":return{value:t.value,error:void 0};case"REJECT":return{value:!1,error:t.error};case"RESET":return{value:void 0,error:void 0};default:return e}}function F(){return(0,s.useReducer)(N,{value:void 0,error:void 0})}let z="undefined"!=typeof window,W=z&&"file:"===window.location.protocol;function $(e){return null!=e}function V(e){return"string"==typeof e&&/^data:/.test(e)}function H(e){C(V(e),"Invalid data URI.");let[t="",n=""]=e.split(",");return -1!==t.split(";").indexOf("base64")?atob(n):unescape(n)}function G(){T(!W,"Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.")}function U(e){(null==e?void 0:e.cancel)&&e.cancel()}function B(e,t){return Object.defineProperty(e,"width",{get(){return this.view[2]*t},configurable:!0}),Object.defineProperty(e,"height",{get(){return this.view[3]*t},configurable:!0}),Object.defineProperty(e,"originalWidth",{get(){return this.view[2]},configurable:!0}),Object.defineProperty(e,"originalHeight",{get(){return this.view[3]},configurable:!0}),e}function q(){let e=D();C(e,"Unable to find Page context.");let{onGetStructTreeError:t,onGetStructTreeSuccess:n}=e,[r,o]=F(),{value:a,error:l}=r,{customTextRenderer:c,page:u}=e;return((0,s.useEffect)(function(){o({type:"RESET"})},[o,u]),(0,s.useEffect)(function(){if(c||!u)return;let e=S(u.getStructTree());return e.promise.then(e=>{o({type:"RESOLVE",value:e})}).catch(e=>{o({type:"REJECT",error:e})}),()=>U(e)},[c,u,o]),(0,s.useEffect)(()=>{if(void 0!==a){if(!1===a)return void(l&&(T(!1,l.toString()),t&&t(l)));a&&n&&n(a)}},[a]),a)?(0,i.jsx)(A,{className:"react-pdf__Page__structTree structTree",node:a}):null}let Z=E.ng;function J(e){let t=D();C(t,"Unable to find Page context.");let{_className:n,canvasBackground:r,devicePixelRatio:o=z&&window.devicePixelRatio||1,onRenderError:a,onRenderSuccess:l,page:c,renderForms:u,renderTextLayer:d,rotate:f,scale:h}=Object.assign(Object.assign({},t),e),{canvasRef:p}=e;C(c,"Attempted to render page canvas, but no page was specified.");let g=(0,s.useRef)(null);function m(e){"RenderingCancelledException"!==e.name&&(T(!1,e.toString()),a&&a(e))}let v=(0,s.useMemo)(()=>c.getViewport({scale:h*o,rotation:f}),[o,c,f,h]),y=(0,s.useMemo)(()=>c.getViewport({scale:h,rotation:f}),[c,f,h]);(0,s.useEffect)(function(){if(!c)return;c.cleanup();let{current:e}=g;if(!e)return;e.width=v.width,e.height=v.height,e.style.width="".concat(Math.floor(y.width),"px"),e.style.height="".concat(Math.floor(y.height),"px"),e.style.visibility="hidden";let t={annotationMode:u?Z.ENABLE_FORMS:Z.ENABLE,canvasContext:e.getContext("2d",{alpha:!1}),viewport:v};r&&(t.background=r);let n=c.render(t);return n.promise.then(()=>{e.style.visibility="",c&&l&&l(B(c,h))}).catch(m),()=>U(n)},[r,c,u,v,y]);let x=(0,s.useCallback)(()=>{let{current:e}=g;e&&(e.width=0,e.height=0)},[]);return(0,s.useEffect)(()=>x,[x]),(0,i.jsx)("canvas",{className:"".concat(n,"__canvas"),dir:"ltr",ref:_(p,g),style:{display:"block",userSelect:"none"},children:d?(0,i.jsx)(q,{}):null})}function K(){let e=D();C(e,"Unable to find Page context.");let{customTextRenderer:t,onGetTextError:n,onGetTextSuccess:r,onRenderTextLayerError:o,onRenderTextLayerSuccess:a,page:l,pageIndex:c,pageNumber:u,rotate:d,scale:f}=e;C(l,"Attempted to load page text content, but no page was specified.");let[h,p]=F(),{value:g,error:m}=h,v=(0,s.useRef)(null);T(1===Number.parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-text-layer"),10),"TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer"),(0,s.useEffect)(function(){p({type:"RESET"})},[l,p]),(0,s.useEffect)(function(){if(!l)return;let e=S(l.getTextContent());return e.promise.then(e=>{p({type:"RESOLVE",value:e})}).catch(e=>{p({type:"REJECT",error:e})}),()=>U(e)},[l,p]),(0,s.useEffect)(()=>{if(void 0!==g){if(!1===g)return void(m&&(T(!1,m.toString()),n&&n(m)));g&&r&&r(g)}},[g]);let y=(0,s.useCallback)(()=>{a&&a()},[a]),x=(0,s.useCallback)(e=>{T(!1,e.toString()),o&&o(e)},[o]),b=(0,s.useMemo)(()=>l.getViewport({scale:f,rotation:d}),[l,d,f]);return(0,s.useLayoutEffect)(function(){if(!l||!g)return;let{current:e}=v;if(!e)return;e.innerHTML="";let n=l.streamTextContent({includeMarkedContent:!0}),r=new E.D6({container:e,textContentSource:n,viewport:b});return r.render().then(()=>{let n=document.createElement("div");n.className="endOfContent",e.append(n);let r=e.querySelectorAll('[role="presentation"]');if(t){let e=0;g.items.forEach((n,o)=>{if(!("str"in n))return;let i=r[e];i&&(i.innerHTML=t(Object.assign({pageIndex:c,pageNumber:u,itemIndex:o},n)),e+=n.str&&n.hasEOL?2:1)})}y()}).catch(x),()=>U(r)},[t,x,y,l,c,u,g,b]),(0,i.jsx)("div",{className:(0,I.A)("react-pdf__Page__textContent","textLayer"),onMouseUp:function(){let e=v.current;e&&e.classList.remove("selecting")},onMouseDown:function(){let e=v.current;e&&e.classList.add("selecting")},ref:v})}let X=(0,s.createContext)(null);function Y(){return(0,s.useContext)(X)}function Q(){let e=Y(),t=D();C(t,"Unable to find Page context.");let{imageResourcesPath:n,linkService:r,onGetAnnotationsError:o,onGetAnnotationsSuccess:a,onRenderAnnotationLayerError:l,onRenderAnnotationLayerSuccess:c,page:u,pdf:d,renderForms:f,rotate:h,scale:p=1}=Object.assign(Object.assign({},e),t);C(d,"Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop."),C(u,"Attempted to load page annotations, but no page was specified."),C(r,"Attempted to load page annotations, but no linkService was specified.");let[g,m]=F(),{value:v,error:y}=g,x=(0,s.useRef)(null);T(1===Number.parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-annotation-layer"),10),"AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations"),(0,s.useEffect)(function(){m({type:"RESET"})},[m,u]),(0,s.useEffect)(function(){if(!u)return;let e=S(u.getAnnotations());return e.promise.then(e=>{m({type:"RESOLVE",value:e})}).catch(e=>{m({type:"REJECT",error:e})}),()=>{U(e)}},[m,u]),(0,s.useEffect)(()=>{if(void 0!==v){if(!1===v)return void(y&&(T(!1,y.toString()),o&&o(y)));v&&a&&a(v)}},[v]);let b=(0,s.useMemo)(()=>u.getViewport({scale:p,rotation:h}),[u,h,p]);return(0,s.useEffect)(function(){if(!d||!u||!r||!v)return;let{current:e}=x;if(!e)return;let t=b.clone({dontFlip:!0}),o={annotations:v,annotationStorage:d.annotationStorage,div:e,imageResourcesPath:n,linkService:r,page:u,renderForms:f,viewport:t};e.innerHTML="";try{new E.dU({accessibilityManager:null,annotationCanvasMap:null,annotationEditorUIManager:null,div:e,l10n:null,page:u,structTreeLayer:null,viewport:t}).render(o),c&&c()}catch(e){T(!1,"".concat(e)),l&&l(e)}return()=>{}},[v,n,r,u,d,f,b]),(0,i.jsx)("div",{className:(0,I.A)("react-pdf__Page__annotations","annotationLayer"),ref:x})}var ee=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function et(e){let t=Object.assign(Object.assign({},Y()),e),{_className:n="react-pdf__Page",_enableRegisterUnregisterPage:r=!0,canvasBackground:o,canvasRef:a,children:l,className:c,customRenderer:u,customTextRenderer:d,devicePixelRatio:f,error:h="Failed to load the page.",height:p,inputRef:g,loading:m="Loading page…",noData:v="No page specified.",onGetAnnotationsError:y,onGetAnnotationsSuccess:x,onGetStructTreeError:E,onGetStructTreeSuccess:b,onGetTextError:w,onGetTextSuccess:O,onLoadError:j,onLoadSuccess:k,onRenderAnnotationLayerError:L,onRenderAnnotationLayerSuccess:A,onRenderError:D,onRenderSuccess:N,onRenderTextLayerError:z,onRenderTextLayerSuccess:W,pageIndex:V,pageNumber:H,pdf:G,registerPage:q,renderAnnotationLayer:Z=!0,renderForms:X=!1,renderMode:et="canvas",renderTextLayer:en=!0,rotate:er,scale:eo=1,unregisterPage:ei,width:es}=t,ea=ee(t,["_className","_enableRegisterUnregisterPage","canvasBackground","canvasRef","children","className","customRenderer","customTextRenderer","devicePixelRatio","error","height","inputRef","loading","noData","onGetAnnotationsError","onGetAnnotationsSuccess","onGetStructTreeError","onGetStructTreeSuccess","onGetTextError","onGetTextSuccess","onLoadError","onLoadSuccess","onRenderAnnotationLayerError","onRenderAnnotationLayerSuccess","onRenderError","onRenderSuccess","onRenderTextLayerError","onRenderTextLayerSuccess","pageIndex","pageNumber","pdf","registerPage","renderAnnotationLayer","renderForms","renderMode","renderTextLayer","rotate","scale","unregisterPage","width"]),[el,ec]=F(),{value:eu,error:ed}=el,ef=(0,s.useRef)(null);C(G,"Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.");let eh=$(H)?H-1:null!=V?V:null,ep=null!=H?H:$(V)?V+1:null,eg=null!=er?er:eu?eu.rotate:null,em=(0,s.useMemo)(()=>{if(!eu)return null;let e=1,t=null!=eo?eo:1;if(es||p){let t=eu.getViewport({scale:1,rotation:eg});es?e=es/t.width:p&&(e=p/t.height)}return t*e},[p,eu,eg,eo,es]);(0,s.useEffect)(function(){return()=>{$(eh)&&r&&ei&&ei(eh)}},[r,G,eh,ei]),(0,s.useEffect)(function(){ec({type:"RESET"})},[ec,G,eh]),(0,s.useEffect)(function(){if(!G||!ep)return;let e=S(G.getPage(ep));return e.promise.then(e=>{ec({type:"RESOLVE",value:e})}).catch(e=>{ec({type:"REJECT",error:e})}),()=>U(e)},[ec,G,ep]),(0,s.useEffect)(()=>{if(void 0!==eu){if(!1===eu)return void(ed&&(T(!1,ed.toString()),j&&j(ed)));!function(){if(k){if(!eu||!em)return;k(B(eu,em))}if(r&&q){if(!$(eh)||!ef.current)return;q(eh,ef.current)}}()}},[eu,em]);let ev=(0,s.useMemo)(()=>eu&&$(eh)&&ep&&$(eg)&&$(em)?{_className:n,canvasBackground:o,customTextRenderer:d,devicePixelRatio:f,onGetAnnotationsError:y,onGetAnnotationsSuccess:x,onGetStructTreeError:E,onGetStructTreeSuccess:b,onGetTextError:w,onGetTextSuccess:O,onRenderAnnotationLayerError:L,onRenderAnnotationLayerSuccess:A,onRenderError:D,onRenderSuccess:N,onRenderTextLayerError:z,onRenderTextLayerSuccess:W,page:eu,pageIndex:eh,pageNumber:ep,renderForms:X,renderTextLayer:en,rotate:eg,scale:em}:null,[n,o,d,f,y,x,E,b,w,O,L,A,D,N,z,W,eu,eh,ep,X,en,eg,em]),ey=(0,s.useMemo)(()=>R(ea,()=>eu?em?B(eu,em):void 0:eu),[ea,eu,em]),ex="".concat(eh,"@").concat(em,"/").concat(eg);return(0,i.jsx)("div",Object.assign({className:(0,I.A)(n,c),"data-page-number":ep,ref:_(g,ef),style:{"--scale-factor":"".concat(em),backgroundColor:o||"white",position:"relative",minWidth:"min-content",minHeight:"min-content"}},ey,{children:ep?null===G||null==eu?(0,i.jsx)(M,{type:"loading",children:"function"==typeof m?m():m}):!1===G||!1===eu?(0,i.jsx)(M,{type:"error",children:"function"==typeof h?h():h}):(0,i.jsxs)(P.Provider,{value:ev,children:[function(){switch(et){case"custom":return C(u,'renderMode was set to "custom", but no customRenderer was passed.'),(0,i.jsx)(u,{},"".concat(ex,"_custom"));case"none":return null;default:return(0,i.jsx)(J,{canvasRef:a},"".concat(ex,"_canvas"))}}(),en?(0,i.jsx)(K,{},"".concat(ex,"_text")):null,Z?(0,i.jsx)(Q,{},"".concat(ex,"_annotations")):null,l]}):(0,i.jsx)(M,{type:"no-data",children:"function"==typeof v?v():v})}))}var en=Object.prototype.hasOwnProperty;function er(e,t,n){for(n of e.keys())if(eo(n,t))return n}function eo(e,t){var n,r,o;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&eo(e[r],t[r]););return -1===r}if(n===Set){if(e.size!==t.size)return!1;for(r of e)if((o=r)&&"object"==typeof o&&!(o=er(t,o))||!t.has(o))return!1;return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e)if((o=r[0])&&"object"==typeof o&&!(o=er(t,o))||!eo(r[1],t.get(o)))return!1;return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return -1===r}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return -1===r}if(!n||"object"==typeof e){for(n in r=0,e)if(en.call(e,n)&&++r&&!en.call(t,n)||!(n in t)||!eo(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!=e&&t!=t}class ei{constructor(){this.externalLinkEnabled=!0,this.externalLinkRel=void 0,this.externalLinkTarget=void 0,this.isInPresentationMode=!1,this.pdfDocument=void 0,this.pdfViewer=void 0}setDocument(e){this.pdfDocument=e}setViewer(e){this.pdfViewer=e}setExternalLinkRel(e){this.externalLinkRel=e}setExternalLinkTarget(e){this.externalLinkTarget=e}setHistory(){}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return C(this.pdfViewer,"PDF viewer is not initialized."),this.pdfViewer.currentPageNumber||0}set page(e){C(this.pdfViewer,"PDF viewer is not initialized."),this.pdfViewer.currentPageNumber=e}get rotation(){return 0}set rotation(e){}goToDestination(e){return new Promise(t=>{C(this.pdfDocument,"PDF document not loaded."),C(e,"Destination is not specified."),"string"==typeof e?this.pdfDocument.getDestination(e).then(t):Array.isArray(e)?t(e):e.then(t)}).then(e=>{C(Array.isArray(e),`"${e}" is not a valid destination array.`);let t=e[0];new Promise(e=>{C(this.pdfDocument,"PDF document not loaded."),t instanceof Object?this.pdfDocument.getPageIndex(t).then(t=>{e(t)}).catch(()=>{C(!1,`"${t}" is not a valid page reference.`)}):"number"==typeof t?e(t):C(!1,`"${t}" is not a valid destination reference.`)}).then(t=>{let n=t+1;C(this.pdfViewer,"PDF viewer is not initialized."),C(n>=1&&n<=this.pagesCount,`"${n}" is not a valid page number.`),this.pdfViewer.scrollPageIntoView({dest:e,pageIndex:t,pageNumber:n})})})}navigateTo(e){this.goToDestination(e)}goToPage(e){C(this.pdfViewer,"PDF viewer is not initialized."),C(e>=1&&e<=this.pagesCount,`"${e}" is not a valid page number.`),this.pdfViewer.scrollPageIntoView({pageIndex:e-1,pageNumber:e})}addLinkAttributes(e,t,n){e.href=t,e.rel=this.externalLinkRel||"noopener noreferrer nofollow",e.target=n?"_blank":this.externalLinkTarget||""}getDestinationHash(){return"#"}getAnchorUrl(){return"#"}setHash(){}executeNamedAction(){}cachePageRef(){}isPageVisible(){return!0}isPageCached(){return!0}executeSetOCGState(){}}let es={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};var ea=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let{Tm:el}=E,ec=(e,t)=>{switch(t){case es.NEED_PASSWORD:e(prompt("Enter the password to open this PDF file."));break;case es.INCORRECT_PASSWORD:e(prompt("Invalid password. Please try again."))}};function eu(e){return"object"==typeof e&&null!==e&&("data"in e||"range"in e||"url"in e)}let ed=(0,s.forwardRef)(function(e,t){var{children:n,className:r,error:o="Failed to load PDF file.",externalLinkRel:a,externalLinkTarget:l,file:c,inputRef:u,imageResourcesPath:d,loading:f="Loading PDF…",noData:h="No PDF file specified.",onItemClick:p,onLoadError:g,onLoadProgress:m,onLoadSuccess:v,onPassword:y=ec,onSourceError:x,onSourceSuccess:b,options:w,renderMode:_,rotate:P}=e,O=ea(e,["children","className","error","externalLinkRel","externalLinkTarget","file","inputRef","imageResourcesPath","loading","noData","onItemClick","onLoadError","onLoadProgress","onLoadSuccess","onPassword","onSourceError","onSourceSuccess","options","renderMode","rotate"]);let[j,k]=F(),{value:L,error:A}=j,[D,N]=F(),{value:W,error:$}=D,B=(0,s.useRef)(new ei),q=(0,s.useRef)([]),Z=(0,s.useRef)(void 0),J=(0,s.useRef)(void 0);c&&c!==Z.current&&eu(c)&&(T(!eo(c,Z.current),'File prop passed to <Document /> changed, but it\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "file" prop.'),Z.current=c),w&&w!==J.current&&(T(!eo(w,J.current),'Options prop passed to <Document /> changed, but it\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "options" prop.'),J.current=w);let K=(0,s.useRef)({scrollPageIntoView:e=>{let{dest:t,pageNumber:n,pageIndex:r=n-1}=e;if(p)return void p({dest:t,pageIndex:r,pageNumber:n});let o=q.current[r];if(o)return void o.scrollIntoView();T(!1,"An internal link leading to page ".concat(n," was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>."))}});(0,s.useImperativeHandle)(t,()=>({linkService:B,pages:q,viewer:K}),[]),(0,s.useEffect)(function(){k({type:"RESET"})},[c,k]);let Y=(0,s.useCallback)(()=>{var e,t,n,r;return e=this,t=void 0,n=void 0,r=function*(){if(!c)return null;if("string"==typeof c)return V(c)?{data:H(c)}:(G(),{url:c});if(c instanceof el)return{range:c};if(c instanceof ArrayBuffer)return{data:c};if(z&&(C(z,"isBlob can only be used in a browser environment"),c instanceof Blob))return{data:yield new Promise((e,t)=>{let n=new FileReader;n.onload=()=>{if(!n.result)return t(Error("Error while reading a file."));e(n.result)},n.onerror=e=>{if(!e.target)return t(Error("Error while reading a file."));let{error:n}=e.target;if(!n)return t(Error("Error while reading a file."));switch(n.code){case n.NOT_FOUND_ERR:return t(Error("Error while reading a file: File not found."));case n.SECURITY_ERR:return t(Error("Error while reading a file: Security error."));case n.ABORT_ERR:return t(Error("Error while reading a file: Aborted."));default:return t(Error("Error while reading a file."))}},n.readAsArrayBuffer(c)})};if(C("object"==typeof c,"Invalid parameter in file, need either Uint8Array, string or a parameter object"),C(eu(c),"Invalid parameter object: need either .data, .range or .url"),"url"in c&&"string"==typeof c.url){if(V(c.url)){let{url:e}=c,t=ea(c,["url"]);return Object.assign({data:H(e)},t)}G()}return c},new(n||(n=Promise))(function(o,i){function s(e){try{l(r.next(e))}catch(e){i(e)}}function a(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((r=r.apply(e,t||[])).next())})},[c]);(0,s.useEffect)(()=>{let e=S(Y());return e.promise.then(e=>{k({type:"RESOLVE",value:e})}).catch(e=>{k({type:"REJECT",error:e})}),()=>{U(e)}},[Y,k]),(0,s.useEffect)(()=>{if(void 0!==L){if(!1===L)return void(A&&(T(!1,A.toString()),x&&x(A)));b&&b()}},[L]),(0,s.useEffect)(function(){N({type:"RESET"})},[N,L]),(0,s.useEffect)(function(){if(!L)return;let e=w?Object.assign(Object.assign({},L),w):L,t=E.YE(e);m&&(t.onProgress=m),y&&(t.onPassword=y);let n=t.promise.then(e=>{N({type:"RESOLVE",value:e})}).catch(e=>{t.destroyed||N({type:"REJECT",error:e})});return()=>{n.finally(()=>t.destroy())}},[w,N,L]),(0,s.useEffect)(()=>{if(void 0!==W){if(!1===W)return void($&&(T(!1,$.toString()),g&&g($)));W&&(v&&v(W),q.current=Array(W.numPages),B.current.setDocument(W))}},[W]),(0,s.useEffect)(function(){B.current.setViewer(K.current),B.current.setExternalLinkRel(a),B.current.setExternalLinkTarget(l)},[a,l]);let Q=(0,s.useCallback)((e,t)=>{q.current[e]=t},[]),ee=(0,s.useCallback)(e=>{delete q.current[e]},[]),et=(0,s.useMemo)(()=>({imageResourcesPath:d,linkService:B.current,onItemClick:p,pdf:W,registerPage:Q,renderMode:_,rotate:P,unregisterPage:ee}),[d,p,W,Q,_,P,ee]),en=(0,s.useMemo)(()=>R(O,()=>W),[O,W]);return(0,i.jsx)("div",Object.assign({className:(0,I.A)("react-pdf__Document",r),ref:u,style:{"--scale-factor":"1"}},en,{children:c?null==W?(0,i.jsx)(M,{type:"loading",children:"function"==typeof f?f():f}):!1===W?(0,i.jsx)(M,{type:"error",children:"function"==typeof o?o():o}):(0,i.jsx)(X.Provider,{value:et,children:n}):(0,i.jsx)(M,{type:"no-data",children:"function"==typeof h?h():h})}))});var ef=n(84611);function eh(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ep(e,t){return(ep=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eg(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,ep(e,t)}var em=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function ev(e,t){if(e.length!==t.length)return!1;for(var n,r,o=0;o<e.length;o++)if(!((n=e[o])===(r=t[o])||em(n)&&em(r))&&1)return!1;return!0}let ey=function(e,t){void 0===t&&(t=ev);var n,r,o=[],i=!1;return function(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];return i&&n===this&&t(s,o)||(r=e.apply(this,s),i=!0,n=this,o=s),r}};var ex="object"==typeof performance&&"function"==typeof performance.now?function(){return performance.now()}:function(){return Date.now()};function eE(e){cancelAnimationFrame(e.id)}function eS(e,t){var n=ex(),r={id:requestAnimationFrame(function o(){ex()-n>=t?e.call(null):r.id=requestAnimationFrame(o)})};return r}var eb=-1;function ew(e){if(void 0===e&&(e=!1),-1===eb||e){var t=document.createElement("div"),n=t.style;n.width="50px",n.height="50px",n.overflow="scroll",document.body.appendChild(t),eb=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return eb}var eR=null;function eI(e){if(void 0===e&&(e=!1),null===eR||e){var t=document.createElement("div"),n=t.style;n.width="50px",n.height="50px",n.overflow="scroll",n.direction="rtl";var r=document.createElement("div"),o=r.style;o.width="100px",o.height="100px",t.appendChild(r),document.body.appendChild(t),t.scrollLeft>0?eR="positive-descending":(t.scrollLeft=1,eR=0===t.scrollLeft?"negative":"positive-ascending"),document.body.removeChild(t)}return eR}var e_=function(e){var t=e.columnIndex;return e.data,e.rowIndex+":"+t},eC=function(e,t){e.children,e.direction,e.height,e.innerTagName,e.outerTagName,e.overscanColumnsCount,e.overscanCount,e.overscanRowsCount,e.width,t.instance},eT=function(e,t){var n=e.rowCount,r=t.rowMetadataMap,o=t.estimatedRowHeight,i=t.lastMeasuredRowIndex,s=0;if(i>=n&&(i=n-1),i>=0){var a=r[i];s=a.offset+a.size}return s+(n-i-1)*o},eP=function(e,t){var n=e.columnCount,r=t.columnMetadataMap,o=t.estimatedColumnWidth,i=t.lastMeasuredColumnIndex,s=0;if(i>=n&&(i=n-1),i>=0){var a=r[i];s=a.offset+a.size}return s+(n-i-1)*o},eM=function(e,t,n,r){var o,i,s;if("column"===e?(o=r.columnMetadataMap,i=t.columnWidth,s=r.lastMeasuredColumnIndex):(o=r.rowMetadataMap,i=t.rowHeight,s=r.lastMeasuredRowIndex),n>s){var a=0;if(s>=0){var l=o[s];a=l.offset+l.size}for(var c=s+1;c<=n;c++){var u=i(c);o[c]={offset:a,size:u},a+=u}"column"===e?r.lastMeasuredColumnIndex=n:r.lastMeasuredRowIndex=n}return o[n]},eO=function(e,t,n,r,o,i){for(;o<=r;){var s=o+Math.floor((r-o)/2),a=eM(e,t,s,n).offset;if(a===i)return s;a<i?o=s+1:a>i&&(r=s-1)}return o>0?o-1:0},ej=function(e,t,n,r,o){for(var i="column"===e?t.columnCount:t.rowCount,s=1;r<i&&eM(e,t,r,n).offset<o;)r+=s,s*=2;return eO(e,t,n,Math.min(r,i-1),Math.floor(r/2),o)},ek=function(e,t){return e},eL=function(e,t){e.children,e.direction,e.height,e.layout,e.innerTagName,e.outerTagName,e.width,t.instance},eA=function(e,t,n){var r=e.itemSize,o=n.itemMetadataMap,i=n.lastMeasuredIndex;if(t>i){var s=0;if(i>=0){var a=o[i];s=a.offset+a.size}for(var l=i+1;l<=t;l++){var c=r(l);o[l]={offset:s,size:c},s+=c}n.lastMeasuredIndex=t}return o[t]},eD=function(e,t,n){var r=t.itemMetadataMap,o=t.lastMeasuredIndex;return(o>0?r[o].offset:0)>=n?eN(e,t,o,0,n):eF(e,t,Math.max(0,o),n)},eN=function(e,t,n,r,o){for(;r<=n;){var i=r+Math.floor((n-r)/2),s=eA(e,i,t).offset;if(s===o)return i;s<o?r=i+1:s>o&&(n=i-1)}return r>0?r-1:0},eF=function(e,t,n,r){for(var o=e.itemCount,i=1;n<o&&eA(e,n,t).offset<r;)n+=i,i*=2;return eN(e,t,Math.min(n,o-1),Math.floor(n/2),r)},ez=function(e,t){var n=e.itemCount,r=t.itemMetadataMap,o=t.estimatedItemSize,i=t.lastMeasuredIndex,s=0;if(i>=n&&(i=n-1),i>=0){var a=r[i];s=a.offset+a.size}return s+(n-i-1)*o},eW=function(e){var t,n=e.getItemOffset,r=e.getEstimatedTotalSize,o=e.getItemSize,i=e.getOffsetForIndexAndAlignment,a=e.getStartIndexForOffset,l=e.getStopIndexForStartIndex,c=e.initInstanceProps,u=e.shouldResetStyleCacheOnItemSizeChange,d=e.validateProps;return(t=function(e){function t(t){var r;return(r=e.call(this,t)||this)._instanceProps=c(r.props,eh(r)),r._outerRef=void 0,r._resetIsScrollingTimeoutId=null,r.state={instance:eh(r),isScrolling:!1,scrollDirection:"forward",scrollOffset:"number"==typeof r.props.initialScrollOffset?r.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},r._callOnItemsRendered=void 0,r._callOnItemsRendered=ey(function(e,t,n,o){return r.props.onItemsRendered({overscanStartIndex:e,overscanStopIndex:t,visibleStartIndex:n,visibleStopIndex:o})}),r._callOnScroll=void 0,r._callOnScroll=ey(function(e,t,n){return r.props.onScroll({scrollDirection:e,scrollOffset:t,scrollUpdateWasRequested:n})}),r._getItemStyle=void 0,r._getItemStyle=function(e){var t,i=r.props,s=i.direction,a=i.itemSize,l=i.layout,c=r._getItemStyleCache(u&&a,u&&l,u&&s);if(c.hasOwnProperty(e))t=c[e];else{var d=n(r.props,e,r._instanceProps),f=o(r.props,e,r._instanceProps),h="horizontal"===s||"horizontal"===l,p="rtl"===s,g=h?d:0;c[e]=t={position:"absolute",left:p?void 0:g,right:p?g:void 0,top:h?0:d,height:h?"100%":f,width:h?f:"100%"}}return t},r._getItemStyleCache=void 0,r._getItemStyleCache=ey(function(e,t,n){return{}}),r._onScrollHorizontal=function(e){var t=e.currentTarget,n=t.clientWidth,o=t.scrollLeft,i=t.scrollWidth;r.setState(function(e){if(e.scrollOffset===o)return null;var t=r.props.direction,s=o;if("rtl"===t)switch(eI()){case"negative":s=-o;break;case"positive-descending":s=i-n-o}return s=Math.max(0,Math.min(s,i-n)),{isScrolling:!0,scrollDirection:e.scrollOffset<o?"forward":"backward",scrollOffset:s,scrollUpdateWasRequested:!1}},r._resetIsScrollingDebounced)},r._onScrollVertical=function(e){var t=e.currentTarget,n=t.clientHeight,o=t.scrollHeight,i=t.scrollTop;r.setState(function(e){if(e.scrollOffset===i)return null;var t=Math.max(0,Math.min(i,o-n));return{isScrolling:!0,scrollDirection:e.scrollOffset<t?"forward":"backward",scrollOffset:t,scrollUpdateWasRequested:!1}},r._resetIsScrollingDebounced)},r._outerRefSetter=function(e){var t=r.props.outerRef;r._outerRef=e,"function"==typeof t?t(e):null!=t&&"object"==typeof t&&t.hasOwnProperty("current")&&(t.current=e)},r._resetIsScrollingDebounced=function(){null!==r._resetIsScrollingTimeoutId&&eE(r._resetIsScrollingTimeoutId),r._resetIsScrollingTimeoutId=eS(r._resetIsScrolling,150)},r._resetIsScrolling=function(){r._resetIsScrollingTimeoutId=null,r.setState({isScrolling:!1},function(){r._getItemStyleCache(-1,null)})},r}eg(t,e),t.getDerivedStateFromProps=function(e,t){return eL(e,t),d(e),null};var f=t.prototype;return f.scrollTo=function(e){e=Math.max(0,e),this.setState(function(t){return t.scrollOffset===e?null:{scrollDirection:t.scrollOffset<e?"forward":"backward",scrollOffset:e,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},f.scrollToItem=function(e,t){void 0===t&&(t="auto");var n=this.props,r=n.itemCount,o=n.layout,s=this.state.scrollOffset;e=Math.max(0,Math.min(e,r-1));var a=0;if(this._outerRef){var l=this._outerRef;a="vertical"===o?l.scrollWidth>l.clientWidth?ew():0:l.scrollHeight>l.clientHeight?ew():0}this.scrollTo(i(this.props,e,t,s,this._instanceProps,a))},f.componentDidMount=function(){var e=this.props,t=e.direction,n=e.initialScrollOffset,r=e.layout;if("number"==typeof n&&null!=this._outerRef){var o=this._outerRef;"horizontal"===t||"horizontal"===r?o.scrollLeft=n:o.scrollTop=n}this._callPropsCallbacks()},f.componentDidUpdate=function(){var e=this.props,t=e.direction,n=e.layout,r=this.state,o=r.scrollOffset;if(r.scrollUpdateWasRequested&&null!=this._outerRef){var i=this._outerRef;if("horizontal"===t||"horizontal"===n)if("rtl"===t)switch(eI()){case"negative":i.scrollLeft=-o;break;case"positive-ascending":i.scrollLeft=o;break;default:var s=i.clientWidth,a=i.scrollWidth;i.scrollLeft=a-s-o}else i.scrollLeft=o;else i.scrollTop=o}this._callPropsCallbacks()},f.componentWillUnmount=function(){null!==this._resetIsScrollingTimeoutId&&eE(this._resetIsScrollingTimeoutId)},f.render=function(){var e=this.props,t=e.children,n=e.className,o=e.direction,i=e.height,a=e.innerRef,l=e.innerElementType,c=e.innerTagName,u=e.itemCount,d=e.itemData,f=e.itemKey,h=void 0===f?ek:f,p=e.layout,g=e.outerElementType,m=e.outerTagName,v=e.style,y=e.useIsScrolling,x=e.width,E=this.state.isScrolling,S="horizontal"===o||"horizontal"===p,b=S?this._onScrollHorizontal:this._onScrollVertical,w=this._getRangeToRender(),R=w[0],I=w[1],_=[];if(u>0)for(var C=R;C<=I;C++)_.push((0,s.createElement)(t,{data:d,key:h(C,d),index:C,isScrolling:y?E:void 0,style:this._getItemStyle(C)}));var T=r(this.props,this._instanceProps);return(0,s.createElement)(g||m||"div",{className:n,onScroll:b,ref:this._outerRefSetter,style:(0,ef.A)({position:"relative",height:i,width:x,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:o},v)},(0,s.createElement)(l||c||"div",{children:_,ref:a,style:{height:S?"100%":T,pointerEvents:E?"none":void 0,width:S?T:"100%"}}))},f._callPropsCallbacks=function(){if("function"==typeof this.props.onItemsRendered&&this.props.itemCount>0){var e=this._getRangeToRender(),t=e[0],n=e[1],r=e[2],o=e[3];this._callOnItemsRendered(t,n,r,o)}if("function"==typeof this.props.onScroll){var i=this.state,s=i.scrollDirection,a=i.scrollOffset,l=i.scrollUpdateWasRequested;this._callOnScroll(s,a,l)}},f._getRangeToRender=function(){var e=this.props,t=e.itemCount,n=e.overscanCount,r=this.state,o=r.isScrolling,i=r.scrollDirection,s=r.scrollOffset;if(0===t)return[0,0,0,0];var c=a(this.props,s,this._instanceProps),u=l(this.props,c,s,this._instanceProps);return[Math.max(0,c-(o&&"backward"!==i?1:Math.max(1,n))),Math.max(0,Math.min(t-1,u+(o&&"forward"!==i?1:Math.max(1,n)))),c,u]},t}(s.PureComponent)).defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},t}({getItemOffset:function(e,t,n){return eA(e,t,n).offset},getItemSize:function(e,t,n){return n.itemMetadataMap[t].size},getEstimatedTotalSize:ez,getOffsetForIndexAndAlignment:function(e,t,n,r,o,i){var s=e.direction,a=e.height,l=e.layout,c=e.width,u="horizontal"===s||"horizontal"===l?c:a,d=eA(e,t,o),f=Math.max(0,Math.min(ez(e,o)-u,d.offset)),h=Math.max(0,d.offset-u+d.size+i);switch("smart"===n&&(n=r>=h-u&&r<=f+u?"auto":"center"),n){case"start":return f;case"end":return h;case"center":return Math.round(h+(f-h)/2);default:if(r>=h&&r<=f)return r;if(r<h)return h;return f}},getStartIndexForOffset:function(e,t,n){return eD(e,n,t)},getStopIndexForStartIndex:function(e,t,n,r){for(var o=e.direction,i=e.height,s=e.itemCount,a=e.layout,l=e.width,c=eA(e,t,r),u=n+("horizontal"===o||"horizontal"===a?l:i),d=c.offset+c.size,f=t;f<s-1&&d<u;)d+=eA(e,++f,r).size;return f},initInstanceProps:function(e,t){var n={itemMetadataMap:{},estimatedItemSize:e.estimatedItemSize||50,lastMeasuredIndex:-1};return t.resetAfterIndex=function(e,r){void 0===r&&(r=!0),n.lastMeasuredIndex=Math.min(n.lastMeasuredIndex,e-1),t._getItemStyleCache(-1),r&&t.forceUpdate()},n},shouldResetStyleCacheOnItemSizeChange:!1,validateProps:function(e){e.itemSize}});function e$(e){return Array.isArray?Array.isArray(e):"[object Array]"===eZ(e)}let eV=1/0;function eH(e){return"string"==typeof e}function eG(e){return"number"==typeof e}function eU(e){return"object"==typeof e}function eB(e){return null!=e}function eq(e){return!e.trim().length}function eZ(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}let eJ=e=>`Invalid value for key ${e}`,eK=e=>`Pattern length exceeds max of ${e}.`,eX=e=>`Missing ${e} property in key`,eY=e=>`Property 'weight' in key '${e}' must be a positive integer`,eQ=Object.prototype.hasOwnProperty;class e0{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach(e=>{let n=e1(e);t+=n.weight,this._keys.push(n),this._keyMap[n.id]=n,t+=n.weight}),this._keys.forEach(e=>{e.weight/=t})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function e1(e){let t=null,n=null,r=null,o=1,i=null;if(eH(e)||e$(e))r=e,t=e2(e),n=e4(e);else{if(!eQ.call(e,"name"))throw Error(eX("name"));let s=e.name;if(r=s,eQ.call(e,"weight")&&(o=e.weight)<=0)throw Error(eY(s));t=e2(s),n=e4(s),i=e.getFn}return{path:t,id:n,weight:o,src:r,getFn:i}}function e2(e){return e$(e)?e:e.split(".")}function e4(e){return e$(e)?e.join("."):e}var e3={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(e,t){let n=[],r=!1,o=(e,t,i)=>{if(eB(e))if(t[i]){var s,a;let l=e[t[i]];if(!eB(l))return;if(i===t.length-1&&(eH(l)||eG(l)||!0===(s=l)||!1===s||eU(a=s)&&null!==a&&"[object Boolean]"==eZ(s)))n.push(null==l?"":function(e){if("string"==typeof e)return e;let t=e+"";return"0"==t&&1/e==-eV?"-0":t}(l));else if(e$(l)){r=!0;for(let e=0,n=l.length;e<n;e+=1)o(l[e],t,i+1)}else t.length&&o(l,t,i+1)}else n.push(e)};return o(e,eH(t)?t.split("."):t,0),r?n:n[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};let e6=/[^ ]+/g;class e8{constructor({getFn:e=e3.getFn,fieldNormWeight:t=e3.fieldNormWeight}={}){this.norm=function(e=1,t=3){let n=new Map,r=Math.pow(10,t);return{get(t){let o=t.match(e6).length;if(n.has(o))return n.get(o);let i=parseFloat(Math.round(1/Math.pow(o,.5*e)*r)/r);return n.set(o,i),i},clear(){n.clear()}}}(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((e,t)=>{this._keysMap[e.id]=t})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,eH(this.docs[0])?this.docs.forEach((e,t)=>{this._addString(e,t)}):this.docs.forEach((e,t)=>{this._addObject(e,t)}),this.norm.clear())}add(e){let t=this.size();eH(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,n=this.size();t<n;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!eB(e)||eq(e))return;let n={v:e,i:t,n:this.norm.get(e)};this.records.push(n)}_addObject(e,t){let n={i:t,$:{}};this.keys.forEach((t,r)=>{let o=t.getFn?t.getFn(e):this.getFn(e,t.path);if(eB(o)){if(e$(o)){let e=[],t=[{nestedArrIndex:-1,value:o}];for(;t.length;){let{nestedArrIndex:n,value:r}=t.pop();if(eB(r))if(eH(r)&&!eq(r)){let t={v:r,i:n,n:this.norm.get(r)};e.push(t)}else e$(r)&&r.forEach((e,n)=>{t.push({nestedArrIndex:n,value:e})})}n.$[r]=e}else if(eH(o)&&!eq(o)){let e={v:o,n:this.norm.get(o)};n.$[r]=e}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function e5(e,t,{getFn:n=e3.getFn,fieldNormWeight:r=e3.fieldNormWeight}={}){let o=new e8({getFn:n,fieldNormWeight:r});return o.setKeys(e.map(e1)),o.setSources(t),o.create(),o}function e9(e,{errors:t=0,currentLocation:n=0,expectedLocation:r=0,distance:o=e3.distance,ignoreLocation:i=e3.ignoreLocation}={}){let s=t/e.length;if(i)return s;let a=Math.abs(r-n);return o?s+a/o:a?1:s}class e7{constructor(e,{location:t=e3.location,threshold:n=e3.threshold,distance:r=e3.distance,includeMatches:o=e3.includeMatches,findAllMatches:i=e3.findAllMatches,minMatchCharLength:s=e3.minMatchCharLength,isCaseSensitive:a=e3.isCaseSensitive,ignoreLocation:l=e3.ignoreLocation}={}){if(this.options={location:t,threshold:n,distance:r,includeMatches:o,findAllMatches:i,minMatchCharLength:s,isCaseSensitive:a,ignoreLocation:l},this.pattern=a?e:e.toLowerCase(),this.chunks=[],!this.pattern.length)return;let c=(e,t)=>{this.chunks.push({pattern:e,alphabet:function(e){let t={};for(let n=0,r=e.length;n<r;n+=1){let o=e.charAt(n);t[o]=(t[o]||0)|1<<r-n-1}return t}(e),startIndex:t})},u=this.pattern.length;if(u>32){let e=0,t=u%32,n=u-t;for(;e<n;)c(this.pattern.substr(e,32),e),e+=32;if(t){let e=u-32;c(this.pattern.substr(e),e)}}else c(this.pattern,0)}searchIn(e){let{isCaseSensitive:t,includeMatches:n}=this.options;if(t||(e=e.toLowerCase()),this.pattern===e){let t={isMatch:!0,score:0};return n&&(t.indices=[[0,e.length-1]]),t}let{location:r,distance:o,threshold:i,findAllMatches:s,minMatchCharLength:a,ignoreLocation:l}=this.options,c=[],u=0,d=!1;this.chunks.forEach(({pattern:t,alphabet:f,startIndex:h})=>{let{isMatch:p,score:g,indices:m}=function(e,t,n,{location:r=e3.location,distance:o=e3.distance,threshold:i=e3.threshold,findAllMatches:s=e3.findAllMatches,minMatchCharLength:a=e3.minMatchCharLength,includeMatches:l=e3.includeMatches,ignoreLocation:c=e3.ignoreLocation}={}){let u;if(t.length>32)throw Error(eK(32));let d=t.length,f=e.length,h=Math.max(0,Math.min(r,f)),p=i,g=h,m=a>1||l,v=m?Array(f):[];for(;(u=e.indexOf(t,g))>-1;)if(p=Math.min(e9(t,{currentLocation:u,expectedLocation:h,distance:o,ignoreLocation:c}),p),g=u+d,m){let e=0;for(;e<d;)v[u+e]=1,e+=1}g=-1;let y=[],x=1,E=d+f,S=1<<d-1;for(let r=0;r<d;r+=1){let i=0,a=E;for(;i<a;)e9(t,{errors:r,currentLocation:h+a,expectedLocation:h,distance:o,ignoreLocation:c})<=p?i=a:E=a,a=Math.floor((E-i)/2+i);E=a;let l=Math.max(1,h-a+1),u=s?f:Math.min(h+a,f)+d,b=Array(u+2);b[u+1]=(1<<r)-1;for(let i=u;i>=l;i-=1){let s=i-1,a=n[e.charAt(s)];if(m&&(v[s]=+!!a),b[i]=(b[i+1]<<1|1)&a,r&&(b[i]|=(y[i+1]|y[i])<<1|1|y[i+1]),b[i]&S&&(x=e9(t,{errors:r,currentLocation:s,expectedLocation:h,distance:o,ignoreLocation:c}))<=p){if(p=x,(g=s)<=h)break;l=Math.max(1,2*h-g)}}if(e9(t,{errors:r+1,currentLocation:h,expectedLocation:h,distance:o,ignoreLocation:c})>p)break;y=b}let b={isMatch:g>=0,score:Math.max(.001,x)};if(m){let e=function(e=[],t=e3.minMatchCharLength){let n=[],r=-1,o=-1,i=0;for(let s=e.length;i<s;i+=1){let s=e[i];s&&-1===r?r=i:s||-1===r||((o=i-1)-r+1>=t&&n.push([r,o]),r=-1)}return e[i-1]&&i-r>=t&&n.push([r,i-1]),n}(v,a);e.length?l&&(b.indices=e):b.isMatch=!1}return b}(e,t,f,{location:r+h,distance:o,threshold:i,findAllMatches:s,minMatchCharLength:a,includeMatches:n,ignoreLocation:l});p&&(d=!0),u+=g,p&&m&&(c=[...c,...m])});let f={isMatch:d,score:d?u/this.chunks.length:1};return d&&n&&(f.indices=c),f}}class te{constructor(e){this.pattern=e}static isMultiMatch(e){return tt(e,this.multiRegex)}static isSingleMatch(e){return tt(e,this.singleRegex)}search(){}}function tt(e,t){let n=e.match(t);return n?n[1]:null}class tn extends te{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){let t=e===this.pattern;return{isMatch:t,score:+!t,indices:[0,this.pattern.length-1]}}}class tr extends te{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){let t=-1===e.indexOf(this.pattern);return{isMatch:t,score:+!t,indices:[0,e.length-1]}}}class to extends te{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){let t=e.startsWith(this.pattern);return{isMatch:t,score:+!t,indices:[0,this.pattern.length-1]}}}class ti extends te{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){let t=!e.startsWith(this.pattern);return{isMatch:t,score:+!t,indices:[0,e.length-1]}}}class ts extends te{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){let t=e.endsWith(this.pattern);return{isMatch:t,score:+!t,indices:[e.length-this.pattern.length,e.length-1]}}}class ta extends te{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){let t=!e.endsWith(this.pattern);return{isMatch:t,score:+!t,indices:[0,e.length-1]}}}class tl extends te{constructor(e,{location:t=e3.location,threshold:n=e3.threshold,distance:r=e3.distance,includeMatches:o=e3.includeMatches,findAllMatches:i=e3.findAllMatches,minMatchCharLength:s=e3.minMatchCharLength,isCaseSensitive:a=e3.isCaseSensitive,ignoreLocation:l=e3.ignoreLocation}={}){super(e),this._bitapSearch=new e7(e,{location:t,threshold:n,distance:r,includeMatches:o,findAllMatches:i,minMatchCharLength:s,isCaseSensitive:a,ignoreLocation:l})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class tc extends te{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t,n=0,r=[],o=this.pattern.length;for(;(t=e.indexOf(this.pattern,n))>-1;)n=t+o,r.push([t,n-1]);let i=!!r.length;return{isMatch:i,score:+!i,indices:r}}}let tu=[tn,tc,to,ti,ta,ts,tr,tl],td=tu.length,tf=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,th=new Set([tl.type,tc.type]);class tp{constructor(e,{isCaseSensitive:t=e3.isCaseSensitive,includeMatches:n=e3.includeMatches,minMatchCharLength:r=e3.minMatchCharLength,ignoreLocation:o=e3.ignoreLocation,findAllMatches:i=e3.findAllMatches,location:s=e3.location,threshold:a=e3.threshold,distance:l=e3.distance}={}){this.query=null,this.options={isCaseSensitive:t,includeMatches:n,minMatchCharLength:r,findAllMatches:i,ignoreLocation:o,location:s,threshold:a,distance:l},this.pattern=t?e:e.toLowerCase(),this.query=function(e,t={}){return e.split("|").map(e=>{let n=e.trim().split(tf).filter(e=>e&&!!e.trim()),r=[];for(let e=0,o=n.length;e<o;e+=1){let o=n[e],i=!1,s=-1;for(;!i&&++s<td;){let e=tu[s],n=e.isMultiMatch(o);n&&(r.push(new e(n,t)),i=!0)}if(!i)for(s=-1;++s<td;){let e=tu[s],n=e.isSingleMatch(o);if(n){r.push(new e(n,t));break}}}return r})}(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){let t=this.query;if(!t)return{isMatch:!1,score:1};let{includeMatches:n,isCaseSensitive:r}=this.options;e=r?e:e.toLowerCase();let o=0,i=[],s=0;for(let r=0,a=t.length;r<a;r+=1){let a=t[r];i.length=0,o=0;for(let t=0,r=a.length;t<r;t+=1){let r=a[t],{isMatch:l,indices:c,score:u}=r.search(e);if(l){if(o+=1,s+=u,n){let e=r.constructor.type;th.has(e)?i=[...i,...c]:i.push(c)}}else{s=0,o=0,i.length=0;break}}if(o){let e={isMatch:!0,score:s/o};return n&&(e.indices=i),e}}return{isMatch:!1,score:1}}}let tg=[];function tm(e,t){for(let n=0,r=tg.length;n<r;n+=1){let r=tg[n];if(r.condition(e,t))return new r(e,t)}return new e7(e,t)}let tv={AND:"$and",OR:"$or"},ty={PATH:"$path",PATTERN:"$val"},tx=e=>!!(e[tv.AND]||e[tv.OR]),tE=e=>!!e[ty.PATH],tS=e=>!e$(e)&&eU(e)&&!tx(e),tb=e=>({[tv.AND]:Object.keys(e).map(t=>({[t]:e[t]}))});function tw(e,t,{auto:n=!0}={}){let r=e=>{let o=Object.keys(e),i=tE(e);if(!i&&o.length>1&&!tx(e))return r(tb(e));if(tS(e)){let r=i?e[ty.PATH]:o[0],s=i?e[ty.PATTERN]:e[r];if(!eH(s))throw Error(eJ(r));let a={keyId:e4(r),pattern:s};return n&&(a.searcher=tm(s,t)),a}let s={children:[],operator:o[0]};return o.forEach(t=>{let n=e[t];e$(n)&&n.forEach(e=>{s.children.push(r(e))})}),s};return tx(e)||(e=tb(e)),r(e)}function tR(e,t){let n=e.matches;t.matches=[],eB(n)&&n.forEach(e=>{if(!eB(e.indices)||!e.indices.length)return;let{indices:n,value:r}=e,o={indices:n,value:r};e.key&&(o.key=e.key.src),e.idx>-1&&(o.refIndex=e.idx),t.matches.push(o)})}function tI(e,t){t.score=e.score}class t_{constructor(e,t={},n){this.options={...e3,...t},this.options.useExtendedSearch,this._keyStore=new e0(this.options.keys),this.setCollection(e,n)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof e8))throw Error("Incorrect 'index' type");this._myIndex=t||e5(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){eB(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){let t=[];for(let n=0,r=this._docs.length;n<r;n+=1){let o=this._docs[n];e(o,n)&&(this.removeAt(n),n-=1,r-=1,t.push(o))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){let{includeMatches:n,includeScore:r,shouldSort:o,sortFn:i,ignoreFieldNorm:s}=this.options,a=eH(e)?eH(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return!function(e,{ignoreFieldNorm:t=e3.ignoreFieldNorm}){e.forEach(e=>{let n=1;e.matches.forEach(({key:e,norm:r,score:o})=>{let i=e?e.weight:null;n*=Math.pow(0===o&&i?Number.EPSILON:o,(i||1)*(t?1:r))}),e.score=n})}(a,{ignoreFieldNorm:s}),o&&a.sort(i),eG(t)&&t>-1&&(a=a.slice(0,t)),function(e,t,{includeMatches:n=e3.includeMatches,includeScore:r=e3.includeScore}={}){let o=[];return n&&o.push(tR),r&&o.push(tI),e.map(e=>{let{idx:n}=e,r={item:t[n],refIndex:n};return o.length&&o.forEach(t=>{t(e,r)}),r})}(a,this._docs,{includeMatches:n,includeScore:r})}_searchStringList(e){let t=tm(e,this.options),{records:n}=this._myIndex,r=[];return n.forEach(({v:e,i:n,n:o})=>{if(!eB(e))return;let{isMatch:i,score:s,indices:a}=t.searchIn(e);i&&r.push({item:e,idx:n,matches:[{score:s,value:e,norm:o,indices:a}]})}),r}_searchLogical(e){let t=tw(e,this.options),n=(e,t,r)=>{if(!e.children){let{keyId:n,searcher:o}=e,i=this._findMatches({key:this._keyStore.get(n),value:this._myIndex.getValueForItemAtKeyId(t,n),searcher:o});return i&&i.length?[{idx:r,item:t,matches:i}]:[]}let o=[];for(let i=0,s=e.children.length;i<s;i+=1){let s=n(e.children[i],t,r);if(s.length)o.push(...s);else if(e.operator===tv.AND)return[]}return o},r=this._myIndex.records,o={},i=[];return r.forEach(({$:e,i:r})=>{if(eB(e)){let s=n(t,e,r);s.length&&(o[r]||(o[r]={idx:r,item:e,matches:[]},i.push(o[r])),s.forEach(({matches:e})=>{o[r].matches.push(...e)}))}}),i}_searchObjectList(e){let t=tm(e,this.options),{keys:n,records:r}=this._myIndex,o=[];return r.forEach(({$:e,i:r})=>{if(!eB(e))return;let i=[];n.forEach((n,r)=>{i.push(...this._findMatches({key:n,value:e[r],searcher:t}))}),i.length&&o.push({idx:r,item:e,matches:i})}),o}_findMatches({key:e,value:t,searcher:n}){if(!eB(t))return[];let r=[];if(e$(t))t.forEach(({v:t,i:o,n:i})=>{if(!eB(t))return;let{isMatch:s,score:a,indices:l}=n.searchIn(t);s&&r.push({score:a,key:e,value:t,idx:o,norm:i,indices:l})});else{let{v:o,n:i}=t,{isMatch:s,score:a,indices:l}=n.searchIn(o);s&&r.push({score:a,key:e,value:o,norm:i,indices:l})}return r}}t_.version="6.6.2",t_.createIndex=e5,t_.parseIndex=function(e,{getFn:t=e3.getFn,fieldNormWeight:n=e3.fieldNormWeight}={}){let{keys:r,records:o}=e,i=new e8({getFn:t,fieldNormWeight:n});return i.setKeys(r),i.setIndexRecords(o),i},t_.config=e3,t_.parseQuery=tw,function(...e){tg.push(...e)}(tp),function(e){e.purple="llama-purple",e.magenta="llama-magenta",e.red="llama-red",e.orange="llama-orange",e.yellow="llama-yellow",e.lime="llama-lime",e.teal="llama-teal",e.cyan="llama-cyan",e.blue="llama-blue",e.indigo="llama-indigo"}(r||(r={}));let tC={[r.purple]:"bg-llama-purple-light",[r.magenta]:"bg-llama-magenta-light",[r.red]:"bg-llama-red-light",[r.indigo]:"bg-llama-indigo-light",[r.lime]:"bg-llama-lime-light",[r.orange]:"bg-llama-orange-light",[r.blue]:"bg-llama-blue-light",[r.yellow]:"bg-llama-yellow-light",[r.teal]:"bg-llama-teal-light",[r.cyan]:"bg-llama-cyan-light"},tT=(e,t,n=r.yellow)=>{var i;let s=tC[n],a=document.querySelectorAll(`div[data-page-number='${t+1}'] .react-pdf__Page__textContent.textLayer span`),l=[];a.forEach((e,t)=>{(e.textContent||"").split(" ").map((e,n)=>{l.push({text:e,spanIdx:t,wordIdx:n})})});let c=e,u=(c=(c=(c=c.replace(/\s{2,}/g," ")).replace(/\t/g," ")).toString().trim().replace(/(\r\n|\n|\r)/g,"")).split(" "),d=u.length;if(!d)return;let f=u[0];if(!f)return;let h=new t_(function(e,t,n){var r,o,i,s,a;let l=[];for(let c=0;c<=t.length-n;c++)if((null==(r=t[c])?void 0:r.text)===e){let e=t.slice(c,c+n).reduce((e,t)=>e+" "+t.text,""),r=(null==(o=t[c])?void 0:o.spanIdx)||0,u=(null==(i=t[c+n])?void 0:i.spanIdx)||0,d=(null==(s=t[c])?void 0:s.wordIdx)||0,f=(null==(a=t[c+n])?void 0:a.wordIdx)||0;l.push({text:e,startSpan:r,endSpan:u,startWordIdx:d,endWordIdx:f})}return l}(f,l,d),{includeScore:!0,threshold:.1,minMatchCharLength:10,shouldSort:!0,findAllMatches:!0,includeMatches:!0,keys:["text"]}).search(c);if(h.length>0){let e=null==(i=h[0])?void 0:i.item,t=(null==e?void 0:e.startSpan)||0,n=(null==e?void 0:e.endSpan)||0,r=(null==e?void 0:e.startWordIdx)||0,l=(null==e?void 0:e.endWordIdx)||0;for(let e=t;e<n+1;e++){let i=a[e];if(e==t)0===r?tM(i,s):tO(r,i,o.START);else if(e==n)if(0===l)return;else tO(l,i,o.END);else tM(i,s)}}return!0},tP="opacity-40 saturate-[3] highlighted-by-llama ",tM=(e,t)=>{let n=e.textContent||"",r=document.createElement("span");r.className=tP+t,r.innerText=n,e.innerText="",e.appendChild(r)};!function(e){e[e.START=0]="START",e[e.END=1]="END"}(o||(o={}));let tO=(e,t,n=o.START)=>{let r=t.textContent;if(!r)return;let i=r.split(" ")[e-1]||"";t.textContent="",r.split(i).forEach((e,r)=>{if(n===o.START)if(0==r)t.appendChild(document.createTextNode(e));else{t.appendChild(document.createTextNode(i));let n=document.createElement("span");n.className=tP,n.textContent=e,t.appendChild(n)}if(n===o.END)if(0==r){let n=document.createElement("span");n.className=tP,n.textContent=e,t.appendChild(n),t.appendChild(document.createTextNode(e))}else t.appendChild(document.createTextNode(i)),t.appendChild(document.createTextNode(e))})};E.EA.workerSrc="//unpkg.com/pdfjs-dist@"+String(E.rE)+"/legacy/build/pdf.worker.min.mjs";let{HORIZONTAL_GUTTER_SIZE_PX:tj,OBSERVER_THRESHOLD_PERCENTAGE:tk,PAGE_HEIGHT:tL,PDF_HEADER_SIZE_PX:tA,PDF_SIDEBAR_SIZE_PX:tD,PDF_WIDTH_PERCENTAGE:tN,VERTICAL_GUTTER_SIZE_PX:tF}={VERTICAL_GUTTER_SIZE_PX:20,HORIZONTAL_GUTTER_SIZE_PX:20,PAGE_HEIGHT:792,PDF_WIDTH_PERCENTAGE:56,PDF_HEIGHT_PERCENTAGE:94,OBSERVER_THRESHOLD_PERCENTAGE:.4,PDF_HEADER_SIZE_PX:44,PDF_SIDEBAR_SIZE_PX:80},tz=({file:e,pageNumber:t,style:n,scale:r,listWidth:o,setPageInView:a})=>{var l;let{pdfFocusState:u}=c(),[d,f]=(0,s.useState)(!1),[h,p]=(0,s.useState)(!1),{ref:E,inView:S}=function(){var e;let{threshold:t,delay:n,trackVisibility:r,rootMargin:o,root:i,triggerOnce:a,skip:l,initialInView:c,fallbackInView:u,onChange:d}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[f,h]=s.useState(null),p=s.useRef(),[g,E]=s.useState({inView:!!c,entry:void 0});p.current=d,s.useEffect(()=>{let e;if(!l&&f)return e=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:x;if(void 0===window.IntersectionObserver&&void 0!==r){let o=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:"number"==typeof n.threshold?n.threshold:0,time:0,boundingClientRect:o,intersectionRect:o,rootBounds:o}),()=>{}}let{id:o,observer:i,elements:s}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var n;return"".concat(t,"_").concat("root"===t?(n=e.root)?(v.has(n)||(y+=1,v.set(n,y.toString())),v.get(n)):"0":e[t])}).toString(),n=m.get(t);if(!n){let r,o=new Map,i=new IntersectionObserver(t=>{t.forEach(t=>{var n;let i=t.isIntersecting&&r.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=i),null==(n=o.get(t.target))||n.forEach(e=>{e(i,t)})})},e);r=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:i,elements:o},m.set(t,n)}return n}(n),a=s.get(e)||[];return s.has(e)||s.set(e,a),a.push(t),i.observe(e),function(){a.splice(a.indexOf(t),1),0===a.length&&(s.delete(e),i.unobserve(e)),0===s.size&&(i.disconnect(),m.delete(o))}}(f,(t,n)=>{E({inView:t,entry:n}),p.current&&p.current(t,n),n.isIntersecting&&a&&e&&(e(),e=void 0)},{root:i,rootMargin:o,threshold:t,trackVisibility:r,delay:n},u),()=>{e&&e()}},[Array.isArray(t)?t.toString():t,f,i,o,a,l,r,u,n]);let S=null==(e=g.entry)?void 0:e.target,b=s.useRef();f||!S||a||l||b.current===S||(b.current=S,E({inView:!!c,entry:void 0}));let w=[h,g.inView,g.entry];return w.ref=w[0],w.inView=w[1],w.entry=w[2],w}({threshold:tk*Math.min(1/r,1)}),b=(0,s.useRef)(null),w=(0,s.useCallback)(e=>{b.current=e,E(e)},[E]);(0,s.useEffect)(()=>{S&&a(t)},[S,t,a,E]);let R=(0,s.useCallback)(()=>{if(b.current){let e=b.current.querySelector("canvas");e&&(e.style.visibility="hidden")}},[b]),I=(0,s.useCallback)(()=>{if(b.current){let e=b.current.querySelector("canvas");e&&(e.style.visibility="visible")}},[b]),_=(0,s.useCallback)(()=>{R()},[R]),C=(0,s.useCallback)(()=>{I()},[I]),T=(0,s.useCallback)(e=>{I(),M(),o>e.width?f(!0):f(!1)},[I,o]),P=u.documentId===e.id;(0,s.useEffect)(()=>{M()},[P,S]);let M=(0,s.useCallback)(g(()=>{var e;P&&(null==(e=u.citation)?void 0:e.pageNumber)===t+1&&!h&&(tT(u.citation.snippet,t,u.citation.color),p(!0))},50),[null==(l=u.citation)?void 0:l.snippet,t,h]);return(0,i.jsx)("div",{ref:w,style:Object.assign(Object.assign({},n),{padding:"10px",backgroundColor:"WhiteSmoke",display:`${d?"flex":""}`,justifyContent:"center"}),children:(0,i.jsx)(et,{scale:r,onRenderSuccess:T,onLoadSuccess:_,onRenderError:C,pageIndex:t,renderAnnotationLayer:!0})},`${e.url}-${t}`)},tW=(0,s.forwardRef)(({file:e,scale:t,setIndex:n,setScaleFit:r,setNumPages:o},a)=>{let l=function(){var e=(0,s.useState)(h?window.innerWidth:null),t=e[0],n=e[1],r=(0,s.useCallback)(function(){return n(window.innerWidth)},[]);return f(h?window:null,"resize",r),t}(),c=function(){var e=(0,s.useState)(p?window.innerHeight:null),t=e[0],n=e[1],r=(0,s.useCallback)(function(){return n(window.innerHeight)},[]);return f(p?window:null,"resize",r),t}(),u=.01*tN*(l||0)-tD-tj,[d,g]=(0,s.useState)(null),m=(0,s.useRef)(null);(0,s.useEffect)(()=>{m.current&&m.current.resetAfterIndex(0)},[t]),(0,s.useEffect)(()=>{d&&((function(){var e,t,n,o;return e=this,t=void 0,n=void 0,o=function*(){d&&(yield d.getPage(1).then(e=>{r(u/e.getViewport({scale:1}).width)}))},new(n||(n=Promise))(function(r,i){function s(e){try{l(o.next(e))}catch(e){i(e)}}function a(e){try{l(o.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((o=o.apply(e,t||[])).next())})})().catch(()=>console.log("page load error")),o(d.numPages))},[d,o,r,u]),s.useImperativeHandle(a,()=>({scrollToPage:e=>{v({pageNumber:e})}}));let v=({pageNumber:e})=>{m.current&&m.current.scrollTo(e*(tL+tF)*t)};return(0,i.jsx)("div",{className:"pdfDocument",children:(0,i.jsx)(ed,{onItemClick:v,file:e.url,onLoadSuccess:function(e){g(e)},loading:"Loading...",children:d?(0,i.jsx)(eW,{ref:m,width:u+tj,height:(c||0)-tA,itemCount:d.numPages,itemSize:function(){return(tL+tF)*t},estimatedItemSize:(tL+tF)*t,children:({index:r,style:o})=>(0,i.jsx)(tz,{file:e,pageNumber:r,style:o,scale:t,listWidth:u,setPageInView:n},`page-${r}`)}):null},e.url)})}),t$=(0,s.memo)(tW);t$.displayName="VirtualizedPDF";let tV=t$,tH=e=>(0,i.jsx)("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},e,{children:(0,i.jsx)("path",{d:"m6 9 6 6 6-6"})})),tG=e=>(0,i.jsx)("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},e,{children:(0,i.jsx)("path",{d:"m18 15-6-6-6 6"})})),tU=e=>(0,i.jsxs)("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},e,{children:[(0,i.jsx)("circle",{cx:"11",cy:"11",r:"8"}),(0,i.jsx)("line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65"}),(0,i.jsx)("line",{x1:"8",x2:"14",y1:"11",y2:"11"})]})),tB=e=>(0,i.jsxs)("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},e,{children:[(0,i.jsx)("circle",{cx:"11",cy:"11",r:"8"}),(0,i.jsx)("line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65"}),(0,i.jsx)("line",{x1:"11",x2:"11",y1:"8",y2:"14"}),(0,i.jsx)("line",{x1:"8",x2:"14",y1:"11",y2:"11"})]})),tq=({file:e,scrolledIndex:t,numPages:n,scaleText:r,nextPage:o,prevPage:a,handleZoomIn:l,handleZoomOut:c,goToPage:d,setZoomLevel:f,zoomInEnabled:h,zoomOutEnabled:p})=>{let[g,m]=(0,s.useState)(`${t+1}`);(0,s.useEffect)(()=>{m(`${t+1}`)},[t]);let v=e=>{f(e)},y=e=>{d(e)};return(0,i.jsxs)("div",{className:"optionBar",children:[(0,i.jsxs)("div",{className:"title truncate",children:["File ID: ",e.id]}),(0,i.jsxs)("div",{className:"control",children:[(0,i.jsxs)("div",{className:"pageControl",children:[(0,i.jsx)("button",{className:"pageUp",onClick:a,disabled:0===t,children:(0,i.jsx)(tG,{})}),(0,i.jsx)("input",{className:"pageInput",value:g,onChange:e=>{m(e.target.value)},onKeyDown:e=>{if("Enter"===e.key){let e=parseInt(g,10);!isNaN(e)&&e>0&&y(e-1)}}}),(0,i.jsx)("button",{className:"pageDown",onClick:o,disabled:t===n-1,children:(0,i.jsx)(tH,{})})]}),(0,i.jsxs)("div",{className:"zoomControl",children:[(0,i.jsx)("button",{className:"zoomOut",onClick:c,disabled:!p,children:(0,i.jsx)(tU,{})}),(0,i.jsx)("select",{className:"zoomSelect",value:r,onChange:e=>v(e.target.value),children:u.map((e,t)=>(0,i.jsx)("option",{value:e,children:e},t))}),(0,i.jsx)("button",{className:"zoomIn",onClick:l,disabled:!h,children:(0,i.jsx)(tB,{})})]})]})]})};tq.displayName="PDFOptionsBar";let tZ=tq,tJ=({file:e,hideOptionBar:t,containerClassName:n})=>{let{scrolledIndex:r,setCurrentPageNumber:o,scale:s,setScaleFit:a,numPages:l,setNumPages:c,handleZoomIn:u,handleZoomOut:f,nextPage:h,prevPage:p,scaleText:g,pdfFocusRef:m,goToPage:v,setZoomLevel:y,zoomInEnabled:x,zoomOutEnabled:E}=d(e);return(0,i.jsxs)("div",{className:(0,I.$)("pdf-viewer-container",n),children:[!t&&(0,i.jsx)(tZ,{file:e,scrolledIndex:r,numPages:l,scaleText:g,nextPage:h,prevPage:p,handleZoomIn:u,handleZoomOut:f,goToPage:v,setZoomLevel:y,zoomInEnabled:x,zoomOutEnabled:E}),(0,i.jsx)(tV,{ref:m,file:e,setIndex:o,scale:s,setScaleFit:a,setNumPages:c},`${e.id}`)]})}}}]);