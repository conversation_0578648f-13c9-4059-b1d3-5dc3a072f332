"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[487],{80487:(e,t,r)=>{r.r(t),r.d(t,{Checkbox:()=>N});var n=r(53891),a=r(73987),s=r(77310),i=r(80428),d=r(77292),o=r(25261),c=r(11425),l=r(14593),u=r(71138),p=r(4513),f="Checkbox",[b,h]=(0,i.A)(f),[k,x]=b(f),v=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:i,checked:c,defaultChecked:l,required:u,disabled:b,value:h="on",onCheckedChange:x,form:v,...m}=e,[y,E]=a.useState(null),j=(0,s.s)(t,e=>E(e)),N=a.useRef(!1),R=!y||v||!!y.closest("form"),[D,I]=(0,o.i)({prop:c,defaultProp:null!=l&&l,onChange:x,caller:f}),P=a.useRef(D);return a.useEffect(()=>{let e=null==y?void 0:y.form;if(e){let t=()=>I(P.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[y,I]),(0,n.jsxs)(k,{scope:r,state:D,disabled:b,children:[(0,n.jsx)(p.sG.button,{type:"button",role:"checkbox","aria-checked":w(D)?"mixed":D,"aria-required":u,"data-state":C(D),"data-disabled":b?"":void 0,disabled:b,value:h,...m,ref:j,onKeyDown:(0,d.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,d.m)(e.onClick,e=>{I(e=>!!w(e)||!e),R&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),R&&(0,n.jsx)(g,{control:y,bubbles:!N.current,name:i,value:h,checked:D,required:u,disabled:b,form:v,style:{transform:"translateX(-100%)"},defaultChecked:!w(l)&&l})]})});v.displayName=f;var m="CheckboxIndicator",y=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:a,...s}=e,i=x(m,r);return(0,n.jsx)(u.C,{present:a||w(i.state)||!0===i.state,children:(0,n.jsx)(p.sG.span,{"data-state":C(i.state),"data-disabled":i.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});y.displayName=m;var g=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,control:i,checked:d,bubbles:o=!0,defaultChecked:u,...f}=e,b=a.useRef(null),h=(0,s.s)(b,t),k=(0,c.Z)(d),x=(0,l.X)(i);a.useEffect(()=>{let e=b.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(k!==d&&t){let r=new Event("click",{bubbles:o});e.indeterminate=w(d),t.call(e,!w(d)&&d),e.dispatchEvent(r)}},[k,d,o]);let v=a.useRef(!w(d)&&d);return(0,n.jsx)(p.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=u?u:v.current,...f,tabIndex:-1,ref:h,style:{...f.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return"indeterminate"===e}function C(e){return w(e)?"indeterminate":e?"checked":"unchecked"}g.displayName="CheckboxBubbleInput";var E=r(25907),j=r(61971);function N(e){let{className:t,...r}=e;return(0,n.jsx)(v,{"data-slot":"checkbox",className:(0,j.cn)("border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs peer size-4 shrink-0 rounded-[4px] border outline-none transition-shadow focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,n.jsx)(y,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,n.jsx)(E.A,{className:"size-3.5"})})})}}}]);