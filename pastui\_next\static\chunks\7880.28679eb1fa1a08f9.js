"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7880],{37880:(e,t,n)=>{n.r(t),n.d(t,{NavigationMenu:()=>ep,NavigationMenuContent:()=>eN,NavigationMenuIndicator:()=>ej,NavigationMenuItem:()=>ex,NavigationMenuLink:()=>eC,NavigationMenuList:()=>ew,NavigationMenuTrigger:()=>eb,NavigationMenuViewport:()=>eR,navigationMenuTriggerStyle:()=>eh});var a=n(53891),o=n(73987),r=n(62866),i=n(80428),s=n(77292),u=n(4513),l=n(25261),d=n(77310),c=n(41105),v=n(71138),f=n(10145),m=n(333),g=n(72057),p=n(11425),w=n(637),x=n(15949),h=n(72748),b="NavigationMenu",[N,R,C]=(0,m.N)(b),[j,E,y]=(0,m.N)(b),[M,T]=(0,i.A)(b,[C,y]),[k,P]=M(b),[L,I]=M(b),_=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:r,onValueChange:i,defaultValue:s,delayDuration:v=200,skipDelayDuration:f=300,orientation:m="horizontal",dir:g,...p}=e,[w,x]=o.useState(null),h=(0,d.s)(t,e=>x(e)),N=(0,c.jH)(g),R=o.useRef(0),C=o.useRef(0),j=o.useRef(0),[E,y]=o.useState(!0),[M,T]=(0,l.i)({prop:r,onChange:e=>{let t=f>0;""!==e?(window.clearTimeout(j.current),t&&y(!1)):(window.clearTimeout(j.current),j.current=window.setTimeout(()=>y(!0),f)),null==i||i(e)},defaultProp:null!=s?s:"",caller:b}),k=o.useCallback(()=>{window.clearTimeout(C.current),C.current=window.setTimeout(()=>T(""),150)},[T]),P=o.useCallback(e=>{window.clearTimeout(C.current),T(e)},[T]),L=o.useCallback(e=>{M===e?window.clearTimeout(C.current):R.current=window.setTimeout(()=>{window.clearTimeout(C.current),T(e)},v)},[M,T,v]);return o.useEffect(()=>()=>{window.clearTimeout(R.current),window.clearTimeout(C.current),window.clearTimeout(j.current)},[]),(0,a.jsx)(A,{scope:n,isRootMenu:!0,value:M,dir:N,orientation:m,rootNavigationMenu:w,onTriggerEnter:e=>{window.clearTimeout(R.current),E?L(e):P(e)},onTriggerLeave:()=>{window.clearTimeout(R.current),k()},onContentEnter:()=>window.clearTimeout(C.current),onContentLeave:k,onItemSelect:e=>{T(t=>t===e?"":e)},onItemDismiss:()=>T(""),children:(0,a.jsx)(u.sG.nav,{"aria-label":"Main","data-orientation":m,dir:N,...p,ref:h})})});_.displayName=b;var D="NavigationMenuSub";o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:o,onValueChange:r,defaultValue:i,orientation:s="horizontal",...d}=e,c=P(D,n),[v,f]=(0,l.i)({prop:o,onChange:r,defaultProp:null!=i?i:"",caller:D});return(0,a.jsx)(A,{scope:n,isRootMenu:!1,value:v,dir:c.dir,orientation:s,rootNavigationMenu:c.rootNavigationMenu,onTriggerEnter:e=>f(e),onItemSelect:e=>f(e),onItemDismiss:()=>f(""),children:(0,a.jsx)(u.sG.div,{"data-orientation":s,...d,ref:t})})}).displayName=D;var A=e=>{let{scope:t,isRootMenu:n,rootNavigationMenu:r,dir:i,orientation:s,children:u,value:l,onItemSelect:d,onItemDismiss:c,onTriggerEnter:v,onTriggerLeave:m,onContentEnter:g,onContentLeave:w}=e,[h,b]=o.useState(null),[R,C]=o.useState(new Map),[j,E]=o.useState(null);return(0,a.jsx)(k,{scope:t,isRootMenu:n,rootNavigationMenu:r,value:l,previousValue:(0,p.Z)(l),baseId:(0,f.B)(),dir:i,orientation:s,viewport:h,onViewportChange:b,indicatorTrack:j,onIndicatorTrackChange:E,onTriggerEnter:(0,x.c)(v),onTriggerLeave:(0,x.c)(m),onContentEnter:(0,x.c)(g),onContentLeave:(0,x.c)(w),onItemSelect:(0,x.c)(d),onItemDismiss:(0,x.c)(c),onViewportContentChange:o.useCallback((e,t)=>{C(n=>(n.set(e,t),new Map(n)))},[]),onViewportContentRemove:o.useCallback(e=>{C(t=>t.has(e)?(t.delete(e),new Map(t)):t)},[]),children:(0,a.jsx)(N.Provider,{scope:t,children:(0,a.jsx)(L,{scope:t,items:R,children:u})})})},F="NavigationMenuList",S=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...o}=e,r=P(F,n),i=(0,a.jsx)(u.sG.ul,{"data-orientation":r.orientation,...o,ref:t});return(0,a.jsx)(u.sG.div,{style:{position:"relative"},ref:r.onIndicatorTrackChange,children:(0,a.jsx)(N.Slot,{scope:n,children:r.isRootMenu?(0,a.jsx)(ea,{asChild:!0,children:i}):i})})});S.displayName=F;var z="NavigationMenuItem",[K,O]=M(z),G=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:r,...i}=e,s=(0,f.B)(),l=o.useRef(null),d=o.useRef(null),c=o.useRef(null),v=o.useRef(()=>{}),m=o.useRef(!1),g=o.useCallback(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"start";if(l.current){v.current();let t=ei(l.current);t.length&&es("start"===e?t:t.reverse())}},[]),p=o.useCallback(()=>{if(l.current){let e=ei(l.current);e.length&&(v.current=function(e){return e.forEach(e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}),()=>{e.forEach(e=>{let t=e.dataset.tabindex;e.setAttribute("tabindex",t)})}}(e))}},[]);return(0,a.jsx)(K,{scope:n,value:r||s||"LEGACY_REACT_AUTO_VALUE",triggerRef:d,contentRef:l,focusProxyRef:c,wasEscapeCloseRef:m,onEntryKeyDown:g,onFocusProxyEnter:g,onRootContentClose:p,onContentFocusOutside:p,children:(0,a.jsx)(u.sG.li,{...i,ref:t})})});G.displayName=z;var V="NavigationMenuTrigger",H=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,disabled:r,...i}=e,l=P(V,e.__scopeNavigationMenu),c=O(V,e.__scopeNavigationMenu),v=o.useRef(null),f=(0,d.s)(v,c.triggerRef,t),m=ed(l.baseId,c.value),g=ec(l.baseId,c.value),p=o.useRef(!1),w=o.useRef(!1),x=c.value===l.value;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.ItemSlot,{scope:n,value:c.value,children:(0,a.jsx)(er,{asChild:!0,children:(0,a.jsx)(u.sG.button,{id:m,disabled:r,"data-disabled":r?"":void 0,"data-state":el(x),"aria-expanded":x,"aria-controls":g,...i,ref:f,onPointerEnter:(0,s.m)(e.onPointerEnter,()=>{w.current=!1,c.wasEscapeCloseRef.current=!1}),onPointerMove:(0,s.m)(e.onPointerMove,ev(()=>{r||w.current||c.wasEscapeCloseRef.current||p.current||(l.onTriggerEnter(c.value),p.current=!0)})),onPointerLeave:(0,s.m)(e.onPointerLeave,ev(()=>{r||(l.onTriggerLeave(),p.current=!1)})),onClick:(0,s.m)(e.onClick,()=>{l.onItemSelect(c.value),w.current=x}),onKeyDown:(0,s.m)(e.onKeyDown,e=>{let t={horizontal:"ArrowDown",vertical:"rtl"===l.dir?"ArrowLeft":"ArrowRight"}[l.orientation];x&&e.key===t&&(c.onEntryKeyDown(),e.preventDefault())})})})}),x&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.bL,{"aria-hidden":!0,tabIndex:0,ref:c.focusProxyRef,onFocus:e=>{let t=c.contentRef.current,n=e.relatedTarget,a=n===v.current,o=null==t?void 0:t.contains(n);(a||!o)&&c.onFocusProxyEnter(a?"start":"end")}}),l.viewport&&(0,a.jsx)("span",{"aria-owns":g})]})]})});H.displayName=V;var U="navigationMenu.linkSelect",W=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,active:o,onSelect:r,...i}=e;return(0,a.jsx)(er,{asChild:!0,children:(0,a.jsx)(u.sG.a,{"data-active":o?"":void 0,"aria-current":o?"page":void 0,...i,ref:t,onClick:(0,s.m)(e.onClick,e=>{let t=e.target,n=new CustomEvent(U,{bubbles:!0,cancelable:!0});if(t.addEventListener(U,e=>null==r?void 0:r(e),{once:!0}),(0,u.hO)(t,n),!n.defaultPrevented&&!e.metaKey){let e=new CustomEvent(Q,{bubbles:!0,cancelable:!0});(0,u.hO)(t,e)}},{checkForDefaultPrevented:!1})})})});W.displayName="NavigationMenuLink";var q="NavigationMenuIndicator",B=o.forwardRef((e,t)=>{let{forceMount:n,...o}=e,i=P(q,e.__scopeNavigationMenu),s=!!i.value;return i.indicatorTrack?r.createPortal((0,a.jsx)(v.C,{present:n||s,children:(0,a.jsx)(Y,{...o,ref:t})}),i.indicatorTrack):null});B.displayName=q;var Y=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...r}=e,i=P(q,n),s=R(n),[l,d]=o.useState(null),[c,v]=o.useState(null),f="horizontal"===i.orientation,m=!!i.value;o.useEffect(()=>{var e;let t=null==(e=s().find(e=>e.value===i.value))?void 0:e.ref.current;t&&d(t)},[s,i.value]);let g=()=>{l&&v({size:f?l.offsetWidth:l.offsetHeight,offset:f?l.offsetLeft:l.offsetTop})};return eu(l,g),eu(i.indicatorTrack,g),c?(0,a.jsx)(u.sG.div,{"aria-hidden":!0,"data-state":m?"visible":"hidden","data-orientation":i.orientation,...r,ref:t,style:{position:"absolute",...f?{left:0,width:c.size+"px",transform:"translateX(".concat(c.offset,"px)")}:{top:0,height:c.size+"px",transform:"translateY(".concat(c.offset,"px)")},...r.style}}):null}),X="NavigationMenuContent",Z=o.forwardRef((e,t)=>{let{forceMount:n,...o}=e,r=P(X,e.__scopeNavigationMenu),i=O(X,e.__scopeNavigationMenu),u=(0,d.s)(i.contentRef,t),l=i.value===r.value,c={value:i.value,triggerRef:i.triggerRef,focusProxyRef:i.focusProxyRef,wasEscapeCloseRef:i.wasEscapeCloseRef,onContentFocusOutside:i.onContentFocusOutside,onRootContentClose:i.onRootContentClose,...o};return r.viewport?(0,a.jsx)(J,{forceMount:n,...c,ref:u}):(0,a.jsx)(v.C,{present:n||l,children:(0,a.jsx)($,{"data-state":el(l),...c,ref:u,onPointerEnter:(0,s.m)(e.onPointerEnter,r.onContentEnter),onPointerLeave:(0,s.m)(e.onPointerLeave,ev(r.onContentLeave)),style:{pointerEvents:!l&&r.isRootMenu?"none":void 0,...c.style}})})});Z.displayName=X;var J=o.forwardRef((e,t)=>{let{onViewportContentChange:n,onViewportContentRemove:a}=P(X,e.__scopeNavigationMenu);return(0,w.N)(()=>{n(e.value,{ref:t,...e})},[e,t,n]),(0,w.N)(()=>()=>a(e.value),[e.value,a]),null}),Q="navigationMenu.rootContentDismiss",$=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:r,triggerRef:i,focusProxyRef:u,wasEscapeCloseRef:l,onRootContentClose:c,onContentFocusOutside:v,...f}=e,m=P(X,n),p=o.useRef(null),w=(0,d.s)(p,t),x=ed(m.baseId,r),h=ec(m.baseId,r),b=R(n),N=o.useRef(null),{onItemDismiss:C}=m;o.useEffect(()=>{let e=p.current;if(m.isRootMenu&&e){let t=()=>{var t;C(),c(),e.contains(document.activeElement)&&(null==(t=i.current)||t.focus())};return e.addEventListener(Q,t),()=>e.removeEventListener(Q,t)}},[m.isRootMenu,e.value,i,C,c]);let j=o.useMemo(()=>{let e=b().map(e=>e.value);"rtl"===m.dir&&e.reverse();let t=e.indexOf(m.value),n=e.indexOf(m.previousValue),a=r===m.value,o=n===e.indexOf(r);if(!a&&!o)return N.current;let i=(()=>{if(t!==n){if(a&&-1!==n)return t>n?"from-end":"from-start";if(o&&-1!==t)return t>n?"to-start":"to-end"}return null})();return N.current=i,i},[m.previousValue,m.value,m.dir,b,r]);return(0,a.jsx)(ea,{asChild:!0,children:(0,a.jsx)(g.qW,{id:h,"aria-labelledby":x,"data-motion":j,"data-orientation":m.orientation,...f,ref:w,disableOutsidePointerEvents:!1,onDismiss:()=>{var e;let t=new Event(Q,{bubbles:!0,cancelable:!0});null==(e=p.current)||e.dispatchEvent(t)},onFocusOutside:(0,s.m)(e.onFocusOutside,e=>{var t;v();let n=e.target;(null==(t=m.rootNavigationMenu)?void 0:t.contains(n))&&e.preventDefault()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{var t;let n=e.target,a=b().some(e=>{var t;return null==(t=e.ref.current)?void 0:t.contains(n)}),o=m.isRootMenu&&(null==(t=m.viewport)?void 0:t.contains(n));(a||o||!m.isRootMenu)&&e.preventDefault()}),onKeyDown:(0,s.m)(e.onKeyDown,e=>{let t=e.altKey||e.ctrlKey||e.metaKey;if("Tab"===e.key&&!t){let t=ei(e.currentTarget),a=document.activeElement,o=t.findIndex(e=>e===a);if(es(e.shiftKey?t.slice(0,o).reverse():t.slice(o+1,t.length)))e.preventDefault();else{var n;null==(n=u.current)||n.focus()}}}),onEscapeKeyDown:(0,s.m)(e.onEscapeKeyDown,e=>{l.current=!0})})})}),ee="NavigationMenuViewport",et=o.forwardRef((e,t)=>{let{forceMount:n,...o}=e,r=!!P(ee,e.__scopeNavigationMenu).value;return(0,a.jsx)(v.C,{present:n||r,children:(0,a.jsx)(en,{...o,ref:t})})});et.displayName=ee;var en=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,children:r,...i}=e,l=P(ee,n),c=(0,d.s)(t,l.onViewportChange),f=I(X,e.__scopeNavigationMenu),[m,g]=o.useState(null),[p,w]=o.useState(null),x=m?(null==m?void 0:m.width)+"px":void 0,h=m?(null==m?void 0:m.height)+"px":void 0,b=!!l.value,N=b?l.value:l.previousValue;return eu(p,()=>{p&&g({width:p.offsetWidth,height:p.offsetHeight})}),(0,a.jsx)(u.sG.div,{"data-state":el(b),"data-orientation":l.orientation,...i,ref:c,style:{pointerEvents:!b&&l.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":x,"--radix-navigation-menu-viewport-height":h,...i.style},onPointerEnter:(0,s.m)(e.onPointerEnter,l.onContentEnter),onPointerLeave:(0,s.m)(e.onPointerLeave,ev(l.onContentLeave)),children:Array.from(f.items).map(e=>{let[t,{ref:n,forceMount:o,...r}]=e,i=N===t;return(0,a.jsx)(v.C,{present:o||i,children:(0,a.jsx)($,{...r,ref:(0,d.t)(n,e=>{i&&e&&w(e)})})},t)})})}),ea=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...o}=e,r=P("FocusGroup",n);return(0,a.jsx)(j.Provider,{scope:n,children:(0,a.jsx)(j.Slot,{scope:n,children:(0,a.jsx)(u.sG.div,{dir:r.dir,...o,ref:t})})})}),eo=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],er=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...o}=e,r=E(n),i=P("FocusGroupItem",n);return(0,a.jsx)(j.ItemSlot,{scope:n,children:(0,a.jsx)(u.sG.button,{...o,ref:t,onKeyDown:(0,s.m)(e.onKeyDown,e=>{if(["Home","End",...eo].includes(e.key)){let t=r().map(e=>e.ref.current);if(["rtl"===i.dir?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(e.key)&&t.reverse(),eo.includes(e.key)){let n=t.indexOf(e.currentTarget);t=t.slice(n+1)}setTimeout(()=>es(t)),e.preventDefault()}})})})});function ei(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function es(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}function eu(e,t){let n=(0,x.c)(t);(0,w.N)(()=>{let t=0;if(e){let a=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return a.observe(e),()=>{window.cancelAnimationFrame(t),a.unobserve(e)}}},[e,n])}function el(e){return e?"open":"closed"}function ed(e,t){return"".concat(e,"-trigger-").concat(t)}function ec(e,t){return"".concat(e,"-content-").concat(t)}function ev(e){return t=>"mouse"===t.pointerType?e(t):void 0}var ef=n(52722),em=n(68135),eg=n(61971);function ep(e){let{className:t,children:n,viewport:o=!0,...r}=e;return(0,a.jsxs)(_,{"data-slot":"navigation-menu","data-viewport":o,className:(0,eg.cn)("group/navigation-menu relative flex max-w-max flex-1 items-center justify-center",t),...r,children:[n,o&&(0,a.jsx)(eR,{})]})}function ew(e){let{className:t,...n}=e;return(0,a.jsx)(S,{"data-slot":"navigation-menu-list",className:(0,eg.cn)("group flex flex-1 list-none items-center justify-center gap-1",t),...n})}function ex(e){let{className:t,...n}=e;return(0,a.jsx)(G,{"data-slot":"navigation-menu-item",className:(0,eg.cn)("relative",t),...n})}let eh=(0,ef.F)("group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1");function eb(e){let{className:t,children:n,...o}=e;return(0,a.jsxs)(H,{"data-slot":"navigation-menu-trigger",className:(0,eg.cn)(eh(),"group",t),...o,children:[n," ",(0,a.jsx)(em.A,{className:"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})}function eN(e){let{className:t,...n}=e;return(0,a.jsx)(Z,{"data-slot":"navigation-menu-content",className:(0,eg.cn)("data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 left-0 top-0 w-full p-2 pr-2.5 md:absolute md:w-auto","group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200",t),...n})}function eR(e){let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,eg.cn)("absolute left-0 top-full isolate z-50 flex justify-center"),children:(0,a.jsx)(et,{"data-slot":"navigation-menu-viewport",className:(0,eg.cn)("origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]",t),...n})})}function eC(e){let{className:t,...n}=e;return(0,a.jsx)(W,{"data-slot":"navigation-menu-link",className:(0,eg.cn)("data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm outline-none transition-all focus-visible:outline-1 focus-visible:ring-[3px] [&_svg:not([class*='size-'])]:size-4",t),...n})}function ej(e){let{className:t,...n}=e;return(0,a.jsx)(B,{"data-slot":"navigation-menu-indicator",className:(0,eg.cn)("data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden",t),...n,children:(0,a.jsx)("div",{className:"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md"})})}}}]);