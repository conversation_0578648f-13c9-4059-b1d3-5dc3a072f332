// 引用处理模块 - 独立模块

/**
 * 引用处理器类
 */
class CitationProcessor {
  constructor() {
    this.debug = true; // 开启调试模式
  }

  /**
   * 处理响应文本，提取引用数据并转换引用格式
   */
  processResponse(responseText) {
    try {
      this.log("=== 开始处理响应 ===");
      this.log("原始响应长度:", responseText.length);
      this.log("原始响应前500字符:", responseText.substring(0, 500));

      // 解析流式响应，提取实际内容
      let actualContent = "";
      let citationData = {};

      // 尝试从流式响应中提取tool_output.content
      const toolOutputMatch = responseText.match(
        /"tool_output":\s*{[^}]*"content":\s*"([^"]*(?:\\.[^"]*)*)"/
      );

      if (toolOutputMatch) {
        // 解码Unicode转义序列
        actualContent = toolOutputMatch[1].replace(
          /\\u([0-9a-fA-F]{4})/g,
          (_, code) => String.fromCharCode(parseInt(code, 16))
        );
        // 处理换行符
        actualContent = actualContent.replace(/\\n/g, "\n");
        this.log("✅ 从tool_output中提取的内容:", actualContent);
      } else {
        this.log("❌ 未找到tool_output内容，使用原始响应");
        actualContent = responseText;
      }

      // 提取引用数据
      citationData = this.extractCitationData(actualContent);

      // 移除引用数据注释
      actualContent = actualContent
        .replace(/<!-- CITATION_DATA: .*? -->/s, "")
        .trim();

      // 处理引用，显示排名序号、文档名和内容
      const processedContent = this.processCitations(
        actualContent,
        citationData
      );

      return processedContent.trim() || "抱歉，没有收到有效回复。";
    } catch (error) {
      this.error("❌ 解析响应失败:", error);
      return "抱歉，解析回复时出现错误。";
    }
  }

  /**
   * 提取引用数据
   */
  extractCitationData(content) {
    const citationMatch = content.match(/<!-- CITATION_DATA: (.*?) -->/s);
    if (!citationMatch) {
      this.log("❌ 未找到 CITATION_DATA 注释");
      return {};
    }

    try {
      this.log("✅ 找到引用数据:", citationMatch[1]);
      const citationData = JSON.parse(citationMatch[1]);
      this.log("✅ 解析后的引用数据:", citationData);
      return citationData;
    } catch (e) {
      this.error("❌ 解析引用数据失败:", e);
      this.log("尝试修复JSON格式...");

      // 尝试修复常见的JSON格式问题
      let fixedJson = citationMatch[1].trim();
      if (!fixedJson.startsWith("{")) {
        fixedJson = "{" + fixedJson;
      }
      if (!fixedJson.endsWith("}")) {
        fixedJson = fixedJson + "}";
      }

      try {
        const citationData = JSON.parse(fixedJson);
        this.log("✅ 修复后解析成功:", citationData);
        return citationData;
      } catch (e2) {
        this.error("❌ 修复后仍然解析失败:", e2);
        return {};
      }
    }
  }

  /**
   * 处理引用信息
   */
  processCitations(text, citationData) {
    this.log("=== 处理引用信息 ===");
    this.log("输入文本:", text);
    this.log("引用数据:", citationData);

    // 首先尝试处理 [citation:id] 格式
    const citationRegex = /\[citation:([a-f0-9-]+)\]/gi;
    let processedText = text.replace(citationRegex, (_, citationId) => {
      this.log("✅ 找到 [citation:] 格式引用ID:", citationId);
      return this.createCitationElement(citationId, citationData[citationId]);
    });

    // 如果没有找到 [citation:] 格式，尝试处理数字引用格式
    if (processedText === text) {
      this.log("未找到 [citation:] 格式，尝试处理数字引用格式");

      // 处理 [1] 格式
      const bracketRegex = /\[(\d+)\]/g;
      processedText = text.replace(bracketRegex, (_, number) => {
        this.log("✅ 找到 [数字] 格式引用:", number);
        const citation = this.findCitationByRank(
          citationData,
          parseInt(number)
        );
        return this.createCitationElement(`rank-${number}`, citation, number);
      });

      // 如果还没有处理，尝试 【数字】 格式
      if (processedText === text) {
        const chineseRegex = /【(\d+)】/g;
        processedText = text.replace(chineseRegex, (_, number) => {
          this.log("✅ 找到 【数字】 格式引用:", number);
          const citation = this.findCitationByRank(
            citationData,
            parseInt(number)
          );
          return this.createCitationElement(`rank-${number}`, citation, number);
        });
      }
    }

    this.log("✅ 处理后的文本:", processedText);
    return processedText;
  }

  /**
   * 根据排名查找引用数据
   */
  findCitationByRank(citationData, rank) {
    for (const [id, data] of Object.entries(citationData)) {
      if (data.rank === rank) {
        this.log("✅ 根据排名找到引用数据:", data);
        return data;
      }
    }
    return null;
  }

  /**
   * 创建引用元素
   */
  createCitationElement(citationId, citation, rankNumber = null) {
    if (!citation) {
      this.log("❌ 未找到引用数据，使用简单格式");
      const displayNumber = rankNumber || citationId.substring(0, 1);
      return `<span class="citation citation-debug">${displayNumber}</span>`;
    }

    this.log("✅ 创建引用元素:", citation);

    // 简化显示的文件名
    const displayName = citation.filename
      .replace(/^常见问题类-\d+_/, "")
      .replace(/\.txt$/, "");

    // 格式化相似度分数
    const similarityPercent = Math.round(citation.similarity_score * 100);

    // 清理内容中的换行符和多余空格
    const cleanContent = citation.content
      .replace(/\\n/g, "\n")
      .replace(/\n\n+/g, "\n\n")
      .trim();

    // 创建悬浮窗内容
    const tooltipContent =
      `<button class="tooltip-close" onclick="window.citationProcessor.hideTooltip()">&times;</button>` +
      `<div class="tooltip-header">${this.escapeHtml(displayName)}</div>` +
      `<div class="tooltip-meta">排名: #${citation.rank} | 相似度: ${similarityPercent}%</div>` +
      `<div class="tooltip-content">${this.escapeHtml(cleanContent)}</div>`;

    return (
      `<span class="citation" onmouseenter="window.citationProcessor.showTooltip(this)" onmouseleave="window.citationProcessor.scheduleHideTooltip()">` +
      `<span class="citation-number">${citation.rank}</span>` +
      `<span class="citation-tooltip tooltip-debug">${tooltipContent}</span>` +
      `</span>`
    );
  }

  /**
   * 显示悬浮窗
   */
  showTooltip(element) {
    this.log("显示悬浮窗");

    // 清除隐藏定时器
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }

    const tooltip = element.querySelector(".citation-tooltip");
    if (tooltip) {
      tooltip.style.visibility = "visible";
      tooltip.style.opacity = "1";
      tooltip.style.pointerEvents = "auto";
    }
  }

  /**
   * 计划隐藏悬浮窗（延迟执行）
   */
  scheduleHideTooltip() {
    this.log("计划隐藏悬浮窗");

    // 设置延迟隐藏，给用户时间移动鼠标到悬浮窗上
    this.hideTimer = setTimeout(() => {
      this.hideTooltip();
    }, 300);
  }

  /**
   * 立即隐藏悬浮窗
   */
  hideTooltip() {
    this.log("隐藏悬浮窗");

    // 清除定时器
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }

    // 隐藏所有悬浮窗
    const tooltips = document.querySelectorAll(".citation-tooltip");
    tooltips.forEach((tooltip) => {
      tooltip.style.visibility = "hidden";
      tooltip.style.opacity = "0";
      tooltip.style.pointerEvents = "none";
    });
  }

  /**
   * HTML转义
   */
  escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 日志输出
   */
  log(...args) {
    if (this.debug) {
      console.log(...args);
    }
  }

  /**
   * 错误输出
   */
  error(...args) {
    if (this.debug) {
      console.error(...args);
    }
  }
}

// 创建全局实例
window.citationProcessor = new CitationProcessor();
