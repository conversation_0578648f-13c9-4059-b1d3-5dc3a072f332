"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4466,9370],{39370:(e,t,r)=>{r.r(t),r.d(t,{Form:()=>i,FormControl:()=>b,FormDescription:()=>v,FormField:()=>m,FormItem:()=>x,FormLabel:()=>p,FormMessage:()=>F,useFormField:()=>u});var a=r(53891),o=r(7349),n=r(73987),l=r(53459),s=r(74466),d=r(61971);let i=l.Op,c=n.createContext({}),m=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(l.xI,{...t})})},u=()=>{let e=n.useContext(c),t=n.useContext(f),{getFieldState:r}=(0,l.xW)(),a=(0,l.lN)({name:e.name}),o=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:s}=t;return{id:s,name:e.name,formItemId:"".concat(s,"-form-item"),formDescriptionId:"".concat(s,"-form-item-description"),formMessageId:"".concat(s,"-form-item-message"),...o}},f=n.createContext({});function x(e){let{className:t,...r}=e,o=n.useId();return(0,a.jsx)(f.Provider,{value:{id:o},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,d.cn)("grid gap-2",t),...r})})}function p(e){let{className:t,...r}=e,{error:o,formItemId:n}=u();return(0,a.jsx)(s.Label,{"data-slot":"form-label","data-error":!!o,className:(0,d.cn)("data-[error=true]:text-destructive",t),htmlFor:n,...r})}function b(e){let{...t}=e,{error:r,formItemId:n,formDescriptionId:l,formMessageId:s}=u();return(0,a.jsx)(o.DX,{"data-slot":"form-control",id:n,"aria-describedby":r?"".concat(l," ").concat(s):"".concat(l),"aria-invalid":!!r,...t})}function v(e){let{className:t,...r}=e,{formDescriptionId:o}=u();return(0,a.jsx)("p",{"data-slot":"form-description",id:o,className:(0,d.cn)("text-muted-foreground text-sm",t),...r})}function F(e){var t;let{className:r,...o}=e,{error:n,formMessageId:l}=u(),s=n?String(null!=(t=null==n?void 0:n.message)?t:""):o.children;return s?(0,a.jsx)("p",{"data-slot":"form-message",id:l,className:(0,d.cn)("text-destructive text-sm",r),...o,children:s}):null}},74466:(e,t,r)=>{r.r(t),r.d(t,{Label:()=>d});var a=r(53891),o=r(73987),n=r(4513),l=o.forwardRef((e,t)=>(0,a.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var s=r(61971);function d(e){let{className:t,...r}=e;return(0,a.jsx)(l,{"data-slot":"label",className:(0,s.cn)("flex select-none items-center gap-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50",t),...r})}}}]);