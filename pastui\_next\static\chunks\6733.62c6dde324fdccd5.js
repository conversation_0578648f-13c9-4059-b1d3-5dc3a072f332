"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6733],{96733:(t,o,e)=>{e.r(o),e.d(o,{Tooltip:()=>s,TooltipContent:()=>d,TooltipProvider:()=>n,TooltipTrigger:()=>l});var i=e(53891),a=e(17771);e(73987);var r=e(61971);function n(t){let{delayDuration:o=0,...e}=t;return(0,i.jsx)(a.Kq,{"data-slot":"tooltip-provider",delayDuration:o,...e})}function s(t){let{...o}=t;return(0,i.jsx)(n,{children:(0,i.jsx)(a.bL,{"data-slot":"tooltip",...o})})}function l(t){let{...o}=t;return(0,i.jsx)(a.l9,{"data-slot":"tooltip-trigger",...o})}function d(t){let{className:o,sideOffset:e=0,children:n,...s}=t;return(0,i.jsx)(a.ZL,{children:(0,i.jsxs)(a.UC,{"data-slot":"tooltip-content",sideOffset:e,className:(0,r.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--radix-tooltip-content-transform-origin) z-50 w-fit text-balance rounded-md px-3 py-1.5 text-xs",o),...s,children:[n,(0,i.jsx)(a.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}}}]);