"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2081],{23279:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(75779).A)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},82081:(e,t,n)=>{n.r(t),n.d(t,{InputOTP:()=>E,InputOTPGroup:()=>x,InputOTPSeparator:()=>C,InputOTPSlot:()=>y});var r=n(53891),a=n(73987),l=Object.defineProperty,o=Object.defineProperties,i=Object.getOwnPropertyDescriptors,u=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,d=(e,t,n)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p=(e,t)=>{for(var n in t||(t={}))s.call(t,n)&&d(e,n,t[n]);if(u)for(var n of u(t))c.call(t,n)&&d(e,n,t[n]);return e},m=(e,t)=>o(e,i(t)),v=(e,t)=>{var n={};for(var r in e)s.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&u)for(var r of u(e))0>t.indexOf(r)&&c.call(e,r)&&(n[r]=e[r]);return n},f=a.createContext({}),h=a.forwardRef((e,t)=>{var n,r,l,o,i,{value:u,onChange:s,maxLength:c,textAlign:d="left",pattern:h,placeholder:w,inputMode:S="numeric",onComplete:E,pushPasswordManagerStrategy:x="increase-width",pasteTransformer:y,containerClassName:C,noScriptCSSFallback:P=b,render:k,children:M}=e,j=v(e,["value","onChange","maxLength","textAlign","pattern","placeholder","inputMode","onComplete","pushPasswordManagerStrategy","pasteTransformer","containerClassName","noScriptCSSFallback","render","children"]);let[T,O]=a.useState("string"==typeof j.defaultValue?j.defaultValue:""),R=null!=u?u:T,D=function(e){let t=a.useRef();return a.useEffect(()=>{t.current=e}),t.current}(R),A=a.useCallback(e=>{null==s||s(e),O(e)},[s]),I=a.useMemo(()=>h?"string"==typeof h?new RegExp(h):h:null,[h]),W=a.useRef(null),_=a.useRef(null),B=a.useRef({value:R,onChange:A,isIOS:"undefined"!=typeof window&&(null==(r=null==(n=null==window?void 0:window.CSS)?void 0:n.supports)?void 0:r.call(n,"-webkit-touch-callout","none"))}),N=a.useRef({prev:[null==(l=W.current)?void 0:l.selectionStart,null==(o=W.current)?void 0:o.selectionEnd,null==(i=W.current)?void 0:i.selectionDirection]});a.useImperativeHandle(t,()=>W.current,[]),a.useEffect(()=>{let e=W.current,t=_.current;if(!e||!t)return;function n(){if(document.activeElement!==e){z(null),q(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,a=e.maxLength,l=e.value,o=N.current.prev,i=-1,u=-1,s;if(0!==l.length&&null!==t&&null!==n){let e=t===n,r=t===l.length&&l.length<a;if(e&&!r){if(0===t)i=0,u=1,s="forward";else if(t===a)i=t-1,u=t,s="backward";else if(a>1&&l.length>1){let e=0;if(null!==o[0]&&null!==o[1]){s=t<o[1]?"backward":"forward";let n=o[0]===o[1]&&o[0]<a;"backward"!==s||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&W.current.setSelectionRange(i,u,s)}let c=-1!==i?i:t,d=-1!==u?u:n,p=null!=s?s:r;z(c),q(d),N.current.prev=[c,d,p]}if(B.current.value!==e.value&&B.current.onChange(e.value),N.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&$(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";g(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),g(e.sheet,`[data-input-otp]:autofill { ${t} }`),g(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),g(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),g(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let a=new ResizeObserver(r);return a.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),a.disconnect()}},[]);let[F,H]=a.useState(!1),[L,$]=a.useState(!1),[G,z]=a.useState(null),[V,q]=a.useState(null);a.useEffect(()=>{!function(e){setTimeout(e,0),setTimeout(e,10),setTimeout(e,50)}(()=>{var e,t,n,r;null==(e=W.current)||e.dispatchEvent(new Event("input"));let a=null==(t=W.current)?void 0:t.selectionStart,l=null==(n=W.current)?void 0:n.selectionEnd,o=null==(r=W.current)?void 0:r.selectionDirection;null!==a&&null!==l&&(z(a),q(l),N.current.prev=[a,l,o])})},[R,L]),a.useEffect(()=>{void 0!==D&&R!==D&&D.length<c&&R.length===c&&(null==E||E(R))},[c,E,D,R]);let U=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:r}){let[l,o]=a.useState(!1),[i,u]=a.useState(!1),[s,c]=a.useState(!1),d=a.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&l&&i,[l,i,n]),p=a.useCallback(()=>{let r=e.current,a=t.current;if(!r||!a||s||"none"===n)return;let l=r.getBoundingClientRect().left+r.offsetWidth,i=r.getBoundingClientRect().top+r.offsetHeight/2;0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(l-18,i)===r||(o(!0),c(!0))},[e,t,s,n]);return a.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){u(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let a=setInterval(r,1e3);return()=>{clearInterval(a)}},[e,n]),a.useEffect(()=>{let e=r||document.activeElement===t.current;if("none"===n||!e)return;let a=setTimeout(p,0),l=setTimeout(p,2e3),o=setTimeout(p,5e3),i=setTimeout(()=>{c(!0)},6e3);return()=>{clearTimeout(a),clearTimeout(l),clearTimeout(o),clearTimeout(i)}},[t,r,n,p]),{hasPWMBadge:l,willPushPWMBadge:d,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:_,inputRef:W,pushPasswordManagerStrategy:x,isFocused:L}),J=a.useCallback(e=>{let t=e.currentTarget.value.slice(0,c);if(t.length>0&&I&&!I.test(t))return void e.preventDefault();"string"==typeof D&&t.length<D.length&&document.dispatchEvent(new Event("selectionchange")),A(t)},[c,A,D,I]),K=a.useCallback(()=>{var e;if(W.current){let t=Math.min(W.current.value.length,c-1),n=W.current.value.length;null==(e=W.current)||e.setSelectionRange(t,n),z(t),q(n)}$(!0)},[c]),Q=a.useCallback(e=>{var t,n;let r=W.current;if(!y&&(!B.current.isIOS||!e.clipboardData||!r))return;let a=e.clipboardData.getData("text/plain"),l=y?y(a):a;e.preventDefault();let o=null==(t=W.current)?void 0:t.selectionStart,i=null==(n=W.current)?void 0:n.selectionEnd,u=(o!==i?R.slice(0,o)+l+R.slice(i):R.slice(0,o)+l+R.slice(o)).slice(0,c);if(u.length>0&&I&&!I.test(u))return;r.value=u,A(u);let s=Math.min(u.length,c-1),d=u.length;r.setSelectionRange(s,d),z(s),q(d)},[c,A,I,R]),X=a.useMemo(()=>({position:"relative",cursor:j.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[j.disabled]),Y=a.useMemo(()=>({position:"absolute",inset:0,width:U.willPushPWMBadge?`calc(100% + ${U.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:U.willPushPWMBadge?`inset(0 ${U.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:d,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[U.PWM_BADGE_SPACE_WIDTH,U.willPushPWMBadge,d]),Z=a.useMemo(()=>a.createElement("input",m(p({autoComplete:j.autoComplete||"one-time-code"},j),{"data-input-otp":!0,"data-input-otp-placeholder-shown":0===R.length||void 0,"data-input-otp-mss":G,"data-input-otp-mse":V,inputMode:S,pattern:null==I?void 0:I.source,"aria-placeholder":w,style:Y,maxLength:c,value:R,ref:W,onPaste:e=>{var t;Q(e),null==(t=j.onPaste)||t.call(j,e)},onChange:J,onMouseOver:e=>{var t;H(!0),null==(t=j.onMouseOver)||t.call(j,e)},onMouseLeave:e=>{var t;H(!1),null==(t=j.onMouseLeave)||t.call(j,e)},onFocus:e=>{var t;K(),null==(t=j.onFocus)||t.call(j,e)},onBlur:e=>{var t;$(!1),null==(t=j.onBlur)||t.call(j,e)}})),[J,K,Q,S,Y,c,V,G,j,null==I?void 0:I.source,R]),ee=a.useMemo(()=>({slots:Array.from({length:c}).map((e,t)=>{var n;let r=L&&null!==G&&null!==V&&(G===V&&t===G||t>=G&&t<V),a=void 0!==R[t]?R[t]:null;return{char:a,placeholderChar:void 0!==R[0]?null:null!=(n=null==w?void 0:w[t])?n:null,isActive:r,hasFakeCaret:r&&null===a}}),isFocused:L,isHovering:!j.disabled&&F}),[L,F,c,V,G,j.disabled,R]),et=a.useMemo(()=>k?k(ee):a.createElement(f.Provider,{value:ee},M),[M,ee,k]);return a.createElement(a.Fragment,null,null!==P&&a.createElement("noscript",null,a.createElement("style",null,P)),a.createElement("div",{ref:_,"data-input-otp-container":!0,style:X,className:C},et,a.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},Z)))});function g(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}h.displayName="Input";var b=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`,w=n(23279),S=n(61971);function E(e){let{className:t,containerClassName:n,...a}=e;return(0,r.jsx)(h,{"data-slot":"input-otp",containerClassName:(0,S.cn)("flex items-center gap-2 has-disabled:opacity-50",n),className:(0,S.cn)("disabled:cursor-not-allowed",t),...a})}function x(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"input-otp-group",className:(0,S.cn)("flex items-center",t),...n})}function y(e){var t;let{index:n,className:l,...o}=e,i=a.useContext(f),{char:u,hasFakeCaret:s,isActive:c}=null!=(t=null==i?void 0:i.slots[n])?t:{};return(0,r.jsxs)("div",{"data-slot":"input-otp-slot","data-active":c,className:(0,S.cn)("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input shadow-xs relative flex h-9 w-9 items-center justify-center border-y border-r text-sm outline-none transition-all first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]",l),...o,children:[u,s&&(0,r.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-caret-blink bg-foreground h-4 w-px duration-1000"})})]})}function C(e){let{...t}=e;return(0,r.jsx)("div",{"data-slot":"input-otp-separator",role:"separator",...t,children:(0,r.jsx)(w.A,{})})}}}]);