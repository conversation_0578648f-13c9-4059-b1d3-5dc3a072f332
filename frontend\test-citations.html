<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>引用功能测试</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        margin: 20px;
        background: #f5f5f5;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .citation {
        display: inline-block;
        background: #e3f2fd;
        color: #1976d2;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        margin: 0 3px;
        cursor: pointer;
        border: 1px solid #bbdefb;
        position: relative;
        font-weight: 500;
        transition: all 0.2s ease;
      }
      .citation:hover {
        background: #bbdefb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .citation-rank {
        background: #1976d2;
        color: white;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        margin-right: 4px;
      }
      .citation-tooltip {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 11px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.2s ease;
        z-index: 1000;
        margin-bottom: 5px;
        max-width: 200px;
        text-align: center;
      }
      .citation-tooltip::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-top-color: #333;
      }
      .citation:hover .citation-tooltip {
        opacity: 1;
        visibility: visible;
      }
      .citation-content {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 12px;
        font-size: 12px;
        white-space: pre-wrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1001;
        margin-bottom: 10px;
        max-width: 300px;
        max-height: 200px;
        overflow-y: auto;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        color: #333;
        line-height: 1.4;
      }
      .citation-content::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 8px solid transparent;
        border-top-color: white;
      }
      .citation:hover .citation-content {
        opacity: 1;
        visibility: visible;
      }
      .test-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 10px 5px;
      }
      .test-button:hover {
        background: #0056b3;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>引用功能测试页面</h1>
      
      <h2>测试引用显示</h2>
      <p>这是一个测试段落，包含引用：
        <span class="citation" onclick="showCitationDetails('test-id-1', testCitation1)">
          <span class="citation-rank">1</span>
          <span class="citation-tooltip">排名: #1 | 相似度: 85%<br>电子发票重复报销防范措施.txt</span>
          <span class="citation-content">电子发票重复报销是企业财务管理中的常见问题。为了防范这种情况，企业应建立完善的发票管理制度...</span>
          📄 电子发票重复报销防范措施
        </span>
        和另一个引用
        <span class="citation" onclick="showCitationDetails('test-id-2', testCitation2)">
          <span class="citation-rank">2</span>
          <span class="citation-tooltip">排名: #2 | 相似度: 78%<br>发票开具错误的更正和红冲流程.txt</span>
          <span class="citation-content">发票开具错误时，需要按照规定的流程进行更正或红冲处理。具体步骤包括...</span>
          📄 发票开具错误的更正和红冲流程
        </span>
      </p>
      
      <h2>功能测试</h2>
      <button class="test-button" onclick="testCitationParsing()">测试引用解析</button>
      <button class="test-button" onclick="testAPICall()">测试API调用</button>
      
      <div id="test-results" style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 5px;"></div>
    </div>

    <script>
      // 测试数据
      const testCitation1 = {
        rank: 1,
        filename: "常见问题类-04_电子发票重复报销防范措施.txt",
        content: "电子发票重复报销是企业财务管理中的常见问题。为了防范这种情况，企业应建立完善的发票管理制度，包括发票验证、重复检查等环节。",
        similarity_score: 0.85
      };
      
      const testCitation2 = {
        rank: 2,
        filename: "常见问题类-03_发票开具错误的更正和红冲流程.txt",
        content: "发票开具错误时，需要按照规定的流程进行更正或红冲处理。具体步骤包括：1. 确认错误类型 2. 选择处理方式 3. 执行相应操作",
        similarity_score: 0.78
      };

      function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
      }

      function showCitationDetails(citationId, citation) {
        if (!citation) {
          alert(`引用ID: ${citationId}\n\n这是从知识库中引用的文档内容。`);
          return;
        }
        
        const similarityPercent = Math.round(citation.similarity_score * 100);
        const displayName = citation.filename
          .replace(/^常见问题类-\d+_/, "")
          .replace(/\.txt$/, "");
          
        const message =
          `📄 文档引用详情\n\n` +
          `排名序号: #${citation.rank}\n` +
          `文档名称: ${displayName}\n` +
          `完整文件名: ${citation.filename}\n` +
          `相似度分数: ${similarityPercent}%\n` +
          `引用ID: ${citationId}\n\n` +
          `文档内容预览:\n${citation.content}\n\n` +
          `💡 提示：鼠标悬浮在引用块上可以查看内容预览`;
        
        alert(message);
      }

      function testCitationParsing() {
        const testText = "这是一个测试 [citation:abc123] 和另一个 [citation:def456]";
        const testData = {
          "abc123": testCitation1,
          "def456": testCitation2
        };
        
        const result = processCitations(testText, testData);
        document.getElementById('test-results').innerHTML = 
          '<h3>引用解析测试结果:</h3>' + 
          '<div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">' + result + '</div>';
      }

      function processCitations(text, citationData) {
        const citationRegex = /\[citation:([a-f0-9-]+)\]/gi;
        
        return text.replace(citationRegex, function (match, citationId) {
          const citation = citationData[citationId];
          
          if (!citation) {
            const shortId = citationId.substring(0, 8);
            return `<span class="citation" onclick="showCitation('${citationId}', null)">📄 文档-${shortId}</span>`;
          }
          
          const displayName = citation.filename
            .replace(/^常见问题类-\d+_/, "")
            .replace(/\.txt$/, "");
          
          const similarityPercent = Math.round(citation.similarity_score * 100);
          
          return (
            '<span class="citation" onclick="showCitationDetails(\'' +
            citationId +
            "', " +
            JSON.stringify(citation).replace(/"/g, '&quot;') +
            ')">' +
            '<span class="citation-rank">' +
            citation.rank +
            "</span>" +
            '<span class="citation-tooltip">' +
            "排名: #" +
            citation.rank +
            " | 相似度: " +
            similarityPercent +
            "%<br>" +
            escapeHtml(citation.filename) +
            "</span>" +
            '<span class="citation-content">' +
            escapeHtml(citation.content) +
            "</span>" +
            "📄 " +
            escapeHtml(displayName) +
            "</span>"
          );
        });
      }

      async function testAPICall() {
        try {
          const response = await fetch("/api/chat", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              id: "test_" + Date.now(),
              messages: [
                {
                  role: "user",
                  content: "电子发票重复报销如何防范？",
                },
              ],
              data: {},
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const responseText = await response.text();
          document.getElementById('test-results').innerHTML = 
            '<h3>API调用测试结果:</h3>' + 
            '<pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto;">' + 
            escapeHtml(responseText) + 
            '</pre>';
        } catch (error) {
          document.getElementById('test-results').innerHTML = 
            '<h3>API调用失败:</h3>' + 
            '<div style="color: red;">' + error.message + '</div>';
        }
      }
    </script>
  </body>
</html>
