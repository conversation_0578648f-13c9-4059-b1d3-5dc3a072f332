"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8729],{18729:(t,e,a)=>{a.r(e),a.d(e,{HoverCard:()=>n,HoverCardContent:()=>i,HoverCardTrigger:()=>s});var o=a(53891),r=a(49565);a(73987);var d=a(61971);function n(t){let{...e}=t;return(0,o.jsx)(r.bL,{"data-slot":"hover-card",...e})}function s(t){let{...e}=t;return(0,o.jsx)(r.l9,{"data-slot":"hover-card-trigger",...e})}function i(t){let{className:e,align:a="center",sideOffset:n=4,...s}=t;return(0,o.jsx)(r.<PERSON><PERSON>,{"data-slot":"hover-card-portal",children:(0,o.jsx)(r.<PERSON>,{"data-slot":"hover-card-content",align:a,sideOffset:n,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--radix-hover-card-content-transform-origin) outline-hidden z-50 w-64 rounded-md border p-4 shadow-md",e),...s})})}}}]);