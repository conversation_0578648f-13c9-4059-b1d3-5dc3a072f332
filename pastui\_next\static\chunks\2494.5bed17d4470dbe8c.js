"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2494],{62494:(e,t,a)=>{a.r(t),a.d(t,{Menubar:()=>W,MenubarCheckboxItem:()=>eo,MenubarContent:()=>er,MenubarGroup:()=>$,MenubarItem:()=>en,MenubarLabel:()=>ei,MenubarMenu:()=>X,MenubarPortal:()=>ee,MenubarRadioGroup:()=>et,MenubarRadioItem:()=>es,MenubarSeparator:()=>ed,MenubarShortcut:()=>eu,MenubarSub:()=>el,MenubarSubContent:()=>ef,MenubarSubTrigger:()=>ec,MenubarTrigger:()=>ea});var r=a(53891),n=a(73987),o=a(333),s=a(41105),i=a(77292),d=a(77310),u=a(80428),l=a(10145),c=a(36704),f=a(76653),p=a(4513),m=a(25261),b="Menubar",[g,x,v]=(0,o.N)(b),[h,j]=(0,u.A)(b,[v,f.RG]),w=(0,c.UE)(),y=(0,f.RG)(),[M,N]=h(b),R=n.forwardRef((e,t)=>{let{__scopeMenubar:a,value:o,onValueChange:i,defaultValue:d,loop:u=!0,dir:l,...c}=e,x=(0,s.jH)(l),v=y(a),[h,j]=(0,m.i)({prop:o,onChange:i,defaultProp:null!=d?d:"",caller:b}),[w,N]=n.useState(null);return(0,r.jsx)(M,{scope:a,value:h,onMenuOpen:n.useCallback(e=>{j(e),N(e)},[j]),onMenuClose:n.useCallback(()=>j(""),[j]),onMenuToggle:n.useCallback(e=>{j(t=>t?"":e),N(e)},[j]),dir:x,loop:u,children:(0,r.jsx)(g.Provider,{scope:a,children:(0,r.jsx)(g.Slot,{scope:a,children:(0,r.jsx)(f.bL,{asChild:!0,...v,orientation:"horizontal",loop:u,dir:x,currentTabStopId:w,onCurrentTabStopIdChange:N,children:(0,r.jsx)(p.sG.div,{role:"menubar",...c,ref:t})})})})})});R.displayName=b;var C="MenubarMenu",[k,z]=h(C),I=e=>{let{__scopeMenubar:t,value:a,...o}=e,s=(0,l.B)(),i=a||s||"LEGACY_REACT_AUTO_VALUE",d=N(C,t),u=w(t),f=n.useRef(null),p=n.useRef(!1),m=d.value===i;return n.useEffect(()=>{m||(p.current=!1)},[m]),(0,r.jsx)(k,{scope:t,value:i,triggerId:(0,l.B)(),triggerRef:f,contentId:(0,l.B)(),wasKeyboardTriggerOpenRef:p,children:(0,r.jsx)(c.bL,{...u,open:m,onOpenChange:e=>{e||d.onMenuClose()},modal:!1,dir:d.dir,...o})})};I.displayName=C;var A="MenubarTrigger",_=n.forwardRef((e,t)=>{let{__scopeMenubar:a,disabled:o=!1,...s}=e,u=y(a),l=w(a),m=N(A,a),b=z(A,a),x=n.useRef(null),v=(0,d.s)(t,x,b.triggerRef),[h,j]=n.useState(!1),M=m.value===b.value;return(0,r.jsx)(g.ItemSlot,{scope:a,value:b.value,disabled:o,children:(0,r.jsx)(f.q7,{asChild:!0,...u,focusable:!o,tabStopId:b.value,children:(0,r.jsx)(c.Mz,{asChild:!0,...l,children:(0,r.jsx)(p.sG.button,{type:"button",role:"menuitem",id:b.triggerId,"aria-haspopup":"menu","aria-expanded":M,"aria-controls":M?b.contentId:void 0,"data-highlighted":h?"":void 0,"data-state":M?"open":"closed","data-disabled":o?"":void 0,disabled:o,...s,ref:v,onPointerDown:(0,i.m)(e.onPointerDown,e=>{!o&&0===e.button&&!1===e.ctrlKey&&(m.onMenuOpen(b.value),M||e.preventDefault())}),onPointerEnter:(0,i.m)(e.onPointerEnter,()=>{if(m.value&&!M){var e;m.onMenuOpen(b.value),null==(e=x.current)||e.focus()}}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!o&&(["Enter"," "].includes(e.key)&&m.onMenuToggle(b.value),"ArrowDown"===e.key&&m.onMenuOpen(b.value),["Enter"," ","ArrowDown"].includes(e.key)&&(b.wasKeyboardTriggerOpenRef.current=!0,e.preventDefault()))}),onFocus:(0,i.m)(e.onFocus,()=>j(!0)),onBlur:(0,i.m)(e.onBlur,()=>j(!1))})})})})});_.displayName=A;var O=e=>{let{__scopeMenubar:t,...a}=e,n=w(t);return(0,r.jsx)(c.ZL,{...n,...a})};O.displayName="MenubarPortal";var S="MenubarContent",D=n.forwardRef((e,t)=>{let{__scopeMenubar:a,align:o="start",...s}=e,d=w(a),u=N(S,a),l=z(S,a),f=x(a),p=n.useRef(!1);return(0,r.jsx)(c.UC,{id:l.contentId,"aria-labelledby":l.triggerId,"data-radix-menubar-content":"",...d,...s,ref:t,align:o,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{if(!u.value&&!p.current){var t;null==(t=l.triggerRef.current)||t.focus()}p.current=!1,e.preventDefault()}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{let t=e.target;f().some(e=>{var a;return null==(a=e.ref.current)?void 0:a.contains(t)})&&e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,()=>{p.current=!0}),onEntryFocus:e=>{l.wasKeyboardTriggerOpenRef.current||e.preventDefault()},onKeyDown:(0,i.m)(e.onKeyDown,e=>{if(["ArrowRight","ArrowLeft"].includes(e.key)){let t=e.target,a=t.hasAttribute("data-radix-menubar-subtrigger"),r=t.closest("[data-radix-menubar-content]")!==e.currentTarget,n=("rtl"===u.dir?"ArrowRight":"ArrowLeft")===e.key;if(!n&&a||r&&n)return;let o=f().filter(e=>!e.disabled).map(e=>e.value);n&&o.reverse();let s=o.indexOf(l.value),[i]=o=u.loop?function(e,t){return e.map((a,r)=>e[(t+r)%e.length])}(o,s+1):o.slice(s+1);i&&u.onMenuOpen(i)}},{checkForDefaultPrevented:!1}),style:{...e.style,"--radix-menubar-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-menubar-content-available-width":"var(--radix-popper-available-width)","--radix-menubar-content-available-height":"var(--radix-popper-available-height)","--radix-menubar-trigger-width":"var(--radix-popper-anchor-width)","--radix-menubar-trigger-height":"var(--radix-popper-anchor-height)"}})});D.displayName=S;var E=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.YJ,{...o,...n,ref:t})});E.displayName="MenubarGroup";var P=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.JU,{...o,...n,ref:t})});P.displayName="MenubarLabel";var T=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.q7,{...o,...n,ref:t})});T.displayName="MenubarItem";var G=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.H_,{...o,...n,ref:t})});G.displayName="MenubarCheckboxItem";var F=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.z6,{...o,...n,ref:t})});F.displayName="MenubarRadioGroup";var L=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.hN,{...o,...n,ref:t})});L.displayName="MenubarRadioItem";var K=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.VF,{...o,...n,ref:t})});K.displayName="MenubarItemIndicator";var B=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.wv,{...o,...n,ref:t})});B.displayName="MenubarSeparator",n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.i3,{...o,...n,ref:t})}).displayName="MenubarArrow";var U="MenubarSub",q=e=>{let{__scopeMenubar:t,children:a,open:n,onOpenChange:o,defaultOpen:s}=e,i=w(t),[d,u]=(0,m.i)({prop:n,defaultProp:null!=s&&s,onChange:o,caller:U});return(0,r.jsx)(c.Pb,{...i,open:d,onOpenChange:u,children:a})};q.displayName=U;var H=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.ZP,{"data-radix-menubar-subtrigger":"",...o,...n,ref:t})});H.displayName="MenubarSubTrigger";var J=n.forwardRef((e,t)=>{let{__scopeMenubar:a,...n}=e,o=w(a);return(0,r.jsx)(c.G5,{...o,"data-radix-menubar-content":"",...n,ref:t,style:{...e.style,"--radix-menubar-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-menubar-content-available-width":"var(--radix-popper-available-width)","--radix-menubar-content-available-height":"var(--radix-popper-available-height)","--radix-menubar-trigger-width":"var(--radix-popper-anchor-width)","--radix-menubar-trigger-height":"var(--radix-popper-anchor-height)"}})});J.displayName="MenubarSubContent";var V=a(25907),Y=a(41341),Z=a(89111),Q=a(61971);function W(e){let{className:t,...a}=e;return(0,r.jsx)(R,{"data-slot":"menubar",className:(0,Q.cn)("bg-background shadow-xs flex h-9 items-center gap-1 rounded-md border p-1",t),...a})}function X(e){let{...t}=e;return(0,r.jsx)(I,{"data-slot":"menubar-menu",...t})}function $(e){let{...t}=e;return(0,r.jsx)(E,{"data-slot":"menubar-group",...t})}function ee(e){let{...t}=e;return(0,r.jsx)(O,{"data-slot":"menubar-portal",...t})}function et(e){let{...t}=e;return(0,r.jsx)(F,{"data-slot":"menubar-radio-group",...t})}function ea(e){let{className:t,...a}=e;return(0,r.jsx)(_,{"data-slot":"menubar-trigger",className:(0,Q.cn)("focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground outline-hidden flex select-none items-center rounded-sm px-2 py-1 text-sm font-medium",t),...a})}function er(e){let{className:t,align:a="start",alignOffset:n=-4,sideOffset:o=8,...s}=e;return(0,r.jsx)(ee,{children:(0,r.jsx)(D,{"data-slot":"menubar-content",align:a,alignOffset:n,sideOffset:o,className:(0,Q.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--radix-menubar-content-transform-origin) z-50 min-w-[12rem] overflow-hidden rounded-md border p-1 shadow-md",t),...s})})}function en(e){let{className:t,inset:a,variant:n="default",...o}=e;return(0,r.jsx)(T,{"data-slot":"menubar-item","data-inset":a,"data-variant":n,className:(0,Q.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground outline-hidden relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm data-[disabled]:pointer-events-none data-[inset]:pl-8 data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),...o})}function eo(e){let{className:t,children:a,checked:n,...o}=e;return(0,r.jsxs)(G,{"data-slot":"menubar-checkbox-item",className:(0,Q.cn)("focus:bg-accent focus:text-accent-foreground rounded-xs outline-hidden relative flex cursor-default select-none items-center gap-2 py-1.5 pl-8 pr-2 text-sm data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),checked:n,...o,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(K,{children:(0,r.jsx)(V.A,{className:"size-4"})})}),a]})}function es(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(L,{"data-slot":"menubar-radio-item",className:(0,Q.cn)("focus:bg-accent focus:text-accent-foreground rounded-xs outline-hidden relative flex cursor-default select-none items-center gap-2 py-1.5 pl-8 pr-2 text-sm data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),...n,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(K,{children:(0,r.jsx)(Y.A,{className:"size-2 fill-current"})})}),a]})}function ei(e){let{className:t,inset:a,...n}=e;return(0,r.jsx)(P,{"data-slot":"menubar-label","data-inset":a,className:(0,Q.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n})}function ed(e){let{className:t,...a}=e;return(0,r.jsx)(B,{"data-slot":"menubar-separator",className:(0,Q.cn)("bg-border -mx-1 my-1 h-px",t),...a})}function eu(e){let{className:t,...a}=e;return(0,r.jsx)("span",{"data-slot":"menubar-shortcut",className:(0,Q.cn)("text-muted-foreground ml-auto text-xs tracking-widest",t),...a})}function el(e){let{...t}=e;return(0,r.jsx)(q,{"data-slot":"menubar-sub",...t})}function ec(e){let{className:t,inset:a,children:n,...o}=e;return(0,r.jsxs)(H,{"data-slot":"menubar-sub-trigger","data-inset":a,className:(0,Q.cn)("focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[inset]:pl-8",t),...o,children:[n,(0,r.jsx)(Z.A,{className:"ml-auto h-4 w-4"})]})}function ef(e){let{className:t,...a}=e;return(0,r.jsx)(J,{"data-slot":"menubar-sub-content",className:(0,Q.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--radix-menubar-content-transform-origin) z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-lg",t),...a})}}}]);