"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1560],{51560:(t,e,a)=>{a.r(e),a.d(e,{Table:()=>r,TableBody:()=>n,TableCaption:()=>i,TableCell:()=>u,TableFooter:()=>c,TableHead:()=>b,TableHeader:()=>s,TableRow:()=>d});var l=a(53891);a(73987);var o=a(61971);function r(t){let{className:e,...a}=t;return(0,l.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,l.jsx)("table",{"data-slot":"table",className:(0,o.cn)("w-full caption-bottom text-sm",e),...a})})}function s(t){let{className:e,...a}=t;return(0,l.jsx)("thead",{"data-slot":"table-header",className:(0,o.cn)("[&_tr]:border-b",e),...a})}function n(t){let{className:e,...a}=t;return(0,l.jsx)("tbody",{"data-slot":"table-body",className:(0,o.cn)("[&_tr:last-child]:border-0",e),...a})}function c(t){let{className:e,...a}=t;return(0,l.jsx)("tfoot",{"data-slot":"table-footer",className:(0,o.cn)("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",e),...a})}function d(t){let{className:e,...a}=t;return(0,l.jsx)("tr",{"data-slot":"table-row",className:(0,o.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...a})}function b(t){let{className:e,...a}=t;return(0,l.jsx)("th",{"data-slot":"table-head",className:(0,o.cn)("text-foreground h-10 whitespace-nowrap px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}function u(t){let{className:e,...a}=t;return(0,l.jsx)("td",{"data-slot":"table-cell",className:(0,o.cn)("whitespace-nowrap p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}function i(t){let{className:e,...a}=t;return(0,l.jsx)("caption",{"data-slot":"table-caption",className:(0,o.cn)("text-muted-foreground mt-4 text-sm",e),...a})}}}]);