<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>清洁版聊天界面</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
        padding: 20px;
        background: #f5f5f5;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      .header {
        background: #007bff;
        color: white;
        padding: 20px;
        text-align: center;
      }
      .chat-area {
        height: 500px;
        overflow-y: auto;
        padding: 20px;
        border-bottom: 1px solid #eee;
      }
      .message {
        margin-bottom: 15px;
        padding: 10px 15px;
        border-radius: 10px;
        max-width: 80%;
      }
      .user {
        background: #007bff;
        color: white;
        margin-left: auto;
      }
      .assistant {
        background: #f1f1f1;
        color: #333;
      }
      .input-area {
        padding: 20px;
        display: flex;
        gap: 10px;
      }
      #messageInput {
        flex: 1;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
      }
      #sendButton {
        padding: 10px 20px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      }
      #sendButton:hover {
        background: #0056b3;
      }
      .loading {
        opacity: 0.7;
        font-style: italic;
      }
      .citation {
        display: inline-block;
        background: #e3f2fd;
        color: #1976d2;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        margin: 0 3px;
        cursor: pointer;
        border: 1px solid #bbdefb;
        position: relative;
        font-weight: 500;
        transition: all 0.2s ease;
      }
      .citation:hover {
        background: #bbdefb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .citation-tooltip {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 11px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.2s ease;
        z-index: 1000;
        margin-bottom: 5px;
        max-width: 200px;
        text-align: center;
      }
      .citation-tooltip::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-top-color: #333;
      }
      .citation:hover .citation-tooltip {
        opacity: 1;
        visibility: visible;
      }
      .citation-content {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 12px;
        font-size: 12px;
        white-space: pre-wrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1001;
        margin-bottom: 10px;
        max-width: 300px;
        max-height: 200px;
        overflow-y: auto;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        color: #333;
        line-height: 1.4;
      }
      .citation-content::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 8px solid transparent;
        border-top-color: white;
      }
      .citation:hover .citation-content {
        opacity: 1;
        visibility: visible;
      }
      .citation-rank {
        background: #1976d2;
        color: white;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        margin-right: 4px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>财务知识库助手 - 清洁版</h1>
        <p>直接使用LlamaIndex响应，无额外处理</p>
      </div>

      <div class="chat-area" id="chatArea">
        <div class="message assistant">
          你好！我是财务知识库助手。请问有什么财务问题需要帮助吗？
        </div>
      </div>

      <div class="input-area">
        <input
          type="text"
          id="messageInput"
          placeholder="请输入您的问题..."
          onkeypress="if(event.key==='Enter') sendMessage()"
        />
        <button id="sendButton" onclick="sendMessage()">发送</button>
      </div>
    </div>

    <script>
      const chatArea = document.getElementById("chatArea");
      const messageInput = document.getElementById("messageInput");
      const sendButton = document.getElementById("sendButton");

      let chatId = "chat_" + Date.now();

      function addMessage(content, role) {
        const messageDiv = document.createElement("div");
        messageDiv.className = `message ${role}`;
        messageDiv.innerHTML = content;
        chatArea.appendChild(messageDiv);
        chatArea.scrollTop = chatArea.scrollHeight;
        return messageDiv;
      }

      async function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;

        // 显示用户消息
        addMessage(message, "user");
        messageInput.value = "";

        // 显示加载状态
        const loadingDiv = addMessage("正在思考...", "assistant loading");

        try {
          const response = await fetch("/api/chat", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              id: chatId,
              messages: [
                {
                  role: "user",
                  content: message,
                },
              ],
              data: {},
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const responseText = await response.text();
          console.log("原始响应:", responseText);

          // 移除加载消息
          chatArea.removeChild(loadingDiv);

          // 解析响应
          const aiResponse = parseResponse(responseText);
          addMessage(aiResponse, "assistant");
        } catch (error) {
          console.error("发送消息失败:", error);
          chatArea.removeChild(loadingDiv);
          addMessage("抱歉，发生了错误：" + error.message, "assistant");
        }
      }

      function parseResponse(responseText) {
        try {
          // 分割每一行数据
          const lines = responseText.split("\n").filter((line) => line.trim());
          let toolOutputs = [];
          let citationData = {};

          for (const line of lines) {
            // 移除行号前缀
            const cleanLine = line.replace(/^\d+:/, "");

            if (cleanLine.trim()) {
              try {
                const data = JSON.parse(cleanLine);

                // 处理数组格式的数据
                if (Array.isArray(data)) {
                  for (const item of data) {
                    // 收集所有工具输出
                    if (item.tool_output && item.tool_output.content) {
                      toolOutputs.push(item.tool_output.content);
                    }
                  }
                }
              } catch (e) {
                // 忽略JSON解析错误
              }
            }
          }

          // 只使用最后一个工具输出（最终结果），避免重复
          let content = "";
          if (toolOutputs.length > 0) {
            content = toolOutputs[toolOutputs.length - 1];
          }

          // 提取引用数据
          const citationMatch = content.match(/<!-- CITATION_DATA: (.*?) -->/s);
          if (citationMatch) {
            try {
              citationData = JSON.parse(citationMatch[1]);
              // 移除引用数据注释
              content = content
                .replace(/<!-- CITATION_DATA: .*? -->/s, "")
                .trim();
            } catch (e) {
              console.error("解析引用数据失败:", e);
            }
          }

          // 处理引用，显示排名序号、文档名和内容
          content = processCitations(content, citationData);

          return content.trim() || "抱歉，没有收到有效回复。";
        } catch (error) {
          console.error("解析响应失败:", error);
          return "抱歉，解析回复时出现错误。";
        }
      }

      function processCitations(text, citationData) {
        const citationRegex = /\[citation:([a-f0-9-]+)\]/gi;

        return text.replace(citationRegex, function (match, citationId) {
          const citation = citationData[citationId];

          if (!citation) {
            // 如果没有找到引用数据，使用简单格式
            const shortId = citationId.substring(0, 8);
            return `<span class="citation" onclick="showCitation('${citationId}', null)">📄 文档-${shortId}</span>`;
          }

          // 简化显示的文件名
          const displayName = citation.filename
            .replace(/^常见问题类-\d+_/, "")
            .replace(/\.txt$/, "");

          // 格式化相似度分数
          const similarityPercent = Math.round(citation.similarity_score * 100);

          return (
            '<span class="citation" onclick="showCitationDetails(\'' +
            citationId +
            "', '" +
            escapeHtml(JSON.stringify(citation)) +
            "')\">" +
            '<span class="citation-rank">' +
            citation.rank +
            "</span>" +
            '<span class="citation-tooltip">' +
            "排名: #" +
            citation.rank +
            " | 相似度: " +
            similarityPercent +
            "%<br>" +
            escapeHtml(citation.filename) +
            "</span>" +
            '<span class="citation-content">' +
            escapeHtml(citation.content) +
            "</span>" +
            "📄 " +
            escapeHtml(displayName) +
            "</span>"
          );
        });
      }

      function escapeHtml(text) {
        const div = document.createElement("div");
        div.textContent = text;
        return div.innerHTML;
      }

      function showCitationDetails(citationId, citationStr) {
        let citation = null;

        if (citationStr && typeof citationStr === "string") {
          try {
            citation = JSON.parse(citationStr);
          } catch (e) {
            console.error("解析引用数据失败:", e);
          }
        } else if (citationStr && typeof citationStr === "object") {
          citation = citationStr;
        }

        if (!citation) {
          alert(`引用ID: ${citationId}\n\n这是从知识库中引用的文档内容。`);
          return;
        }

        const similarityPercent = Math.round(citation.similarity_score * 100);
        const displayName = citation.filename
          .replace(/^常见问题类-\d+_/, "")
          .replace(/\.txt$/, "");

        const message =
          `📄 文档引用详情\n\n` +
          `排名序号: #${citation.rank}\n` +
          `文档名称: ${displayName}\n` +
          `完整文件名: ${citation.filename}\n` +
          `相似度分数: ${similarityPercent}%\n` +
          `引用ID: ${citationId}\n\n` +
          `文档内容预览:\n${citation.content}\n\n` +
          `💡 提示：鼠标悬浮在引用块上可以查看内容预览`;

        alert(message);
      }

      function showCitation(citationId, citation) {
        showCitationDetails(citationId, citation);
      }

      // 页面加载完成后聚焦输入框
      window.onload = function () {
        messageInput.focus();
      };
    </script>
  </body>
</html>
