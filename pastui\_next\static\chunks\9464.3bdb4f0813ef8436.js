"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9464],{1019:(e,t,n)=>{n.r(t),n.d(t,{ResizableHandle:()=>c,ResizablePanel:()=>o,ResizablePanelGroup:()=>i});var a=n(53891),r=n(80138);n(73987);var s=n(21854),l=n(61971);function i(e){let{className:t,...n}=e;return(0,a.jsx)(s.YZ,{"data-slot":"resizable-panel-group",className:(0,l.cn)("flex h-full w-full data-[panel-group-direction=vertical]:flex-col",t),...n})}function o(e){let{...t}=e;return(0,a.jsx)(s.Zk,{"data-slot":"resizable-panel",...t})}function c(e){let{withHandle:t,className:n,...i}=e;return(0,a.jsx)(s.TW,{"data-slot":"resizable-handle",className:(0,l.cn)("bg-border focus-visible:ring-ring focus-visible:outline-hidden relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90",n),...i,children:t&&(0,a.jsx)("div",{className:"bg-border rounded-xs z-10 flex h-4 w-3 items-center justify-center border",children:(0,a.jsx)(r.A,{className:"size-2.5"})})})}},17550:(e,t,n)=>{n.r(t),n.d(t,{Accordion:()=>o,AccordionContent:()=>u,AccordionItem:()=>c,AccordionTrigger:()=>d});var a=n(53891),r=n(86308),s=n(68135),l=n(73987),i=n(61971);let o=r.bL,c=l.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.q7,{ref:t,className:(0,i.cn)("border-b",n),...s})});c.displayName="AccordionItem";let d=l.forwardRef((e,t)=>{let{className:n,children:l,...o}=e;return(0,a.jsx)(r.Y9,{className:"flex",children:(0,a.jsxs)(r.l9,{ref:t,className:(0,i.cn)("flex flex-1 items-center justify-between py-4 text-left text-sm font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",n),...o,children:[l,(0,a.jsx)(s.A,{className:"h-4 w-4 shrink-0 text-neutral-500 transition-transform duration-200 dark:text-neutral-400"})]})})});d.displayName=r.l9.displayName;let u=l.forwardRef((e,t)=>{let{className:n,children:s,...l}=e;return(0,a.jsx)(r.UC,{ref:t,className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...l,children:(0,a.jsx)("div",{className:(0,i.cn)("pb-4 pt-0",n),children:s})})});u.displayName=r.UC.displayName},37620:(e,t,n)=>{n.r(t),n.d(t,{Select:()=>d,SelectContent:()=>x,SelectGroup:()=>u,SelectItem:()=>j,SelectLabel:()=>g,SelectScrollDownButton:()=>f,SelectScrollUpButton:()=>p,SelectSeparator:()=>v,SelectTrigger:()=>h,SelectValue:()=>m});var a=n(53891),r=n(75978),s=n(68135),l=n(6110),i=n(25907),o=n(73987),c=n(61971);let d=r.bL,u=r.YJ,m=r.WT,h=o.forwardRef((e,t)=>{let{className:n,children:l,...i}=e;return(0,a.jsxs)(r.l9,{ref:t,className:(0,c.cn)("border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring focus:outline-hidden flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",n),...i,children:[l,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(s.A,{className:"h-4 w-4 opacity-50"})})]})});h.displayName=r.l9.displayName;let p=o.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.PP,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",n),...s,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})});p.displayName=r.PP.displayName;let f=o.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)(r.wn,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",n),...l,children:(0,a.jsx)(s.A,{className:"h-4 w-4"})})});f.displayName=r.wn.displayName;let x=o.forwardRef((e,t)=>{let{className:n,children:s,position:l="popper",...i}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{ref:t,className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:l,...i,children:[(0,a.jsx)(p,{}),(0,a.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(f,{})]})})});x.displayName=r.UC.displayName;let g=o.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.JU,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",n),...s})});g.displayName=r.JU.displayName;let j=o.forwardRef((e,t)=>{let{className:n,children:s,...l}=e;return(0,a.jsxs)(r.q7,{ref:t,className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground outline-hidden data-disabled:pointer-events-none data-disabled:opacity-50 relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm",n),...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})}),(0,a.jsx)(r.p4,{children:s})]})});j.displayName=r.q7.displayName;let v=o.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.wv,{ref:t,className:(0,c.cn)("bg-muted -mx-1 my-1 h-px",n),...s})});v.displayName=r.wv.displayName},59464:(e,t,n)=>{n.r(t),n.d(t,{default:()=>ep});var a=n(53891),r=n(40702),s=n(59884),l=n(73987),i=n(61971),o=n(1019),c=n(2411),d=n(12033),u=n(18793),m=n(61949),h=n(17550),p=n(96740);class f extends l.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){var n,a;console.warn(this.props.eventType,e,t);let r="Fail to render ".concat(this.props.eventType," component: ").concat(e.message);null==(n=(a=this.props).onError)||n.call(a,r)}render(){return this.state.hasError?this.props.fallback||null:this.props.children}constructor(e){super(e),this.state={hasError:!1,error:null}}}var x=n(82612),g=n(39412),j=n(5792);let v="@/components/ui",b={react:()=>Promise.resolve().then(n.t.bind(n,73987,19)),"react-dom":()=>Promise.resolve().then(n.t.bind(n,62866,19)),["".concat(v,"/accordion")]:()=>Promise.resolve().then(n.bind(n,17550)),["".concat(v,"/alert")]:()=>n.e(382).then(n.bind(n,60382)),["".concat(v,"/alert-dialog")]:()=>n.e(6425).then(n.bind(n,66425)),["".concat(v,"/aspect-ratio")]:()=>n.e(3877).then(n.bind(n,23877)),["".concat(v,"/avatar")]:()=>n.e(1909).then(n.bind(n,21909)),["".concat(v,"/badge")]:()=>n.e(3709).then(n.bind(n,93709)),["".concat(v,"/breadcrumb")]:()=>n.e(687).then(n.bind(n,50687)),["".concat(v,"/button")]:()=>Promise.resolve().then(n.bind(n,96740)),["".concat(v,"/calendar")]:()=>Promise.all([n.e(1439),n.e(6634)]).then(n.bind(n,96634)),["".concat(v,"/card")]:()=>n.e(9382).then(n.bind(n,39382)),["".concat(v,"/carousel")]:()=>Promise.all([n.e(7423),n.e(2250)]).then(n.bind(n,62250)),["".concat(v,"/chart")]:()=>Promise.all([n.e(1314),n.e(1128)]).then(n.bind(n,91128)),["".concat(v,"/checkbox")]:()=>n.e(487).then(n.bind(n,80487)),["".concat(v,"/collapsible")]:()=>n.e(1280).then(n.bind(n,51280)),["".concat(v,"/command")]:()=>n.e(1490).then(n.bind(n,61490)),["".concat(v,"/context-menu")]:()=>Promise.all([n.e(9658),n.e(4637)]).then(n.bind(n,84637)),["".concat(v,"/dialog")]:()=>n.e(1964).then(n.bind(n,1964)),["".concat(v,"/drawer")]:()=>Promise.all([n.e(1594),n.e(9987)]).then(n.bind(n,9987)),["".concat(v,"/dropdown-menu")]:()=>Promise.all([n.e(9658),n.e(9744)]).then(n.bind(n,69744)),["".concat(v,"/form")]:()=>n.e(9370).then(n.bind(n,39370)),["".concat(v,"/hover-card")]:()=>n.e(8729).then(n.bind(n,18729)),["".concat(v,"/input")]:()=>n.e(4744).then(n.bind(n,34744)),["".concat(v,"/input-otp")]:()=>n.e(2081).then(n.bind(n,82081)),["".concat(v,"/label")]:()=>n.e(4466).then(n.bind(n,74466)),["".concat(v,"/menubar")]:()=>Promise.all([n.e(9658),n.e(2494)]).then(n.bind(n,62494)),["".concat(v,"/navigation-menu")]:()=>n.e(7880).then(n.bind(n,37880)),["".concat(v,"/pagination")]:()=>n.e(7580).then(n.bind(n,37580)),["".concat(v,"/popover")]:()=>n.e(7819).then(n.bind(n,67819)),["".concat(v,"/progress")]:()=>n.e(825).then(n.bind(n,50825)),["".concat(v,"/radio-group")]:()=>n.e(333).then(n.bind(n,90333)),["".concat(v,"/resizable")]:()=>Promise.resolve().then(n.bind(n,1019)),["".concat(v,"/scroll-area")]:()=>n.e(9371).then(n.bind(n,59371)),["".concat(v,"/select")]:()=>Promise.resolve().then(n.bind(n,37620)),["".concat(v,"/separator")]:()=>n.e(6143).then(n.bind(n,66143)),["".concat(v,"/sheet")]:()=>n.e(7161).then(n.bind(n,17161)),["".concat(v,"/sidebar")]:()=>n.e(2049).then(n.bind(n,2049)),["".concat(v,"/skeleton")]:()=>n.e(1061).then(n.bind(n,51061)),["".concat(v,"/slider")]:()=>n.e(5846).then(n.bind(n,65846)),["".concat(v,"/sonner")]:()=>Promise.all([n.e(2226),n.e(2631)]).then(n.bind(n,52631)),["".concat(v,"/switch")]:()=>n.e(1480).then(n.bind(n,11480)),["".concat(v,"/table")]:()=>n.e(1560).then(n.bind(n,51560)),["".concat(v,"/tabs")]:()=>n.e(3592).then(n.bind(n,23592)),["".concat(v,"/textarea")]:()=>n.e(4254).then(n.bind(n,34254)),["".concat(v,"/toggle")]:()=>n.e(8484).then(n.bind(n,18484)),["".concat(v,"/toggle-group")]:()=>n.e(2287).then(n.bind(n,42287)),["".concat(v,"/tooltip")]:()=>n.e(6733).then(n.bind(n,96733)),"@llamaindex/chat-ui":()=>Promise.resolve().then(n.bind(n,40702)),"@llamaindex/chat-ui/widgets":()=>Promise.resolve().then(n.bind(n,71061)),"lucide-react":()=>Promise.all([n.e(2365),n.e(9647)]).then(n.bind(n,9416)),"@/components/lib/utils":()=>Promise.resolve().then(n.bind(n,61971)),"@/lib/utils":()=>Promise.resolve().then(n.bind(n,61971)),zod:()=>n.e(8827).then(n.bind(n,38827))};async function w(e){let t=[],n=null,a=(0,g.parse)(e,{sourceType:"module",plugins:["jsx","typescript"]});(0,j.default)(a,{ImportDeclaration(e){e.node.specifiers.forEach(n=>{("ImportSpecifier"===n.type||"ImportDefaultSpecifier"===n.type)&&t.push({name:n.local.name,source:e.node.source.value})})},ExportDefaultDeclaration(e){let t=e.node.declaration;"FunctionDeclaration"===t.type&&t.id?n=t.id.name:"Identifier"===t.type&&e.scope.hasBinding(t.name)&&(n=t.name)}});let r=t.map(async e=>{let{name:t,source:n}=e;if(!(n in b))throw Error("Fail to import ".concat(t," from ").concat(n,'. Reason: Module not found. \nCurrently we only support importing UI components from Shadcn components, widgets and hooks from "llamaindex/chat-ui", icons from "lucide-react" and zod for data validation.'));try{let e=await b[n]();return{name:t,module:e[t]}}catch(e){throw Error("Failed to resolve import ".concat(t,". Please check the code and try again."))}}),s=await Promise.all(r),i={React:l};return s.forEach(e=>{let{name:t,module:n}=e;n&&(i[t]=n)}),{componentName:n,importMap:i}}var y=n(59947);async function N(){var e;let t=null!=(e=(0,i.getConfig)("COMPONENTS_API"))?e:"true"===y.env.NEXT_PUBLIC_USE_COMPONENTS_DIR?"/api/components":void 0;if(!t)return console.warn("/api/components endpoint is not defined in config"),{components:[],errors:[]};let n=await fetch(t),a=await n.json(),r=await Promise.all(a.map(async e=>{let{component:t,error:n}=await C(e.code,e.filename);return{type:e.type,comp:t,error:n}}));return{components:r.map(e=>({type:e.type,comp:e.comp})).filter(e=>null!==e.comp),errors:r.map(e=>e.error).filter(e=>void 0!==e)}}async function C(e,t){try{let[n,a]=await Promise.all([S(e,t),w(e)]);return{component:await E(n,a.importMap,a.componentName)}}catch(e){return console.warn("Failed to parse component from ".concat(t),e),{component:null,error:e instanceof Error?e.message:"Unknown error"}}}async function S(e,t){let n=x.transform(e,{presets:["react","typescript"],plugins:[()=>({visitor:{ImportDeclaration(e){e.remove()},ExportDefaultDeclaration(e){e.replaceWith(e.node.declaration)}}})],filename:t}).code;if(!n)throw Error("Transpiled code is empty for ".concat(t));return n}async function E(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Component",a=Object.keys(t),r=Object.values(t);return Function(...a,"".concat(e,"; return ").concat(n,";"))(...r)}let D=["js","ts","jsx","tsx","javascript","typescript"];function A(){let{displayedArtifact:e}=(0,c.u)();return(null==e?void 0:e.type)!=="code"?null:D.includes(e.data.language)?(0,a.jsx)(k,{artifact:e}):(0,a.jsx)("div",{className:"flex h-full items-center justify-center gap-2",children:(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Preview is not supported for this language"})})}function k(e){let{artifact:t}=e,{appendErrors:n}=(0,c.u)(),[r,s]=(0,l.useState)(!0),[i,o]=(0,l.useState)(null),{data:{code:d,file_name:m}}=t;return((0,l.useEffect)(()=>{(async()=>{s(!0);let{component:e,error:a}=await C(d,m);a?(o(null),n(t,[a])):o(()=>e),s(!1)})()},[t]),r)?(0,a.jsxs)("div",{className:"flex h-full items-center justify-center gap-2",children:[(0,a.jsx)(u.A,{className:"size-4 animate-spin"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Rendering Artifact..."})]}):i?(0,a.jsx)(f,{onError:e=>n(t,[e]),children:l.createElement(i)}):(0,a.jsx)(P,{artifact:t})}function P(e){let{artifact:t}=e,{getCodeErrors:n,fixCodeErrors:r}=(0,c.u)(),s=n(t);return 0===s.length?null:(0,a.jsxs)("div",{className:"flex flex-col gap-10 px-10 pt-10",children:[(0,a.jsx)("p",{className:"text-center text-sm text-gray-500",children:"Error when rendering code, please check the details and try fixing them."}),(0,a.jsx)(h.Accordion,{type:"single",defaultValue:"errors",collapsible:!0,className:"w-full rounded-xl border border-gray-100 bg-white shadow-md",children:(0,a.jsxs)(h.AccordionItem,{value:"errors",className:"border-none px-4",children:[(0,a.jsx)(h.AccordionTrigger,{className:"py-2 hover:no-underline",children:(0,a.jsxs)("div",{className:"flex flex-1 items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground font-bold",children:"Rendering errors"}),(0,a.jsx)("span",{className:"inline-flex h-5 w-5 items-center justify-center rounded-full bg-yellow-500 text-xs text-white",children:s.length})]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)("div",{className:(0,i.cn)((0,p.buttonVariants)({variant:"default",size:"sm"}),"mr-2 h-8 cursor-pointer bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600"),onClick:e=>{e.stopPropagation(),r(t)},children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Fix errors"})]})})]})}),(0,a.jsx)(h.AccordionContent,{className:"pb-4",children:(0,a.jsx)("div",{className:"space-y-2",children:s.map((e,t)=>(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:e},t))})})]})})]})}function _(){let{displayedArtifact:e,isCanvasOpen:t}=(0,c.u)();return e&&t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.ResizableHandle,{withHandle:!0}),(0,a.jsx)(o.ResizablePanel,{defaultSize:60,minSize:50,children:(0,a.jsxs)(d.C,{className:"w-full",children:[(0,a.jsx)(d.C.CodeArtifact,{tabs:{preview:(0,a.jsx)(A,{})}}),(0,a.jsx)(d.C.DocumentArtifact,{})]})})]}):null}function L(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("script",{async:!0,src:"https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"}),(0,a.jsx)("style",{type:"text/tailwindcss",children:'\n@import "tailwindcss";\n\n@layer base {\n  *,\n  ::after,\n  ::before,\n  ::backdrop,\n  ::file-selector-button {\n    border-color: var(--color-gray-200, currentColor);\n  }\n}\n'})]})}var z=n(73240),F=n(71061),R=n(37620),I=n(59947);function T(e){let{onSelect:t,defaultPipeline:n,shouldCheckValid:r=!1}=e,{setRequestData:s}=(0,c.a)(),[o,d]=(0,l.useState)(),m=(0,l.useCallback)(e=>{s?s({llamaCloudPipeline:e}):null==t||t(e)},[t,s]);(0,l.useEffect)(()=>{var e;let t=null!=(e=(0,i.getConfig)("LLAMA_CLOUD_API"))?e:"true"===I.env.NEXT_PUBLIC_SHOW_LLAMACLOUD_SELECTOR?"/api/chat/config/llamacloud":"";!o&&t&&fetch(t).then(e=>e.ok?e.json():e.json().then(e=>{window.alert("Error: ".concat(JSON.stringify(e)||"Unknown error occurred"))})).then(e=>{let t=null!=n?n:e.pipeline;d({...e,pipeline:t}),m(t)}).catch(e=>console.error("Error fetching config",e))},[o,n,m]);let h=e=>{d(t=>({...t,pipeline:e})),m(e)},p=async e=>{h(JSON.parse(e))};if(!o)return(0,a.jsx)("div",{className:"flex items-center justify-center p-3",children:(0,a.jsx)(u.A,{className:"h-4 w-4 animate-spin"})});if(r&&!M(o.projects,o.pipeline))return(0,a.jsx)("p",{className:"text-red-500",children:"Invalid LlamaCloud configuration. Check console logs."});let{projects:f,pipeline:x}=o;return(0,a.jsxs)(R.Select,{onValueChange:p,defaultValue:M(f,x,!1)?JSON.stringify(x):void 0,children:[(0,a.jsx)(R.SelectTrigger,{className:"w-[200px]",children:(0,a.jsx)(R.SelectValue,{placeholder:"Select a pipeline"})}),(0,a.jsx)(R.SelectContent,{children:f.map(e=>(0,a.jsxs)(R.SelectGroup,{children:[(0,a.jsxs)(R.SelectLabel,{className:"capitalize",children:["Project: ",e.name]}),e.pipelines.map(t=>(0,a.jsx)(R.SelectItem,{className:"last:border-b",value:JSON.stringify({pipeline:t.name,project:e.name}),children:(0,a.jsx)("span",{className:"pl-2",children:t.name})},t.id))]},e.id))})]})}function M(e,t){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!(null==e?void 0:e.length)||!t)return!1;let a=e.find(e=>e.name===t.project);return a?!!a.pipelines.some(e=>e.name===t.pipeline)||(n&&console.error("LlamaCloud pipeline ".concat(t.pipeline," not found. Check LLAMA_CLOUD_INDEX_NAME variable")),!1):(n&&console.error("LlamaCloud project ".concat(t.project," not found. Check LLAMA_CLOUD_PROJECT_NAME variable")),!1)}var U=n(59947);function O(){var e,t;let{requestData:n,isLoading:s,input:l}=(0,c.a)(),o=null!=(e=(0,i.getConfig)("UPLOAD_API"))?e:"",d=null!=(t=(0,i.getConfig)("LLAMA_CLOUD_API"))?t:"true"===U.env.NEXT_PUBLIC_SHOW_LLAMACLOUD_SELECTOR?"/api/chat/config/llamacloud":"",{imageUrl:u,setImageUrl:m,uploadFile:h,files:p,removeDoc:f,reset:x,getAnnotations:g}=(0,z.u)({uploadAPI:o}),j=async e=>{if(u)return void alert("You can only upload one image at a time.");try{await h(e,n)}catch(e){alert(e instanceof Error?e.message:"An unknown error occurred")}},v=g();return(0,a.jsxs)(r.ChatInput,{resetUploadedFiles:x,annotations:v,children:[u&&(0,a.jsx)(F.ImagePreview,{url:u,onRemove:()=>m(null)}),p.length>0&&(0,a.jsx)("div",{className:"flex w-full gap-4 overflow-auto py-2",children:p.map(e=>(0,a.jsx)(F.DocumentInfo,{document:{url:e.url,sources:[]},className:"mb-2 mt-2",onRemove:()=>f(e)},e.id))}),(0,a.jsxs)(r.ChatInput.Form,{children:[(0,a.jsx)(r.ChatInput.Field,{}),o&&(0,a.jsx)(r.ChatInput.Upload,{onUpload:j}),d&&(0,a.jsx)(T,{}),(0,a.jsx)(r.ChatInput.Submit,{disabled:s||!l.trim()&&0===p.length&&!u})]})]})}function B(){return(0,a.jsx)(r.ChatMessage.Avatar,{children:(0,a.jsx)("img",{className:"border-1 rounded-full border-[#e711dd]",src:"/llama.png",alt:"Llama Logo"})})}var V=n(67238);let J=Object.values(V.M),H=e=>{let{componentDefs:t,appendError:n}=e,{message:s}=(0,r.useChatMessage)(),i=s.annotations,o=(0,l.useRef)(new Set),[c,d]=(0,l.useState)(!1),u=e=>{d(!0),n(e)};(0,l.useEffect)(()=>{if(!(null==i?void 0:i.length))return;let e=new Set(t.map(e=>e.type));i.forEach(t=>{let n=t.type;if(!n)return;let a=(0,V.g)(s,n);J.includes(n)||o.current.has(n)||a&&!e.has(n)&&(console.warn("No component found for event type: ".concat(n," or having error when rendering it. Ensure there is a component file named ").concat(n,".tsx or ").concat(n,".jsx in your components directory, and verify the code for any errors.")),o.current.add(n))})},[i,t]);let m=t.map(e=>{let t=(0,V.g)(s,e.type);return(null==t?void 0:t.length)?{...e,events:t}:null}).filter(e=>null!==e);return 0===m.length||c?null:(0,a.jsx)("div",{className:"components-container",children:m.map((e,t)=>(0,a.jsx)(l.Fragment,{children:function(e,t){return(0,a.jsx)(f,{onError:t,eventType:e.type,children:l.createElement(e.comp,{events:e.events})})}(e,u)},"".concat(e.type,"-").concat(t)))})},X={0:{icon:(0,a.jsx)("span",{children:"☀️"}),status:"Clear sky"},1:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF24️"}),status:"Mainly clear"},2:{icon:(0,a.jsx)("span",{children:"☁️"}),status:"Partly cloudy"},3:{icon:(0,a.jsx)("span",{children:"☁️"}),status:"Overcast"},45:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF2B️"}),status:"Fog"},48:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF2B️"}),status:"Depositing rime fog"},51:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Drizzle"},53:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Drizzle"},55:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Drizzle"},56:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Freezing Drizzle"},57:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Freezing Drizzle"},61:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Rain"},63:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Rain"},65:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Rain"},66:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Freezing Rain"},67:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Freezing Rain"},71:{icon:(0,a.jsx)("span",{children:"❄️"}),status:"Snow fall"},73:{icon:(0,a.jsx)("span",{children:"❄️"}),status:"Snow fall"},75:{icon:(0,a.jsx)("span",{children:"❄️"}),status:"Snow fall"},77:{icon:(0,a.jsx)("span",{children:"❄️"}),status:"Snow grains"},80:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Rain showers"},81:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Rain showers"},82:{icon:(0,a.jsx)("span",{children:"\uD83C\uDF27️"}),status:"Rain showers"},85:{icon:(0,a.jsx)("span",{children:"❄️"}),status:"Snow showers"},86:{icon:(0,a.jsx)("span",{children:"❄️"}),status:"Snow showers"},95:{icon:(0,a.jsx)("span",{children:"⛈️"}),status:"Thunderstorm"},96:{icon:(0,a.jsx)("span",{children:"⛈️"}),status:"Thunderstorm"},99:{icon:(0,a.jsx)("span",{children:"⛈️"}),status:"Thunderstorm"}},W=e=>new Date(e).toLocaleDateString("en-US",{weekday:"long"});function Y(e){let{data:t}=e,n=new Date(t.current.time).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric"});return(0,a.jsxs)("div",{className:"w-fit space-y-4 rounded-2xl bg-[#61B9F2] p-5 text-white shadow-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-xl",children:n}),(0,a.jsxs)("div",{className:"flex gap-4 text-5xl font-semibold",children:[(0,a.jsxs)("span",{children:[t.current.temperature_2m," ",t.current_units.temperature_2m]}),X[t.current.weather_code].icon]})]}),(0,a.jsx)("span",{className:"text-xl",children:X[t.current.weather_code].status})]}),(0,a.jsx)("div",{className:"grid grid-cols-6 gap-2",children:t.daily.time.map((e,n)=>0===n?null:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsx)("span",{children:W(e)}),(0,a.jsx)("div",{className:"text-4xl",children:X[t.daily.weather_code[n]].icon}),(0,a.jsx)("span",{className:"text-sm",children:X[t.daily.weather_code[n]].status})]},e))})]})}function q(){let{messages:e}=(0,c.a)(),{message:t}=(0,r.useChatMessage)(),n=(0,l.useMemo)(()=>(function(e,t){let n="id"in t?t.id:void 0;if(!n)return;let a=1;for(let t of e){let e=(0,V.g)(t,"tools");if(null==e?void 0:e.some(e=>"artifact"===e.toolCall.name)){if("id"in t&&t.id===n)return a;a++}}})(e,t),[e,t]),s=(0,V.g)(t,"tools");return 0===s.length?null:(0,a.jsx)(G,{data:s[0],artifactVersion:n})}function G(e){let{data:t,artifactVersion:n}=e;if(!t)return null;let{toolCall:r,toolOutput:s}=t;if(s.isError)return(0,a.jsxs)("div",{className:"border-l-2 border-red-400 pl-2",children:["There was an error when calling the tool ",r.name," with input:"," ",(0,a.jsx)("br",{}),JSON.stringify(r.input)]});if("get_weather_information"!==r.name)return null;{let e=s.output;return(0,a.jsx)(Y,{data:e})}}function Q(e){let{componentDefs:t,appendError:n}=e;return(0,a.jsxs)(r.ChatMessage.Content,{children:[(0,a.jsx)(r.ChatMessage.Content.Event,{}),(0,a.jsx)(r.ChatMessage.Content.AgentEvent,{}),(0,a.jsx)(q,{}),(0,a.jsx)(r.ChatMessage.Content.Image,{}),(0,a.jsx)(H,{componentDefs:t,appendError:n}),(0,a.jsx)(r.ChatMessage.Content.Markdown,{}),(0,a.jsx)(r.ChatMessage.Content.DocumentFile,{}),(0,a.jsx)(r.ChatMessage.Content.Source,{}),(0,a.jsx)(r.ChatMessage.Content.SuggestedQuestions,{})]})}var Z=n(59947);function $(e){var t;let{className:n}=e,{append:r,messages:s,requestData:l}=(0,c.a)(),o=null!=(t=(0,i.getConfig)("STARTER_QUESTIONS"))?t:JSON.parse(Z.env.NEXT_PUBLIC_STARTER_QUESTIONS||"[]");return 0===o.length||s.length>0?null:(0,a.jsx)(F.StarterQuestions,{append:e=>r(e,{data:l}),questions:o,className:n})}function K(e){let{componentDefs:t,appendError:n}=e,{messages:s}=(0,c.a)();return(0,a.jsxs)(r.ChatMessages,{children:[(0,a.jsxs)(r.ChatMessages.List,{children:[s.map((e,l)=>(0,a.jsxs)(r.ChatMessage,{message:e,isLast:l===s.length-1,children:[(0,a.jsx)(B,{}),(0,a.jsx)(Q,{componentDefs:t,appendError:n}),(0,a.jsx)(r.ChatMessage.Actions,{})]},l)),(0,a.jsx)(r.ChatMessages.Empty,{heading:"Hello there!",subheading:"I'm here to help you with your questions."}),(0,a.jsx)(r.ChatMessages.Loading,{})]}),(0,a.jsx)($,{})]})}n(84900),n(34063);var ee=n(49727);function et(e){let{errors:t,clearErrors:n}=e;return 0===t.length?null:(0,a.jsx)(h.Accordion,{type:"single",defaultValue:"errors",collapsible:!0,className:"rounded-xl border border-gray-100 bg-white shadow-md",children:(0,a.jsxs)(h.AccordionItem,{value:"errors",className:"border-none px-4",children:[(0,a.jsx)(h.AccordionTrigger,{className:"py-2 hover:no-underline",children:(0,a.jsxs)("div",{className:"flex flex-1 items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground font-bold",children:"Errors when rendering dynamic events from components directory"}),(0,a.jsx)("span",{className:"inline-flex h-5 w-5 items-center justify-center rounded-full bg-yellow-500 text-xs text-white",children:t.length})]}),(0,a.jsxs)("div",{className:(0,i.cn)((0,p.buttonVariants)({variant:"ghost",size:"sm"})),onClick:e=>{e.stopPropagation(),n()},children:[(0,a.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Clear all"})]})]})}),(0,a.jsx)(h.AccordionContent,{className:"pb-4",children:(0,a.jsx)("div",{className:"space-y-2",children:t.map((e,t)=>(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:e},t))})})]})})}var en=n(16602),ea=n(59947);let er="/api/dev/files/workflow";function es(){var e;return(null!=(e=(0,i.getConfig)("DEV_MODE"))?e:"true"===ea.env.NEXT_PUBLIC_DEV_MODE)?(0,a.jsx)(el,{}):null}function el(){var e;let[t,n]=(0,l.useState)(!1),[r,s]=(0,l.useState)(!1),[i,o]=(0,l.useState)(),[c,d]=(0,l.useState)(null),[m,h]=(0,l.useState)(null),[f,x]=(0,l.useState)(!1),[g,j]=(0,l.useState)(null),[v,b]=(0,l.useState)(!1),[w,y]=(0,l.useState)(null);async function N(){try{s(!0);let t=await fetch(er),n=await t.json();if(!t.ok){var e;throw Error(null!=(e=null==n?void 0:n.detail)?e:"Unknown error")}d(n),o(null)}catch(e){o(e instanceof Error?e.message:"Unknown error"),console.warn("Error fetching workflow code:",e)}finally{s(!1)}}async function C(){if(!c)return;let e=c.last_modified;b(!0),y(null);let t=Date.now(),a=async()=>{if(Date.now()-t>3e4)return void y("Server not responding after ".concat(30," seconds."));try{let t=await fetch(er),r=await t.json();r.last_modified!==e?(d(r),h(r.content),b(!1),y(null),n(!1)):setTimeout(a,2e3)}catch(e){console.info("Polling error",e),setTimeout(a,2e3)}};setTimeout(a,2e3)}let S=async()=>{if(c)try{x(!0);let t=await fetch(er,{method:"PUT",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify({content:m,file_path:c.file_path})}),n=await t.json();if(!t.ok){var e;throw Error(null!=(e=null==n?void 0:n.detail)?e:"Unknown error")}j(null),await C()}catch(e){console.warn("Error saving workflow code:",e),j(e instanceof Error?e.message:"Unknown error happened when saving workflow code")}finally{x(!1)}};(0,l.useEffect)(()=>{t&&N()},[t]);let E=(0,l.useMemo)(()=>{var e;if(null==c?void 0:c.file_path)return(0,F.fileExtensionToEditorLang)(null!=(e=c.file_path.split(".").pop())?e:"")},[c]);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.Button,{onClick:()=>n(!t),className:"fixed right-2 top-1/2 origin-right -translate-y-1/2 rotate-90 transform rounded-l-md shadow-md transition-transform hover:-translate-x-1",children:"Dev Mode"}),v&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex flex-col items-center justify-center bg-black/50 backdrop-blur-sm",children:[!w&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mb-4 h-16 w-16 animate-spin text-white"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-white",children:"Applying changes and restarting server..."}),(0,a.jsx)("p",{className:"mt-2 text-sm text-slate-300",children:"Please wait for a while then you can start chatting with the updated workflow."})]}),w&&(0,a.jsxs)("div",{className:"bg-destructive/20 text-destructive-foreground mt-4 max-w-md rounded-md p-4 text-center",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-center gap-2",children:[(0,a.jsx)(en.A,{className:"shrink-0",size:16}),(0,a.jsx)("h6",{className:"text-sm font-medium",children:"Server Starting Error"})]}),(0,a.jsx)("p",{className:"text-sm",children:w}),(0,a.jsx)("p",{className:"text-sm",children:"Please reload the page and check server logs."})]})]}),(0,a.jsx)("div",{className:"border-border fixed right-0 top-0 z-10 h-full w-full border-l shadow-xl transition-all duration-300 ease-in-out ".concat(t?"translate-x-0 bg-black/50":"translate-x-full"),onClick:()=>n(!1),children:(0,a.jsxs)("div",{className:"bg-background ml-auto flex h-full w-[800px] flex-col p-4",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-bold",children:"Workflow Editor"}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:r?"Loading...":c?(0,a.jsxs)(a.Fragment,{children:["Edit the code of ",(0,a.jsx)("b",{children:c.file_path})," and save to apply changes to your workflow."]}):""})]}),(0,a.jsx)(p.Button,{variant:"outline",size:"sm",onClick:()=>n(!1),children:"Close"})]}),(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:i?(0,a.jsxs)("div",{className:"bg-destructive/10 text-destructive/70 mb-4 flex items-center gap-2 rounded-md p-4",children:[(0,a.jsx)(en.A,{className:"shrink-0",size:16}),(0,a.jsx)("p",{className:"text-sm font-medium",children:i})]}):(0,a.jsx)(F.CodeEditor,{code:null!=(e=null!=m?m:null==c?void 0:c.content)?e:"",onChange:h,language:E})}),(0,a.jsxs)("div",{className:"mt-4 flex flex-col",children:[g&&(0,a.jsxs)("div",{className:"bg-destructive/10 text-destructive/70 mb-4 rounded-md p-4",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,a.jsx)(en.A,{className:"shrink-0",size:16}),(0,a.jsx)("h6",{className:"text-sm font-medium",children:"Error Saving Code"})]}),(0,a.jsx)("p",{className:"whitespace-pre-wrap text-sm",children:g})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(p.Button,{variant:"outline",className:"mr-2",onClick:()=>{var e;h(null!=(e=null==c?void 0:c.content)?e:null),j(null)},children:"Reset Code"}),(0,a.jsxs)(p.Button,{onClick:S,disabled:f||!m||!c,children:["Save & Restart Server",f&&(0,a.jsx)(u.A,{className:"ml-2 h-4 w-4 animate-spin"})]})]})]})]})})]})}var ei=n(12018),eo=n(70065);function ec(){return(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 px-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ei.A,{className:"size-4"}),(0,a.jsx)("h1",{className:"font-semibold",children:"LlamaIndex App"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("a",{href:"https://www.llamaindex.ai/",target:"_blank",rel:"noopener noreferrer",className:"text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200",children:"Built by LlamaIndex"}),(0,a.jsx)("img",{className:"h-[24px] w-[24px] rounded-sm",src:"/llama.png",alt:"Llama Logo"})]}),(0,a.jsxs)("a",{href:"https://github.com/run-llama/LlamaIndexTS",target:"_blank",rel:"noopener noreferrer",className:"hover:bg-accent flex items-center gap-2 rounded-md border border-gray-300 px-2 py-1 text-sm",children:[(0,a.jsx)(eo.A,{className:"size-4"}),"Star on GitHub"]})]})]})}function ed(e){let{children:t}=e,[n,r]=(0,l.useState)([]),[s,i]=(0,l.useState)(!1),[o,c]=(0,l.useState)([]);(0,l.useEffect)(()=>{(async()=>{i(!0);let e=await eh();if(e.length){let t=await em(e);r(t),c(e=>[...e,...t.map(e=>e.error).filter(Boolean)])}i(!1)})()},[]);let d=e=>{c(t=>[...t,e])},m=e=>{var t;return null==(t=n.find(t=>t.type===e))?void 0:t.component};if(s)return(0,a.jsx)("div",{className:"flex h-screen w-screen flex-col items-center justify-center overflow-hidden",children:(0,a.jsx)(u.A,{className:"text-muted-foreground animate-spin"})});let h=[...new Set(o)];return(0,a.jsxs)("div",{className:"flex h-screen w-screen flex-col overflow-hidden",children:[h.length>0&&(0,a.jsxs)("div",{className:"w-full bg-yellow-100 px-4 py-2 text-black/70",children:[(0,a.jsx)("h2",{className:"mb-2 font-semibold",children:"Errors happened while rendering the layout:"}),h.map(e=>(0,a.jsx)("div",{className:"text-sm",children:e},e))]}),(0,a.jsx)(eu,{component:m("header"),onError:d,fallback:(0,a.jsx)(ec,{})}),t,(0,a.jsx)(eu,{component:m("footer"),onError:d})]})}function eu(e){let{component:t,onError:n,fallback:r}=e;return t?(0,a.jsx)(f,{onError:n,fallback:r,children:l.createElement(t)}):r}async function em(e){return await Promise.all(e.map(async e=>{let t=await C(e.code,e.filename);return{...e,...t}}))}async function eh(){try{let e=(0,i.getConfig)("LAYOUT_API");if(!e)return[];let t=await fetch(e);return await t.json()}catch(e){return console.warn("Error fetching layout files: ",e instanceof Error?e.message:"Unknown error"),[]}}function ep(){let e=(0,s.Y_)({api:(0,i.getConfig)("CHAT_API")||"/api/chat",onError:e=>{let t;if(!(e instanceof Error))throw e;try{t=JSON.parse(e.message).detail}catch(n){t=e.message}alert(t)},experimental_throttle:100});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed,{children:(0,a.jsxs)(r.ChatSection,{handler:e,className:"relative flex min-h-0 flex-1 flex-row justify-center gap-4 px-4 py-0",children:[(0,a.jsxs)(o.ResizablePanelGroup,{direction:"horizontal",children:[(0,a.jsx)(ef,{}),(0,a.jsx)(_,{})]}),(0,a.jsx)(es,{})]})}),(0,a.jsx)(L,{})]})}function ef(){let[e,t]=(0,l.useState)([]),[n,r]=(0,l.useState)([]),s=(0,l.useMemo)(()=>Array.from(new Set(n)),[n]);return(0,l.useEffect)(()=>{N().then(e=>{let{components:n,errors:a}=e;t(n),a.length>0&&r(e=>[...e,...a])})},[]),(0,a.jsx)(o.ResizablePanel,{defaultSize:40,minSize:30,className:"max-w-1/2 mx-auto",children:(0,a.jsxs)("div",{className:"flex h-full min-w-0 flex-1 flex-col gap-4",children:[(0,a.jsx)(et,{errors:s,clearErrors:()=>r([])}),(0,a.jsx)(K,{componentDefs:e,appendError:e=>{r(t=>[...t,e])}}),(0,a.jsx)(O,{})]})})}},61971:(e,t,n)=>{n.r(t),n.d(t,{cn:()=>s,getConfig:()=>l});var a=n(53184),r=n(18181);function s(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.QP)((0,a.$)(t))}let l=e=>{var t;return null==(t=window.LLAMAINDEX)?void 0:t[e]}},96740:(e,t,n)=>{n.r(t),n.d(t,{Button:()=>c,buttonVariants:()=>o});var a=n(53891),r=n(7349),s=n(52722),l=n(73987),i=n(61971);let o=(0,s.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=l.forwardRef((e,t)=>{let{className:n,variant:s,size:l,asChild:c=!1,...d}=e,u=c?r.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(o({variant:s,size:l,className:n})),ref:t,...d})});c.displayName="Button"}}]);