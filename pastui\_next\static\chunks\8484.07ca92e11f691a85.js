"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8484],{18484:(e,t,a)=>{a.r(t),a.d(t,{Toggle:()=>d,toggleVariants:()=>o});var n=a(53891),r=a(26774),i=a(52722);a(73987);var s=a(61971);let o=(0,i.F)("inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap",{variants:{variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground"},size:{default:"h-9 px-2 min-w-9",sm:"h-8 px-1.5 min-w-8",lg:"h-10 px-2.5 min-w-10"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:i,...d}=e;return(0,n.jsx)(r.b,{"data-slot":"toggle",className:(0,s.cn)(o({variant:a,size:i,className:t})),...d})}},26774:(e,t,a)=>{a.d(t,{b:()=>u,l:()=>l});var n=a(73987),r=a(77292),i=a(25261),s=a(4513),o=a(53891),d="Toggle",l=n.forwardRef((e,t)=>{let{pressed:a,defaultPressed:n,onPressedChange:l,...u}=e,[c,g]=(0,i.i)({prop:a,onChange:l,defaultProp:null!=n&&n,caller:d});return(0,o.jsx)(s.sG.button,{type:"button","aria-pressed":c,"data-state":c?"on":"off","data-disabled":e.disabled?"":void 0,...u,ref:t,onClick:(0,r.m)(e.onClick,()=>{e.disabled||g(!c)})})});l.displayName=d;var u=l}}]);