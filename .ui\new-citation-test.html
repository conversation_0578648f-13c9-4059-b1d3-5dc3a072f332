<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新版引用功能测试</title>
    <link rel="stylesheet" href="citation.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-content {
            line-height: 1.6;
            color: #555;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="test-title">新版引用功能测试</h2>
        <div class="test-content">
            <p>这是一个测试段落，包含引用信息。重复报销可能导致多种风险<span class="citation" onclick="window.citationProcessor.showCitationDetails('test-1', JSON.stringify({rank: 1, filename: '常见问题类-001_重复报销风险.txt', content: '重复报销可能导致以下风险：\\n\\n1. 财务风险：增加企业成本，影响财务数据的真实性\\n2. 税务风险：可能被认定为虚假抵扣，面临税务处罚\\n3. 内控风险：暴露内部控制制度的缺陷\\n4. 合规风险：违反财务管理规定，影响企业的信誉', similarity_score: 0.95}))"><span class="citation-number">1</span><span class="citation-tooltip"><div class="tooltip-header">重复报销风险</div><div class="tooltip-meta">排名: #1 | 相似度: 95%</div><div class="tooltip-content">重复报销可能导致以下风险：

1. 财务风险：增加企业成本，影响财务数据的真实性
2. 税务风险：可能被认定为虚假抵扣，面临税务处罚
3. 内控风险：暴露内部控制制度的缺陷
4. 合规风险：违反财务管理规定，影响企业的信誉</div></span></span>，包括财务风险和合规风险<span class="citation" onclick="window.citationProcessor.showCitationDetails('test-2', JSON.stringify({rank: 2, filename: '常见问题类-002_财务合规管理.txt', content: '财务合规管理的重要性：\\n\\n企业必须建立健全的财务管理制度，确保：\\n- 财务数据的真实性和准确性\\n- 遵守相关法律法规\\n- 防范各类财务风险\\n- 提高内部控制水平', similarity_score: 0.88}))"><span class="citation-number">2</span><span class="citation-tooltip"><div class="tooltip-header">财务合规管理</div><div class="tooltip-meta">排名: #2 | 相似度: 88%</div><div class="tooltip-content">财务合规管理的重要性：

企业必须建立健全的财务管理制度，确保：
- 财务数据的真实性和准确性
- 遵守相关法律法规
- 防范各类财务风险
- 提高内部控制水平</div></span></span>。</p>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">功能说明</h2>
        <div class="test-content">
            <p><strong>新版引用系统特点：</strong></p>
            <ul>
                <li>✅ <strong>只显示序号</strong>：引用显示为圆形数字徽章（如 1、2、3）</li>
                <li>✅ <strong>悬浮显示详情</strong>：鼠标悬浮时显示文档名、排名、相似度和内容</li>
                <li>✅ <strong>内容可滚动</strong>：长文本内容支持滚动查看</li>
                <li>✅ <strong>处理换行符</strong>：正确处理 \n\n 等换行符</li>
                <li>✅ <strong>关注点分离</strong>：样式和逻辑独立到 citation.css 和 citation.js</li>
                <li>✅ <strong>调试模式</strong>：开启调试边框便于开发调试</li>
            </ul>
            
            <div class="debug-info">
                <strong>调试信息：</strong><br>
                - 引用样式：citation.css<br>
                - 引用逻辑：citation.js<br>
                - 调试模式：已开启（红色边框为引用，黄色边框为提示框）
            </div>
        </div>
    </div>

    <script src="citation.js"></script>
    <script>
        // 测试页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ 引用测试页面加载完成');
            console.log('✅ CitationProcessor 实例:', window.citationProcessor);
        });
    </script>
</body>
</html>
