"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1490,1964],{1964:(e,t,r)=>{r.r(t),r.d(t,{Dialog:()=>i,DialogClose:()=>d,DialogContent:()=>m,DialogDescription:()=>v,DialogFooter:()=>p,DialogHeader:()=>f,DialogOverlay:()=>s,DialogPortal:()=>u,DialogTitle:()=>g,DialogTrigger:()=>c});var n=r(53891),a=r(18526),l=r(49727);r(73987);var o=r(61971);function i(e){let{...t}=e;return(0,n.jsx)(a.bL,{"data-slot":"dialog",...t})}function c(e){let{...t}=e;return(0,n.jsx)(a.l9,{"data-slot":"dialog-trigger",...t})}function u(e){let{...t}=e;return(0,n.jsx)(a.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{...t}=e;return(0,n.jsx)(a.bm,{"data-slot":"dialog-close",...t})}function s(e){let{className:t,...r}=e;return(0,n.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...r})}function m(e){let{className:t,children:r,...i}=e;return(0,n.jsxs)(u,{"data-slot":"dialog-portal",children:[(0,n.jsx)(s,{}),(0,n.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed left-[50%] top-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,children:[r,(0,n.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground rounded-xs focus:outline-hidden absolute right-4 top-4 opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",children:[(0,n.jsx)(l.A,{}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function f(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...r})}function p(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...r})}function g(e){let{className:t,...r}=e;return(0,n.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg font-semibold leading-none",t),...r})}function v(e){let{className:t,...r}=e;return(0,n.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...r})}},61490:(e,t,r)=>{r.r(t),r.d(t,{Command:()=>U,CommandDialog:()=>T,CommandEmpty:()=>Z,CommandGroup:()=>W,CommandInput:()=>$,CommandItem:()=>Q,CommandList:()=>J,CommandSeparator:()=>Y,CommandShortcut:()=>X});var n=r(53891),a=/[\\\/_+.#"@\[\(\{&]/,l=/[\\\/_+.#"@\[\(\{&]/g,o=/[\s-]/,i=/[\s-]/g;function c(e){return e.toLowerCase().replace(i," ")}var u=r(18526),d=r(73987),s=r(4513),m=r(10145),f=r(77310),p='[cmdk-group=""]',g='[cmdk-group-items=""]',v='[cmdk-item=""]',h="".concat(v,':not([aria-disabled="true"])'),x="cmdk-item-select",b="data-value",k=(e,t,r)=>(function(e,t,r){return function e(t,r,n,c,u,d,s){if(d===r.length)return u===t.length?1:.99;var m=`${u},${d}`;if(void 0!==s[m])return s[m];for(var f,p,g,v,h=c.charAt(d),x=n.indexOf(h,u),b=0;x>=0;)(f=e(t,r,n,c,x+1,d+1,s))>b&&(x===u?f*=1:a.test(t.charAt(x-1))?(f*=.8,(g=t.slice(u,x-1).match(l))&&u>0&&(f*=Math.pow(.999,g.length))):o.test(t.charAt(x-1))?(f*=.9,(v=t.slice(u,x-1).match(i))&&u>0&&(f*=Math.pow(.999,v.length))):(f*=.17,u>0&&(f*=Math.pow(.999,x-u))),t.charAt(x)!==r.charAt(d)&&(f*=.9999)),(f<.1&&n.charAt(x-1)===c.charAt(d+1)||c.charAt(d+1)===c.charAt(d)&&n.charAt(x-1)!==c.charAt(d))&&.1*(p=e(t,r,n,c,x+1,d+2,s))>f&&(f=.1*p),f>b&&(b=f),x=n.indexOf(h,x+1);return s[m]=b,b}(e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,t,c(e),c(t),0,0,{})})(e,t,r),w=d.createContext(void 0),y=()=>d.useContext(w),E=d.createContext(void 0),C=()=>d.useContext(E),S=d.createContext(void 0),j=d.forwardRef((e,t)=>{let r=P(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),n=P(()=>new Set),a=P(()=>new Map),l=P(()=>new Map),o=P(()=>new Set),i=L(e),{label:c,children:u,value:f,onValueChange:y,filter:C,shouldFilter:S,loop:j,disablePointerSelection:_=!1,vimBindings:I=!0,...A}=e,D=(0,m.B)(),N=(0,m.B)(),R=(0,m.B)(),M=d.useRef(null),G=F();z(()=>{if(void 0!==f){let e=f.trim();r.current.value=e,B.emit()}},[f]),z(()=>{G(6,$)},[]);let B=d.useMemo(()=>({subscribe:e=>(o.current.add(e),()=>o.current.delete(e)),snapshot:()=>r.current,setState:(e,t,n)=>{var a,l,o,c;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)T(),H(),G(1,U);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(R);e?e.focus():null==(a=document.getElementById(D))||a.focus()}if(G(7,()=>{var e;r.current.selectedItemId=null==(e=J())?void 0:e.id,B.emit()}),n||G(5,$),(null==(l=i.current)?void 0:l.value)!==void 0){null==(c=(o=i.current).onValueChange)||c.call(o,null!=t?t:"");return}}B.emit()}},emit:()=>{o.current.forEach(e=>e())}}),[]),O=d.useMemo(()=>({value:(e,t,n)=>{var a;t!==(null==(a=l.current.get(e))?void 0:a.value)&&(l.current.set(e,{value:t,keywords:n}),r.current.filtered.items.set(e,V(t,n)),G(2,()=>{H(),B.emit()}))},item:(e,t)=>(n.current.add(e),t&&(a.current.has(t)?a.current.get(t).add(e):a.current.set(t,new Set([e]))),G(3,()=>{T(),H(),r.current.value||U(),B.emit()}),()=>{l.current.delete(e),n.current.delete(e),r.current.filtered.items.delete(e);let t=J();G(4,()=>{T(),(null==t?void 0:t.getAttribute("id"))===e&&U(),B.emit()})}),group:e=>(a.current.has(e)||a.current.set(e,new Set),()=>{l.current.delete(e),a.current.delete(e)}),filter:()=>i.current.shouldFilter,label:c||e["aria-label"],getDisablePointerSelection:()=>i.current.disablePointerSelection,listId:D,inputId:R,labelId:N,listInnerRef:M}),[]);function V(e,t){var n,a;let l=null!=(a=null==(n=i.current)?void 0:n.filter)?a:k;return e?l(e,r.current.search,t):0}function H(){if(!r.current.search||!1===i.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach(r=>{let n=a.current.get(r),l=0;n.forEach(t=>{l=Math.max(e.get(t),l)}),t.push([r,l])});let n=M.current;Z().sort((t,r)=>{var n,a;let l=t.getAttribute("id"),o=r.getAttribute("id");return(null!=(n=e.get(o))?n:0)-(null!=(a=e.get(l))?a:0)}).forEach(e=>{let t=e.closest(g);t?t.appendChild(e.parentElement===t?e:e.closest("".concat(g," > *"))):n.appendChild(e.parentElement===n?e:e.closest("".concat(g," > *")))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=M.current)?void 0:t.querySelector("".concat(p,"[").concat(b,'="').concat(encodeURIComponent(e[0]),'"]'));null==r||r.parentElement.appendChild(r)})}function U(){let e=Z().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(b);B.setState("value",t||void 0)}function T(){var e,t,o,c;if(!r.current.search||!1===i.current.shouldFilter){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let u=0;for(let a of n.current){let n=V(null!=(t=null==(e=l.current.get(a))?void 0:e.value)?t:"",null!=(c=null==(o=l.current.get(a))?void 0:o.keywords)?c:[]);r.current.filtered.items.set(a,n),n>0&&u++}for(let[e,t]of a.current)for(let n of t)if(r.current.filtered.items.get(n)>0){r.current.filtered.groups.add(e);break}r.current.filtered.count=u}function $(){var e,t,r;let n=J();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(p))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function J(){var e;return null==(e=M.current)?void 0:e.querySelector("".concat(v,'[aria-selected="true"]'))}function Z(){var e;return Array.from((null==(e=M.current)?void 0:e.querySelectorAll(h))||[])}function W(e){let t=Z()[e];t&&B.setState("value",t.getAttribute(b))}function Y(e){var t;let r=J(),n=Z(),a=n.findIndex(e=>e===r),l=n[a+e];null!=(t=i.current)&&t.loop&&(l=a+e<0?n[n.length-1]:a+e===n.length?n[0]:n[a+e]),l&&B.setState("value",l.getAttribute(b))}function Q(e){let t=J(),r=null==t?void 0:t.closest(p),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,p):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,p))?void 0:r.querySelector(h);n?B.setState("value",n.getAttribute(b)):Y(e)}let X=()=>W(Z().length-1),ee=e=>{e.preventDefault(),e.metaKey?X():e.altKey?Q(1):Y(1)},et=e=>{e.preventDefault(),e.metaKey?W(0):e.altKey?Q(-1):Y(-1)};return d.createElement(s.sG.div,{ref:t,tabIndex:-1,...A,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=A.onKeyDown)||t.call(A,e);let r=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||r))switch(e.key){case"n":case"j":I&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":I&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),W(0);break;case"End":e.preventDefault(),X();break;case"Enter":{e.preventDefault();let t=J();if(t){let e=new Event(x);t.dispatchEvent(e)}}}}},d.createElement("label",{"cmdk-label":"",htmlFor:O.inputId,id:O.labelId,style:K},c),q(e,e=>d.createElement(E.Provider,{value:B},d.createElement(w.Provider,{value:O},e))))}),_=d.forwardRef((e,t)=>{var r,n;let a=(0,m.B)(),l=d.useRef(null),o=d.useContext(S),i=y(),c=L(e),u=null!=(n=null==(r=c.current)?void 0:r.forceMount)?n:null==o?void 0:o.forceMount;z(()=>{if(!u)return i.item(a,null==o?void 0:o.id)},[u]);let p=B(a,l,[e.value,e.children,l],e.keywords),g=C(),v=G(e=>e.value&&e.value===p.current),h=G(e=>!!u||!1===i.filter()||!e.search||e.filtered.items.get(a)>0);function b(){var e,t;k(),null==(t=(e=c.current).onSelect)||t.call(e,p.current)}function k(){g.setState("value",p.current,!0)}if(d.useEffect(()=>{let t=l.current;if(!(!t||e.disabled))return t.addEventListener(x,b),()=>t.removeEventListener(x,b)},[h,e.onSelect,e.disabled]),!h)return null;let{disabled:w,value:E,onSelect:j,forceMount:_,keywords:I,...A}=e;return d.createElement(s.sG.div,{ref:(0,f.t)(l,t),...A,id:a,"cmdk-item":"",role:"option","aria-disabled":!!w,"aria-selected":!!v,"data-disabled":!!w,"data-selected":!!v,onPointerMove:w||i.getDisablePointerSelection()?void 0:k,onClick:w?void 0:b},e.children)}),I=d.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:a,...l}=e,o=(0,m.B)(),i=d.useRef(null),c=d.useRef(null),u=(0,m.B)(),p=y(),g=G(e=>!!a||!1===p.filter()||!e.search||e.filtered.groups.has(o));z(()=>p.group(o),[]),B(o,i,[e.value,e.heading,c]);let v=d.useMemo(()=>({id:o,forceMount:a}),[a]);return d.createElement(s.sG.div,{ref:(0,f.t)(i,t),...l,"cmdk-group":"",role:"presentation",hidden:!g||void 0},r&&d.createElement("div",{ref:c,"cmdk-group-heading":"","aria-hidden":!0,id:u},r),q(e,e=>d.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?u:void 0},d.createElement(S.Provider,{value:v},e))))}),A=d.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,a=d.useRef(null),l=G(e=>!e.search);return r||l?d.createElement(s.sG.div,{ref:(0,f.t)(a,t),...n,"cmdk-separator":"",role:"separator"}):null}),D=d.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,a=null!=e.value,l=C(),o=G(e=>e.search),i=G(e=>e.selectedItemId),c=y();return d.useEffect(()=>{null!=e.value&&l.setState("search",e.value)},[e.value]),d.createElement(s.sG.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":c.listId,"aria-labelledby":c.labelId,"aria-activedescendant":i,id:c.inputId,type:"text",value:a?e.value:o,onChange:e=>{a||l.setState("search",e.target.value),null==r||r(e.target.value)}})}),N=d.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...a}=e,l=d.useRef(null),o=d.useRef(null),i=G(e=>e.selectedItemId),c=y();return d.useEffect(()=>{if(o.current&&l.current){let e=o.current,t=l.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),d.createElement(s.sG.div,{ref:(0,f.t)(l,t),...a,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":i,"aria-label":n,id:c.listId},q(e,e=>d.createElement("div",{ref:(0,f.t)(o,c.listInnerRef),"cmdk-list-sizer":""},e)))}),R=d.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:a,contentClassName:l,container:o,...i}=e;return d.createElement(u.bL,{open:r,onOpenChange:n},d.createElement(u.ZL,{container:o},d.createElement(u.hJ,{"cmdk-overlay":"",className:a}),d.createElement(u.UC,{"aria-label":e.label,"cmdk-dialog":"",className:l},d.createElement(j,{ref:t,...i}))))}),M=Object.assign(j,{List:N,Item:_,Input:D,Group:I,Separator:A,Dialog:R,Empty:d.forwardRef((e,t)=>G(e=>0===e.filtered.count)?d.createElement(s.sG.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:d.forwardRef((e,t)=>{let{progress:r,children:n,label:a="Loading...",...l}=e;return d.createElement(s.sG.div,{ref:t,...l,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":a},q(e,e=>d.createElement("div",{"aria-hidden":!0},e)))})});function L(e){let t=d.useRef(e);return z(()=>{t.current=e}),t}var z="undefined"==typeof window?d.useEffect:d.useLayoutEffect;function P(e){let t=d.useRef();return void 0===t.current&&(t.current=e()),t}function G(e){let t=C(),r=()=>e(t.snapshot());return d.useSyncExternalStore(t.subscribe,r,r)}function B(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],a=d.useRef(),l=y();return z(()=>{var o;let i=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():a.current}})(),c=n.map(e=>e.trim());l.value(e,i,c),null==(o=t.current)||o.setAttribute(b,i),a.current=i}),a}var F=()=>{let[e,t]=d.useState(),r=P(()=>new Map);return z(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}};function q(e,t){let r,{asChild:n,children:a}=e;return n&&d.isValidElement(a)?d.cloneElement("function"==typeof(r=a.type)?r(a.props):"render"in r?r.render(a.props):a,{ref:a.ref},t(a.props.children)):t(a)}var K={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},O=r(75829),V=r(1964),H=r(61971);function U(e){let{className:t,...r}=e;return(0,n.jsx)(M,{"data-slot":"command",className:(0,H.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",t),...r})}function T(e){let{title:t="Command Palette",description:r="Search for a command to run...",children:a,...l}=e;return(0,n.jsxs)(V.Dialog,{...l,children:[(0,n.jsxs)(V.DialogHeader,{className:"sr-only",children:[(0,n.jsx)(V.DialogTitle,{children:t}),(0,n.jsx)(V.DialogDescription,{children:r})]}),(0,n.jsx)(V.DialogContent,{className:"overflow-hidden p-0",children:(0,n.jsx)(U,{className:"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:a})})]})}function $(e){let{className:t,...r}=e;return(0,n.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,n.jsx)(O.A,{className:"size-4 shrink-0 opacity-50"}),(0,n.jsx)(M.Input,{"data-slot":"command-input",className:(0,H.cn)("placeholder:text-muted-foreground outline-hidden flex h-10 w-full rounded-md bg-transparent py-3 text-sm disabled:cursor-not-allowed disabled:opacity-50",t),...r})]})}function J(e){let{className:t,...r}=e;return(0,n.jsx)(M.List,{"data-slot":"command-list",className:(0,H.cn)("max-h-[300px] scroll-py-1 overflow-y-auto overflow-x-hidden",t),...r})}function Z(e){let{...t}=e;return(0,n.jsx)(M.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...t})}function W(e){let{className:t,...r}=e;return(0,n.jsx)(M.Group,{"data-slot":"command-group",className:(0,H.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",t),...r})}function Y(e){let{className:t,...r}=e;return(0,n.jsx)(M.Separator,{"data-slot":"command-separator",className:(0,H.cn)("bg-border -mx-1 h-px",t),...r})}function Q(e){let{className:t,...r}=e;return(0,n.jsx)(M.Item,{"data-slot":"command-item",className:(0,H.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground outline-hidden relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),...r})}function X(e){let{className:t,...r}=e;return(0,n.jsx)("span",{"data-slot":"command-shortcut",className:(0,H.cn)("text-muted-foreground ml-auto text-xs tracking-widest",t),...r})}},75829:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(75779).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}}]);