"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9382],{39382:(e,r,a)=>{a.r(r),a.d(r,{Card:()=>s,CardContent:()=>o,CardDescription:()=>f,CardFooter:()=>c,CardHeader:()=>n,CardTitle:()=>i});var t=a(53891),d=a(73987),l=a(61971);let s=(0,d.forwardRef)((e,r)=>{let{className:a,...d}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("rounded-xl border border-neutral-200 bg-white text-neutral-950 shadow-sm dark:border-neutral-800 dark:bg-neutral-950 dark:text-neutral-50",a),...d})});s.displayName="Card";let n=(0,d.forwardRef)((e,r)=>{let{className:a,...d}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...d})});n.displayName="CardHeader";let i=(0,d.forwardRef)((e,r)=>{let{className:a,...d}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("font-semibold leading-none tracking-tight",a),...d})});i.displayName="CardTitle";let f=(0,d.forwardRef)((e,r)=>{let{className:a,...d}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("text-sm text-neutral-500 dark:text-neutral-400",a),...d})});f.displayName="CardDescription";let o=(0,d.forwardRef)((e,r)=>{let{className:a,...d}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",a),...d})});o.displayName="CardContent";let c=(0,d.forwardRef)((e,r)=>{let{className:a,...d}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",a),...d})});c.displayName="CardFooter"}}]);