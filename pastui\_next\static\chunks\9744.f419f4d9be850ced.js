"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9744],{69744:(e,t,n)=>{n.r(t),n.d(t,{DropdownMenu:()=>K,DropdownMenuCheckboxItem:()=>Z,DropdownMenuContent:()=>F,DropdownMenuGroup:()=>B,DropdownMenuItem:()=>J,DropdownMenuLabel:()=>V,DropdownMenuPortal:()=>L,DropdownMenuRadioGroup:()=>q,DropdownMenuRadioItem:()=>H,DropdownMenuSeparator:()=>Y,DropdownMenuShortcut:()=>Q,DropdownMenuSub:()=>W,DropdownMenuSubContent:()=>$,DropdownMenuSubTrigger:()=>X,DropdownMenuTrigger:()=>U});var r=n(53891),o=n(73987),a=n(77292),d=n(77310),s=n(80428),i=n(25261),u=n(4513),l=n(36704),p=n(10145),c="DropdownMenu",[f,m]=(0,s.A)(c,[l.UE]),g=(0,l.UE)(),[x,w]=f(c),v=e=>{let{__scopeDropdownMenu:t,children:n,dir:a,open:d,defaultOpen:s,onOpenChange:u,modal:f=!0}=e,m=g(t),w=o.useRef(null),[v,h]=(0,i.i)({prop:d,defaultProp:null!=s&&s,onChange:u,caller:c});return(0,r.jsx)(x,{scope:t,triggerId:(0,p.B)(),triggerRef:w,contentId:(0,p.B)(),open:v,onOpenChange:h,onOpenToggle:o.useCallback(()=>h(e=>!e),[h]),modal:f,children:(0,r.jsx)(l.bL,{...m,open:v,onOpenChange:h,dir:a,modal:f,children:n})})};v.displayName=c;var h="DropdownMenuTrigger",b=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:o=!1,...s}=e,i=w(h,n),p=g(n);return(0,r.jsx)(l.Mz,{asChild:!0,...p,children:(0,r.jsx)(u.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...s,ref:(0,d.t)(t,i.triggerRef),onPointerDown:(0,a.m)(e.onPointerDown,e=>{!o&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{!o&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});b.displayName=h;var j=e=>{let{__scopeDropdownMenu:t,...n}=e,o=g(t);return(0,r.jsx)(l.ZL,{...o,...n})};j.displayName="DropdownMenuPortal";var y="DropdownMenuContent",D=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...d}=e,s=w(y,n),i=g(n),u=o.useRef(!1);return(0,r.jsx)(l.UC,{id:s.contentId,"aria-labelledby":s.triggerId,...i,...d,ref:t,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=s.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,a.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!s.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});D.displayName=y;var M=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.YJ,{...a,...o,ref:t})});M.displayName="DropdownMenuGroup";var N=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.JU,{...a,...o,ref:t})});N.displayName="DropdownMenuLabel";var R=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.q7,{...a,...o,ref:t})});R.displayName="DropdownMenuItem";var z=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.H_,{...a,...o,ref:t})});z.displayName="DropdownMenuCheckboxItem";var C=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.z6,{...a,...o,ref:t})});C.displayName="DropdownMenuRadioGroup";var k=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.hN,{...a,...o,ref:t})});k.displayName="DropdownMenuRadioItem";var I=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.VF,{...a,...o,ref:t})});I.displayName="DropdownMenuItemIndicator";var _=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.wv,{...a,...o,ref:t})});_.displayName="DropdownMenuSeparator",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.i3,{...a,...o,ref:t})}).displayName="DropdownMenuArrow";var O=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.ZP,{...a,...o,ref:t})});O.displayName="DropdownMenuSubTrigger";var A=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,a=g(n);return(0,r.jsx)(l.G5,{...a,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});A.displayName="DropdownMenuSubContent";var S=e=>{let{__scopeDropdownMenu:t,children:n,open:o,onOpenChange:a,defaultOpen:d}=e,s=g(t),[u,p]=(0,i.i)({prop:o,defaultProp:null!=d&&d,onChange:a,caller:"DropdownMenuSub"});return(0,r.jsx)(l.Pb,{...s,open:u,onOpenChange:p,children:n})},P=n(25907),E=n(41341),T=n(89111),G=n(61971);function K(e){let{...t}=e;return(0,r.jsx)(v,{"data-slot":"dropdown-menu",...t})}function L(e){let{...t}=e;return(0,r.jsx)(j,{"data-slot":"dropdown-menu-portal",...t})}function U(e){let{...t}=e;return(0,r.jsx)(b,{"data-slot":"dropdown-menu-trigger",...t})}function F(e){let{className:t,sideOffset:n=4,...o}=e;return(0,r.jsx)(j,{children:(0,r.jsx)(D,{"data-slot":"dropdown-menu-content",sideOffset:n,className:(0,G.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 max-h-(--radix-dropdown-menu-content-available-height) origin-(--radix-dropdown-menu-content-transform-origin) z-50 min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border p-1 shadow-md",t),...o})})}function B(e){let{...t}=e;return(0,r.jsx)(M,{"data-slot":"dropdown-menu-group",...t})}function J(e){let{className:t,inset:n,variant:o="default",...a}=e;return(0,r.jsx)(R,{"data-slot":"dropdown-menu-item","data-inset":n,"data-variant":o,className:(0,G.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground outline-hidden relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm data-[disabled]:pointer-events-none data-[inset]:pl-8 data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),...a})}function Z(e){let{className:t,children:n,checked:o,...a}=e;return(0,r.jsxs)(z,{"data-slot":"dropdown-menu-checkbox-item",className:(0,G.cn)("focus:bg-accent focus:text-accent-foreground outline-hidden relative flex cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-8 pr-2 text-sm data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),checked:o,...a,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(I,{children:(0,r.jsx)(P.A,{className:"size-4"})})}),n]})}function q(e){let{...t}=e;return(0,r.jsx)(C,{"data-slot":"dropdown-menu-radio-group",...t})}function H(e){let{className:t,children:n,...o}=e;return(0,r.jsxs)(k,{"data-slot":"dropdown-menu-radio-item",className:(0,G.cn)("focus:bg-accent focus:text-accent-foreground outline-hidden relative flex cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-8 pr-2 text-sm data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),...o,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(I,{children:(0,r.jsx)(E.A,{className:"size-2 fill-current"})})}),n]})}function V(e){let{className:t,inset:n,...o}=e;return(0,r.jsx)(N,{"data-slot":"dropdown-menu-label","data-inset":n,className:(0,G.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...o})}function Y(e){let{className:t,...n}=e;return(0,r.jsx)(_,{"data-slot":"dropdown-menu-separator",className:(0,G.cn)("bg-border -mx-1 my-1 h-px",t),...n})}function Q(e){let{className:t,...n}=e;return(0,r.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,G.cn)("text-muted-foreground ml-auto text-xs tracking-widest",t),...n})}function W(e){let{...t}=e;return(0,r.jsx)(S,{"data-slot":"dropdown-menu-sub",...t})}function X(e){let{className:t,inset:n,children:o,...a}=e;return(0,r.jsxs)(O,{"data-slot":"dropdown-menu-sub-trigger","data-inset":n,className:(0,G.cn)("focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground outline-hidden flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm data-[inset]:pl-8",t),...a,children:[o,(0,r.jsx)(T.A,{className:"ml-auto size-4"})]})}function $(e){let{className:t,...n}=e;return(0,r.jsx)(A,{"data-slot":"dropdown-menu-sub-content",className:(0,G.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--radix-dropdown-menu-content-transform-origin) z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-lg",t),...n})}}}]);