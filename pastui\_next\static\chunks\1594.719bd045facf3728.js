"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1594],{71594:(t,e,n)=>{let r;n.d(e,{_s:()=>B});var a=n(18526),o=n(73987);let i=o.createContext({drawerRef:{current:null},overlayRef:{current:null},onPress:()=>{},onRelease:()=>{},onDrag:()=>{},onNestedDrag:()=>{},onNestedOpenChange:()=>{},onNestedRelease:()=>{},openProp:void 0,dismissible:!1,isOpen:!1,isDragging:!1,keyboardIsOpen:{current:!1},snapPointsOffset:null,snapPoints:null,handleOnly:!1,modal:!1,shouldFade:!1,activeSnapPoint:null,onOpenChange:()=>{},setActiveSnapPoint:()=>{},closeDrawer:()=>{},direction:"bottom",shouldAnimate:{current:!0},shouldScaleBackground:!1,setBackgroundColorOnScale:!0,noBodyStyles:!1,container:null,autoFocus:!1}),l=()=>{let t=o.useContext(i);if(!t)throw Error("useDrawerContext must be used within a Drawer.Root");return t};function u(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}function c(){return d(/^iPhone/)||d(/^iPad/)||d(/^Mac/)&&navigator.maxTouchPoints>1}function d(t){return"undefined"!=typeof window&&null!=window.navigator?t.test(window.navigator.platform):void 0}!function(t){if(!t||"undefined"==typeof document)return;let e=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",e.appendChild(n),n.styleSheet?n.styleSheet.cssText=t:n.appendChild(document.createTextNode(t))}("[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--initial-transform,100%),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--initial-transform,100%),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-animate=false]{animation:none!important}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\n[data-state=closed]\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,var(--initial-transform,100%),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,var(--initial-transform,100%),0)}}@keyframes slideFromTop{from{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}}@keyframes slideFromLeft{from{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}}@keyframes slideFromRight{from{transform:translate3d(var(--initial-transform,100%),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(var(--initial-transform,100%),0,0)}}");let s="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;function f(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];for(let t of e)"function"==typeof t&&t(...n)}}let m="undefined"!=typeof document&&window.visualViewport;function p(t){let e=window.getComputedStyle(t);return/(auto|scroll)/.test(e.overflow+e.overflowX+e.overflowY)}function h(t){for(p(t)&&(t=t.parentElement);t&&!p(t);)t=t.parentElement;return t||document.scrollingElement||document.documentElement}let v=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),w=0;function g(t,e,n,r){return t.addEventListener(e,n,r),()=>{t.removeEventListener(e,n,r)}}function y(t){let e=document.scrollingElement||document.documentElement;for(;t&&t!==e;){let e=h(t);if(e!==document.documentElement&&e!==document.body&&e!==t){let n=e.getBoundingClientRect().top,r=t.getBoundingClientRect().top;t.getBoundingClientRect().bottom>e.getBoundingClientRect().bottom+24&&(e.scrollTop+=r-n)}t=e.parentElement}}function b(t){return t instanceof HTMLInputElement&&!v.has(t.type)||t instanceof HTMLTextAreaElement||t instanceof HTMLElement&&t.isContentEditable}function E(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return o.useCallback(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}(...e),e)}let R=new WeakMap;function x(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!t||!(t instanceof HTMLElement))return;let r={};Object.entries(e).forEach(e=>{let[n,a]=e;if(n.startsWith("--"))return void t.style.setProperty(n,a);r[n]=t.style[n],t.style[n]=a}),n||R.set(t,r)}let T=t=>{switch(t){case"top":case"bottom":return!0;case"left":case"right":return!1;default:return t}};function D(t,e){if(!t)return null;let n=window.getComputedStyle(t),r=n.transform||n.webkitTransform||n.mozTransform,a=r.match(/^matrix3d\((.+)\)$/);return a?parseFloat(a[1].split(", ")[T(e)?13:12]):(a=r.match(/^matrix\((.+)\)$/))?parseFloat(a[1].split(", ")[T(e)?5:4]):null}function C(t,e){if(!t)return()=>{};let n=t.style.cssText;return Object.assign(t.style,e),()=>{t.style.cssText=n}}let A={DURATION:.5,EASE:[.32,.72,0,1]},M="vaul-dragging";function O(t){let e=o.useRef(t);return o.useEffect(()=>{e.current=t}),o.useMemo(()=>function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return null==e.current?void 0:e.current.call(e,...n)},[])}function P(t){let{prop:e,defaultProp:n,onChange:r=()=>{}}=t,[a,i]=function(t){let{defaultProp:e,onChange:n}=t,r=o.useState(e),[a]=r,i=o.useRef(a),l=O(n);return o.useEffect(()=>{i.current!==a&&(l(a),i.current=a)},[a,i,l]),r}({defaultProp:n,onChange:r}),l=void 0!==e,u=l?e:a,c=O(r);return[u,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&c(n)}else i(t)},[l,e,i,c])]}let S=()=>()=>{},k=null;function N(t){var e,n;let{open:l,onOpenChange:d,children:p,onDrag:v,onRelease:E,snapPoints:C,shouldScaleBackground:O=!1,setBackgroundColorOnScale:S=!0,closeThreshold:N=.25,scrollLockTimeout:F=100,dismissible:I=!0,handleOnly:L=!1,fadeFromIndex:B=C&&C.length-1,activeSnapPoint:H,setActiveSnapPoint:z,fixed:W,modal:U=!0,onClose:j,nested:q,noBodyStyles:Y=!1,direction:X="bottom",defaultOpen:V=!1,disablePreventScroll:_=!0,snapToSequentialPoint:$=!1,preventScrollRestoration:J=!1,repositionInputs:Z=!0,onAnimationEnd:G,container:K,autoFocus:Q=!1}=t,[tt=!1,te]=P({defaultProp:V,prop:l,onChange:t=>{null==d||d(t),t||q||tk(),setTimeout(()=>{null==G||G(t)},1e3*A.DURATION),t&&!U&&"undefined"!=typeof window&&window.requestAnimationFrame(()=>{document.body.style.pointerEvents="auto"}),t||(document.body.style.pointerEvents="auto")}}),[tn,tr]=o.useState(!1),[ta,to]=o.useState(!1),[ti,tl]=o.useState(!1),tu=o.useRef(null),tc=o.useRef(null),td=o.useRef(null),ts=o.useRef(null),tf=o.useRef(null),tm=o.useRef(!1),tp=o.useRef(null),th=o.useRef(0),tv=o.useRef(!1),tw=o.useRef(!V),tg=o.useRef(0),ty=o.useRef(null),tb=o.useRef((null==(e=ty.current)?void 0:e.getBoundingClientRect().height)||0),tE=o.useRef((null==(n=ty.current)?void 0:n.getBoundingClientRect().width)||0),tR=o.useRef(0),tx=o.useCallback(t=>{C&&t===tM.length-1&&(tc.current=new Date)},[]),{activeSnapPoint:tT,activeSnapPointIndex:tD,setActiveSnapPoint:tC,onRelease:tA,snapPointsOffset:tM,onDrag:tO,shouldFade:tP,getPercentageDragged:tS}=function(t){let{activeSnapPointProp:e,setActiveSnapPointProp:n,snapPoints:r,drawerRef:a,overlayRef:i,fadeFromIndex:l,onSnapPointChange:u,direction:c="bottom",container:d,snapToSequentialPoint:s}=t,[f,m]=P({prop:e,defaultProp:null==r?void 0:r[0],onChange:n}),[p,h]=o.useState("undefined"!=typeof window?{innerWidth:window.innerWidth,innerHeight:window.innerHeight}:void 0);o.useEffect(()=>{function t(){h({innerWidth:window.innerWidth,innerHeight:window.innerHeight})}return window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[]);let v=o.useMemo(()=>f===(null==r?void 0:r[r.length-1])||null,[r,f]),w=o.useMemo(()=>{var t;return null!=(t=null==r?void 0:r.findIndex(t=>t===f))?t:null},[r,f]),g=r&&r.length>0&&(l||0===l)&&!Number.isNaN(l)&&r[l]===f||!r,y=o.useMemo(()=>{var t;let e=d?{width:d.getBoundingClientRect().width,height:d.getBoundingClientRect().height}:"undefined"!=typeof window?{width:window.innerWidth,height:window.innerHeight}:{width:0,height:0};return null!=(t=null==r?void 0:r.map(t=>{let n="string"==typeof t,r=0;if(n&&(r=parseInt(t,10)),T(c)){let a=n?r:p?t*e.height:0;return p?"bottom"===c?e.height-a:-e.height+a:a}let a=n?r:p?t*e.width:0;return p?"right"===c?e.width-a:-e.width+a:a}))?t:[]},[r,p,d]),b=o.useMemo(()=>null!==w?null==y?void 0:y[w]:null,[y,w]),E=o.useCallback(t=>{var e;let n=null!=(e=null==y?void 0:y.findIndex(e=>e===t))?e:null;u(n),x(a.current,{transition:"transform ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),transform:T(c)?"translate3d(0, ".concat(t,"px, 0)"):"translate3d(".concat(t,"px, 0, 0)")}),y&&n!==y.length-1&&void 0!==l&&n!==l&&n<l?x(i.current,{transition:"opacity ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),opacity:"0"}):x(i.current,{transition:"opacity ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),opacity:"1"}),m(null==r?void 0:r[Math.max(n,0)])},[a.current,r,y,l,i,m]);return o.useEffect(()=>{if(f||e){var t;let n=null!=(t=null==r?void 0:r.findIndex(t=>t===e||t===f))?t:-1;y&&-1!==n&&"number"==typeof y[n]&&E(y[n])}},[f,e,r,y,E]),{isLastSnapPoint:v,activeSnapPoint:f,shouldFade:g,getPercentageDragged:function(t,e){if(!r||"number"!=typeof w||!y||void 0===l)return null;let n=w===l-1;if(w>=l&&e)return 0;if(n&&!e)return 1;if(!g&&!n)return null;let a=n?w+1:w-1,o=t/Math.abs(n?y[a]-y[a-1]:y[a+1]-y[a]);return n?1-o:o},setActiveSnapPoint:m,activeSnapPointIndex:w,onRelease:function(t){let{draggedDistance:e,closeDrawer:n,velocity:a,dismissible:o}=t;if(void 0===l)return;let u="bottom"===c||"right"===c?(null!=b?b:0)-e:(null!=b?b:0)+e,d=w===l-1,f=0===w,m=e>0;if(d&&x(i.current,{transition:"opacity ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")")}),!s&&a>2&&!m)return void(o?n():E(y[0]));if(!s&&a>2&&m&&y&&r)return void E(y[r.length-1]);let p=null==y?void 0:y.reduce((t,e)=>"number"!=typeof t||"number"!=typeof e?t:Math.abs(e-u)<Math.abs(t-u)?e:t),h=T(c)?window.innerHeight:window.innerWidth;if(a>.4&&Math.abs(e)<.4*h){let t=m?1:-1;return t>0&&v&&r?void E(y[r.length-1]):void(f&&t<0&&o&&n(),null===w||E(y[w+t]))}E(p)},onDrag:function(t){let{draggedDistance:e}=t;if(null===b)return;let n="bottom"===c||"right"===c?b-e:b+e;("bottom"!==c&&"right"!==c||!(n<y[y.length-1]))&&(("top"===c||"left"===c)&&n>y[y.length-1]||x(a.current,{transform:T(c)?"translate3d(0, ".concat(n,"px, 0)"):"translate3d(".concat(n,"px, 0, 0)")}))},snapPointsOffset:y}}({snapPoints:C,activeSnapPointProp:H,setActiveSnapPointProp:z,drawerRef:ty,fadeFromIndex:B,overlayRef:tu,onSnapPointChange:tx,direction:X,container:K,snapToSequentialPoint:$});!function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{isDisabled:e}=t;s(()=>{if(!e){var t,n,a;let e,o,i,l,u,d,s;return 1==++w&&c()&&(i=0,l=window.pageXOffset,u=window.pageYOffset,d=f((t=document.documentElement,n="paddingRight",a="".concat(window.innerWidth-document.documentElement.clientWidth,"px"),e=t.style[n],t.style[n]=a,()=>{t.style[n]=e})),window.scrollTo(0,0),s=f(g(document,"touchstart",t=>{((o=h(t.target))!==document.documentElement||o!==document.body)&&(i=t.changedTouches[0].pageY)},{passive:!1,capture:!0}),g(document,"touchmove",t=>{if(!o||o===document.documentElement||o===document.body)return void t.preventDefault();let e=t.changedTouches[0].pageY,n=o.scrollTop,r=o.scrollHeight-o.clientHeight;0!==r&&((n<=0&&e>i||n>=r&&e<i)&&t.preventDefault(),i=e)},{passive:!1,capture:!0}),g(document,"touchend",t=>{let e=t.target;b(e)&&e!==document.activeElement&&(t.preventDefault(),e.style.transform="translateY(-2000px)",e.focus(),requestAnimationFrame(()=>{e.style.transform=""}))},{passive:!1,capture:!0}),g(document,"focus",t=>{let e=t.target;b(e)&&(e.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{e.style.transform="",m&&(m.height<window.innerHeight?requestAnimationFrame(()=>{y(e)}):m.addEventListener("resize",()=>y(e),{once:!0}))}))},!0),g(window,"scroll",()=>{window.scrollTo(0,0)})),r=()=>{d(),s(),window.scrollTo(l,u)}),()=>{0==--w&&(null==r||r())}}},[e])}({isDisabled:!tt||ta||!U||ti||!tn||!Z||!_});let{restorePositionSetting:tk}=function(t){let{isOpen:e,modal:n,nested:r,hasBeenOpened:a,preventScrollRestoration:i,noBodyStyles:l}=t,[c,d]=o.useState(()=>"undefined"!=typeof window?window.location.href:""),s=o.useRef(0),f=o.useCallback(()=>{if(u()&&null===k&&e&&!l){k={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left,height:document.body.style.height,right:"unset"};let{scrollX:t,innerHeight:e}=window;document.body.style.setProperty("position","fixed","important"),Object.assign(document.body.style,{top:"".concat(-s.current,"px"),left:"".concat(-t,"px"),right:"0px",height:"auto"}),window.setTimeout(()=>window.requestAnimationFrame(()=>{let t=e-window.innerHeight;t&&s.current>=e&&(document.body.style.top="".concat(-(s.current+t),"px"))}),300)}},[e]),m=o.useCallback(()=>{if(u()&&null!==k&&!l){let t=-parseInt(document.body.style.top,10),e=-parseInt(document.body.style.left,10);Object.assign(document.body.style,k),window.requestAnimationFrame(()=>{if(i&&c!==window.location.href)return void d(window.location.href);window.scrollTo(e,t)}),k=null}},[c]);return o.useEffect(()=>{function t(){s.current=window.scrollY}return t(),window.addEventListener("scroll",t),()=>{window.removeEventListener("scroll",t)}},[]),o.useEffect(()=>{if(n)return()=>{"undefined"!=typeof document&&(document.querySelector("[data-vaul-drawer]")||m())}},[n,m]),o.useEffect(()=>{!r&&a&&(e?(window.matchMedia("(display-mode: standalone)").matches||f(),n||window.setTimeout(()=>{m()},500)):m())},[e,a,c,n,r,f,m]),{restorePositionSetting:m}}({isOpen:tt,modal:U,nested:null!=q&&q,hasBeenOpened:tn,preventScrollRestoration:J,noBodyStyles:Y});function tN(){return(window.innerWidth-26)/window.innerWidth}function tF(t,e){var n;let r=t,a=null==(n=window.getSelection())?void 0:n.toString(),o=ty.current?D(ty.current,X):null,i=new Date;if("SELECT"===r.tagName||r.hasAttribute("data-vaul-no-drag")||r.closest("[data-vaul-no-drag]"))return!1;if("right"===X||"left"===X)return!0;if(tc.current&&i.getTime()-tc.current.getTime()<500)return!1;if(null!==o&&("bottom"===X?o>0:o<0))return!0;if(a&&a.length>0)return!1;if(tf.current&&i.getTime()-tf.current.getTime()<F&&0===o||e)return tf.current=i,!1;for(;r;){if(r.scrollHeight>r.clientHeight){if(0!==r.scrollTop)return tf.current=new Date,!1;if("dialog"===r.getAttribute("role"))break}r=r.parentNode}return!0}function tI(t){ta&&ty.current&&(ty.current.classList.remove(M),tm.current=!1,to(!1),ts.current=new Date),null==j||j(),t||te(!1),setTimeout(()=>{C&&tC(C[0])},1e3*A.DURATION)}function tL(){if(!ty.current)return;let t=document.querySelector("[data-vaul-drawer-wrapper]"),e=D(ty.current,X);x(ty.current,{transform:"translate3d(0, 0, 0)",transition:"transform ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")")}),x(tu.current,{transition:"opacity ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),opacity:"1"}),O&&e&&e>0&&tt&&x(t,{borderRadius:"".concat(8,"px"),overflow:"hidden",...T(X)?{transform:"scale(".concat(tN(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)"),transformOrigin:"top"}:{transform:"scale(".concat(tN(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)"),transformOrigin:"left"},transitionProperty:"transform, border-radius",transitionDuration:"".concat(A.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(A.EASE.join(","),")")},!0)}return o.useEffect(()=>{window.requestAnimationFrame(()=>{tw.current=!0})},[]),o.useEffect(()=>{var t;function e(){if(ty.current&&Z&&(b(document.activeElement)||tv.current)){var t;let e=(null==(t=window.visualViewport)?void 0:t.height)||0,n=window.innerHeight,r=n-e,a=ty.current.getBoundingClientRect().height||0;tR.current||(tR.current=a);let o=ty.current.getBoundingClientRect().top;if(Math.abs(tg.current-r)>60&&(tv.current=!tv.current),C&&C.length>0&&tM&&tD&&(r+=tM[tD]||0),tg.current=r,a>e||tv.current){let t=ty.current.getBoundingClientRect().height,i=t;t>e&&(i=e-(a>.8*n?o:26)),W?ty.current.style.height="".concat(t-Math.max(r,0),"px"):ty.current.style.height="".concat(Math.max(i,e-o),"px")}else!function(){let t=navigator.userAgent;return"undefined"!=typeof window&&(/Firefox/.test(t)&&/Mobile/.test(t)||/FxiOS/.test(t))}()&&(ty.current.style.height="".concat(tR.current,"px"));C&&C.length>0&&!tv.current?ty.current.style.bottom="0px":ty.current.style.bottom="".concat(Math.max(r,0),"px")}}return null==(t=window.visualViewport)||t.addEventListener("resize",e),()=>{var t;return null==(t=window.visualViewport)?void 0:t.removeEventListener("resize",e)}},[tD,C,tM]),o.useEffect(()=>(tt&&(x(document.documentElement,{scrollBehavior:"auto"}),tc.current=new Date),()=>{!function(t,e){if(!t||!(t instanceof HTMLElement))return;let n=R.get(t);n&&(t.style[e]=n[e])}(document.documentElement,"scrollBehavior")}),[tt]),o.useEffect(()=>{U||window.requestAnimationFrame(()=>{document.body.style.pointerEvents="auto"})},[U]),o.createElement(a.bL,{defaultOpen:V,onOpenChange:t=>{(I||t)&&(t?tr(!0):tI(!0),te(t))},open:tt},o.createElement(i.Provider,{value:{activeSnapPoint:tT,snapPoints:C,setActiveSnapPoint:tC,drawerRef:ty,overlayRef:tu,onOpenChange:d,onPress:function(t){var e,n;(I||C)&&(!ty.current||ty.current.contains(t.target))&&(tb.current=(null==(e=ty.current)?void 0:e.getBoundingClientRect().height)||0,tE.current=(null==(n=ty.current)?void 0:n.getBoundingClientRect().width)||0,to(!0),td.current=new Date,c()&&window.addEventListener("touchend",()=>tm.current=!1,{once:!0}),t.target.setPointerCapture(t.pointerId),th.current=T(X)?t.pageY:t.pageX)},onRelease:function(t){var e,n;if(!ta||!ty.current)return;ty.current.classList.remove(M),tm.current=!1,to(!1),ts.current=new Date;let r=D(ty.current,X);if(!t||!tF(t.target,!1)||!r||Number.isNaN(r)||null===td.current)return;let a=ts.current.getTime()-td.current.getTime(),o=th.current-(T(X)?t.pageY:t.pageX),i=Math.abs(o)/a;if(i>.05&&(tl(!0),setTimeout(()=>{tl(!1)},200)),C){tA({draggedDistance:o*("bottom"===X||"right"===X?1:-1),closeDrawer:tI,velocity:i,dismissible:I}),null==E||E(t,!0);return}if("bottom"===X||"right"===X?o>0:o<0){tL(),null==E||E(t,!0);return}if(i>.4){tI(),null==E||E(t,!1);return}let l=Math.min(null!=(e=ty.current.getBoundingClientRect().height)?e:0,window.innerHeight),u=Math.min(null!=(n=ty.current.getBoundingClientRect().width)?n:0,window.innerWidth);if(Math.abs(r)>=("left"===X||"right"===X?u:l)*N){tI(),null==E||E(t,!1);return}null==E||E(t,!0),tL()},onDrag:function(t){if(ty.current&&ta){let e="bottom"===X||"right"===X?1:-1,n=(th.current-(T(X)?t.pageY:t.pageX))*e,r=n>0,a=C&&!I&&!r;if(a&&0===tD)return;let o=Math.abs(n),i=document.querySelector("[data-vaul-drawer-wrapper]"),l=o/("bottom"===X||"top"===X?tb.current:tE.current),u=tS(o,r);if(null!==u&&(l=u),a&&l>=1||!tm.current&&!tF(t.target,r))return;if(ty.current.classList.add(M),tm.current=!0,x(ty.current,{transition:"none"}),x(tu.current,{transition:"none"}),C&&tO({draggedDistance:n}),r&&!C){let t=Math.min(-(8*(Math.log(n+1)-2)*1),0)*e;x(ty.current,{transform:T(X)?"translate3d(0, ".concat(t,"px, 0)"):"translate3d(".concat(t,"px, 0, 0)")});return}let c=1-l;if((tP||B&&tD===B-1)&&(null==v||v(t,l),x(tu.current,{opacity:"".concat(c),transition:"none"},!0)),i&&tu.current&&O){let t=Math.min(tN()+l*(1-tN()),1),e=8-8*l,n=Math.max(0,14-14*l);x(i,{borderRadius:"".concat(e,"px"),transform:T(X)?"scale(".concat(t,") translate3d(0, ").concat(n,"px, 0)"):"scale(".concat(t,") translate3d(").concat(n,"px, 0, 0)"),transition:"none"},!0)}if(!C){let t=o*e;x(ty.current,{transform:T(X)?"translate3d(0, ".concat(t,"px, 0)"):"translate3d(".concat(t,"px, 0, 0)")})}}},dismissible:I,shouldAnimate:tw,handleOnly:L,isOpen:tt,isDragging:ta,shouldFade:tP,closeDrawer:tI,onNestedDrag:function(t,e){if(e<0)return;let n=(window.innerWidth-16)/window.innerWidth,r=n+e*(1-n),a=-16+16*e;x(ty.current,{transform:T(X)?"scale(".concat(r,") translate3d(0, ").concat(a,"px, 0)"):"scale(".concat(r,") translate3d(").concat(a,"px, 0, 0)"),transition:"none"})},onNestedOpenChange:function(t){let e=t?(window.innerWidth-16)/window.innerWidth:1,n=t?-16:0;tp.current&&window.clearTimeout(tp.current),x(ty.current,{transition:"transform ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),transform:T(X)?"scale(".concat(e,") translate3d(0, ").concat(n,"px, 0)"):"scale(".concat(e,") translate3d(").concat(n,"px, 0, 0)")}),!t&&ty.current&&(tp.current=setTimeout(()=>{let t=D(ty.current,X);x(ty.current,{transition:"none",transform:T(X)?"translate3d(0, ".concat(t,"px, 0)"):"translate3d(".concat(t,"px, 0, 0)")})},500))},onNestedRelease:function(t,e){let n=T(X)?window.innerHeight:window.innerWidth,r=e?(n-16)/n:1,a=e?-16:0;e&&x(ty.current,{transition:"transform ".concat(A.DURATION,"s cubic-bezier(").concat(A.EASE.join(","),")"),transform:T(X)?"scale(".concat(r,") translate3d(0, ").concat(a,"px, 0)"):"scale(".concat(r,") translate3d(").concat(a,"px, 0, 0)")})},keyboardIsOpen:tv,modal:U,snapPointsOffset:tM,activeSnapPointIndex:tD,direction:X,shouldScaleBackground:O,setBackgroundColorOnScale:S,noBodyStyles:Y,container:K,autoFocus:Q}},p))}let F=o.forwardRef(function(t,e){let{...n}=t,{overlayRef:r,snapPoints:i,onRelease:u,shouldFade:c,isOpen:d,modal:s,shouldAnimate:f}=l(),m=E(e,r),p=i&&i.length>0;if(!s)return null;let h=o.useCallback(t=>u(t),[u]);return o.createElement(a.hJ,{onMouseUp:h,ref:m,"data-vaul-overlay":"","data-vaul-snap-points":d&&p?"true":"false","data-vaul-snap-points-overlay":d&&c?"true":"false","data-vaul-animate":(null==f?void 0:f.current)?"true":"false",...n})});F.displayName="Drawer.Overlay";let I=o.forwardRef(function(t,e){let{onPointerDownOutside:n,style:r,onOpenAutoFocus:i,...u}=t,{drawerRef:c,onPress:d,onRelease:s,onDrag:f,keyboardIsOpen:m,snapPointsOffset:p,activeSnapPointIndex:h,modal:v,isOpen:w,direction:g,snapPoints:y,container:b,handleOnly:R,shouldAnimate:x,autoFocus:D}=l(),[M,O]=o.useState(!1),P=E(e,c),k=o.useRef(null),N=o.useRef(null),F=o.useRef(!1),I=y&&y.length>0,{direction:L,isOpen:B,shouldScaleBackground:H,setBackgroundColorOnScale:z,noBodyStyles:W}=l(),U=o.useRef(null),j=(0,o.useMemo)(()=>document.body.style.backgroundColor,[]);function q(){return(window.innerWidth-26)/window.innerWidth}o.useEffect(()=>{if(B&&H){U.current&&clearTimeout(U.current);let t=document.querySelector("[data-vaul-drawer-wrapper]")||document.querySelector("[vaul-drawer-wrapper]");if(!t)return;!function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n]}(z&&!W?C(document.body,{background:"black"}):S,C(t,{transformOrigin:T(L)?"top":"left",transitionProperty:"transform, border-radius",transitionDuration:"".concat(A.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(A.EASE.join(","),")")}));let e=C(t,{borderRadius:"".concat(8,"px"),overflow:"hidden",...T(L)?{transform:"scale(".concat(q(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)")}:{transform:"scale(".concat(q(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)")}});return()=>{e(),U.current=window.setTimeout(()=>{j?document.body.style.background=j:document.body.style.removeProperty("background")},1e3*A.DURATION)}}},[B,H,j]);let Y=function(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(F.current)return!0;let r=Math.abs(t.y),a=Math.abs(t.x),o=a>r,i=["bottom","right"].includes(e)?1:-1;if("left"===e||"right"===e){if(!(t.x*i<0)&&a>=0&&a<=n)return o}else if(!(t.y*i<0)&&r>=0&&r<=n)return!o;return F.current=!0,!0};function X(t){k.current=null,F.current=!1,s(t)}return o.useEffect(()=>{I&&window.requestAnimationFrame(()=>{O(!0)})},[]),o.createElement(a.UC,{"data-vaul-drawer-direction":g,"data-vaul-drawer":"","data-vaul-delayed-snap-points":M?"true":"false","data-vaul-snap-points":w&&I?"true":"false","data-vaul-custom-container":b?"true":"false","data-vaul-animate":(null==x?void 0:x.current)?"true":"false",...u,ref:P,style:p&&p.length>0?{"--snap-point-height":"".concat(p[null!=h?h:0],"px"),...r}:r,onPointerDown:t=>{R||(null==u.onPointerDown||u.onPointerDown.call(u,t),k.current={x:t.pageX,y:t.pageY},d(t))},onOpenAutoFocus:t=>{null==i||i(t),D||t.preventDefault()},onPointerDownOutside:t=>{if(null==n||n(t),!v||t.defaultPrevented)return void t.preventDefault();m.current&&(m.current=!1)},onFocusOutside:t=>{if(!v)return void t.preventDefault()},onPointerMove:t=>{if(N.current=t,R||(null==u.onPointerMove||u.onPointerMove.call(u,t),!k.current))return;let e=t.pageY-k.current.y,n=t.pageX-k.current.x,r="touch"===t.pointerType?10:2;Y({x:n,y:e},g,r)?f(t):(Math.abs(n)>r||Math.abs(e)>r)&&(k.current=null)},onPointerUp:t=>{null==u.onPointerUp||u.onPointerUp.call(u,t),k.current=null,F.current=!1,s(t)},onPointerOut:t=>{null==u.onPointerOut||u.onPointerOut.call(u,t),X(N.current)},onContextMenu:t=>{null==u.onContextMenu||u.onContextMenu.call(u,t),N.current&&X(N.current)}})});I.displayName="Drawer.Content";let L=o.forwardRef(function(t,e){let{preventCycle:n=!1,children:r,...a}=t,{closeDrawer:i,isDragging:u,snapPoints:c,activeSnapPoint:d,setActiveSnapPoint:s,dismissible:f,handleOnly:m,isOpen:p,onPress:h,onDrag:v}=l(),w=o.useRef(null),g=o.useRef(!1);function y(){w.current&&window.clearTimeout(w.current),g.current=!1}return o.createElement("div",{onClick:function(){if(g.current)return void y();window.setTimeout(()=>{!function(){if(u||n||g.current)return y();if(y(),!c||0===c.length){f||i();return}if(d===c[c.length-1]&&f)return i();let t=c.findIndex(t=>t===d);-1!==t&&s(c[t+1])}()},120)},onPointerCancel:y,onPointerDown:t=>{m&&h(t),w.current=window.setTimeout(()=>{g.current=!0},250)},onPointerMove:t=>{m&&v(t)},ref:e,"data-vaul-drawer-visible":p?"true":"false","data-vaul-handle":"","aria-hidden":"true",...a},o.createElement("span",{"data-vaul-handle-hitarea":"","aria-hidden":"true"},r))});L.displayName="Drawer.Handle";let B={Root:N,NestedRoot:function(t){let{onDrag:e,onOpenChange:n,open:r,...a}=t,{onNestedDrag:i,onNestedOpenChange:u,onNestedRelease:c}=l();if(!i)throw Error("Drawer.NestedRoot must be placed in another drawer");return o.createElement(N,{nested:!0,open:r,onClose:()=>{u(!1)},onDrag:(t,n)=>{i(t,n),null==e||e(t,n)},onOpenChange:t=>{t&&u(t),null==n||n(t)},onRelease:c,...a})},Content:I,Overlay:F,Trigger:a.l9,Portal:function(t){let e=l(),{container:n=e.container,...r}=t;return o.createElement(a.ZL,{container:n,...r})},Handle:L,Close:a.bm,Title:a.hE,Description:a.VY}}}]);