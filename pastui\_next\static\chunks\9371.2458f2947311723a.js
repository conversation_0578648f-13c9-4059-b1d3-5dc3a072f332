"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9371],{59371:(e,r,t)=>{t.r(r),t.d(r,{ScrollArea:()=>q,ScrollBar:()=>J});var o=t(53891),n=t(73987),l=t(4513),i=t(71138),a=t(80428),s=t(77310),c=t(15949),d=t(41105),u=t(637),p=t(90707),f=t(77292),h="ScrollArea",[v,w]=(0,a.A)(h),[b,g]=v(h),m=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:i="hover",dir:a,scrollHideDelay:c=600,...u}=e,[p,f]=n.useState(null),[h,v]=n.useState(null),[w,g]=n.useState(null),[m,S]=n.useState(null),[x,E]=n.useState(null),[C,y]=n.useState(0),[T,R]=n.useState(0),[L,P]=n.useState(!1),[j,_]=n.useState(!1),D=(0,s.s)(r,e=>f(e)),A=(0,d.jH)(a);return(0,o.jsx)(b,{scope:t,type:i,dir:A,scrollHideDelay:c,scrollArea:p,viewport:h,onViewportChange:v,content:w,onContentChange:g,scrollbarX:m,onScrollbarXChange:S,scrollbarXEnabled:L,onScrollbarXEnabledChange:P,scrollbarY:x,onScrollbarYChange:E,scrollbarYEnabled:j,onScrollbarYEnabledChange:_,onCornerWidthChange:y,onCornerHeightChange:R,children:(0,o.jsx)(l.sG.div,{dir:A,...u,ref:D,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":T+"px",...e.style}})})});m.displayName=h;var S="ScrollAreaViewport",x=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:i,nonce:a,...c}=e,d=g(S,t),u=n.useRef(null),p=(0,s.s)(r,u,d.onViewportChange);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,o.jsx)(l.sG.div,{"data-radix-scroll-area-viewport":"",...c,ref:p,style:{overflowX:d.scrollbarXEnabled?"scroll":"hidden",overflowY:d.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,o.jsx)("div",{ref:d.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=S;var E="ScrollAreaScrollbar",C=n.forwardRef((e,r)=>{let{forceMount:t,...l}=e,i=g(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:s}=i,c="horizontal"===e.orientation;return n.useEffect(()=>(c?a(!0):s(!0),()=>{c?a(!1):s(!1)}),[c,a,s]),"hover"===i.type?(0,o.jsx)(y,{...l,ref:r,forceMount:t}):"scroll"===i.type?(0,o.jsx)(T,{...l,ref:r,forceMount:t}):"auto"===i.type?(0,o.jsx)(R,{...l,ref:r,forceMount:t}):"always"===i.type?(0,o.jsx)(L,{...l,ref:r}):null});C.displayName=E;var y=n.forwardRef((e,r)=>{let{forceMount:t,...l}=e,a=g(E,e.__scopeScrollArea),[s,c]=n.useState(!1);return n.useEffect(()=>{let e=a.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),c(!0)},o=()=>{r=window.setTimeout(()=>c(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",o),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",o)}}},[a.scrollArea,a.scrollHideDelay]),(0,o.jsx)(i.C,{present:t||s,children:(0,o.jsx)(R,{"data-state":s?"visible":"hidden",...l,ref:r})})}),T=n.forwardRef((e,r)=>{var t,l;let{forceMount:a,...s}=e,c=g(E,e.__scopeScrollArea),d="horizontal"===e.orientation,u=G(()=>h("SCROLL_END"),100),[p,h]=(t="hidden",l={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,r)=>{let t=l[e][r];return null!=t?t:e},t));return n.useEffect(()=>{if("idle"===p){let e=window.setTimeout(()=>h("HIDE"),c.scrollHideDelay);return()=>window.clearTimeout(e)}},[p,c.scrollHideDelay,h]),n.useEffect(()=>{let e=c.viewport,r=d?"scrollLeft":"scrollTop";if(e){let t=e[r],o=()=>{let o=e[r];t!==o&&(h("SCROLL"),u()),t=o};return e.addEventListener("scroll",o),()=>e.removeEventListener("scroll",o)}},[c.viewport,d,h,u]),(0,o.jsx)(i.C,{present:a||"hidden"!==p,children:(0,o.jsx)(L,{"data-state":"hidden"===p?"hidden":"visible",...s,ref:r,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),R=n.forwardRef((e,r)=>{let t=g(E,e.__scopeScrollArea),{forceMount:l,...a}=e,[s,c]=n.useState(!1),d="horizontal"===e.orientation,u=G(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;c(d?e:r)}},10);return V(t.viewport,u),V(t.content,u),(0,o.jsx)(i.C,{present:l||s,children:(0,o.jsx)(L,{"data-state":s?"visible":"hidden",...a,ref:r})})}),L=n.forwardRef((e,r)=>{let{orientation:t="vertical",...l}=e,i=g(E,e.__scopeScrollArea),a=n.useRef(null),s=n.useRef(0),[c,d]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=Y(c.viewport,c.content),p={...l,sizes:c,onSizesChange:d,hasThumb:!!(u>0&&u<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function f(e,r){return function(e,r,t){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",n=k(t),l=r||n/2,i=t.scrollbar.paddingStart+l,a=t.scrollbar.size-t.scrollbar.paddingEnd-(n-l),s=t.content-t.viewport;return B([i,a],"ltr"===o?[0,s]:[-1*s,0])(e)}(e,s.current,c,r)}return"horizontal"===t?(0,o.jsx)(P,{...p,ref:r,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=U(i.viewport.scrollLeft,c,i.dir);a.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=f(e,i.dir))}}):"vertical"===t?(0,o.jsx)(j,{...p,ref:r,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=U(i.viewport.scrollTop,c);a.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=f(e))}}):null}),P=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:l,...i}=e,a=g(E,e.__scopeScrollArea),[c,d]=n.useState(),u=n.useRef(null),p=(0,s.s)(r,u,a.onScrollbarXChange);return n.useEffect(()=>{u.current&&d(getComputedStyle(u.current))},[u]),(0,o.jsx)(A,{"data-orientation":"horizontal",...i,ref:p,sizes:t,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":k(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(a.viewport){let o=a.viewport.scrollLeft+r.deltaX;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{u.current&&a.viewport&&c&&l({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:X(c.paddingLeft),paddingEnd:X(c.paddingRight)}})}})}),j=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:l,...i}=e,a=g(E,e.__scopeScrollArea),[c,d]=n.useState(),u=n.useRef(null),p=(0,s.s)(r,u,a.onScrollbarYChange);return n.useEffect(()=>{u.current&&d(getComputedStyle(u.current))},[u]),(0,o.jsx)(A,{"data-orientation":"vertical",...i,ref:p,sizes:t,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":k(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(a.viewport){let o=a.viewport.scrollTop+r.deltaY;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{u.current&&a.viewport&&c&&l({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:X(c.paddingTop),paddingEnd:X(c.paddingBottom)}})}})}),[_,D]=v(E),A=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:i,hasThumb:a,onThumbChange:d,onThumbPointerUp:u,onThumbPointerDown:p,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:w,onResize:b,...m}=e,S=g(E,t),[x,C]=n.useState(null),y=(0,s.s)(r,e=>C(e)),T=n.useRef(null),R=n.useRef(""),L=S.viewport,P=i.content-i.viewport,j=(0,c.c)(w),D=(0,c.c)(h),A=G(b,10);function N(e){T.current&&v({x:e.clientX-T.current.left,y:e.clientY-T.current.top})}return n.useEffect(()=>{let e=e=>{let r=e.target;(null==x?void 0:x.contains(r))&&j(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[L,x,P,j]),n.useEffect(D,[i,D]),V(x,A),V(S.content,A),(0,o.jsx)(_,{scope:t,scrollbar:x,hasThumb:a,onThumbChange:(0,c.c)(d),onThumbPointerUp:(0,c.c)(u),onThumbPositionChange:D,onThumbPointerDown:(0,c.c)(p),children:(0,o.jsx)(l.sG.div,{...m,ref:y,style:{position:"absolute",...m.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),T.current=x.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",S.viewport&&(S.viewport.style.scrollBehavior="auto"),N(e))}),onPointerMove:(0,f.m)(e.onPointerMove,N),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,S.viewport&&(S.viewport.style.scrollBehavior=""),T.current=null})})})}),N="ScrollAreaThumb",z=n.forwardRef((e,r)=>{let{forceMount:t,...n}=e,l=D(N,e.__scopeScrollArea);return(0,o.jsx)(i.C,{present:t||l.hasThumb,children:(0,o.jsx)(H,{ref:r,...n})})}),H=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:i,...a}=e,c=g(N,t),d=D(N,t),{onThumbPositionChange:u}=d,p=(0,s.s)(r,e=>d.onThumbChange(e)),h=n.useRef(void 0),v=G(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=c.viewport;if(e){let r=()=>{v(),h.current||(h.current=F(e,u),u())};return u(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[c.viewport,v,u]),(0,o.jsx)(l.sG.div,{"data-state":d.hasThumb?"visible":"hidden",...a,ref:p,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,o=e.clientY-r.top;d.onThumbPointerDown({x:t,y:o})}),onPointerUp:(0,f.m)(e.onPointerUp,d.onThumbPointerUp)})});z.displayName=N;var W="ScrollAreaCorner",I=n.forwardRef((e,r)=>{let t=g(W,e.__scopeScrollArea),n=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&n?(0,o.jsx)(O,{...e,ref:r}):null});I.displayName=W;var O=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,...i}=e,a=g(W,t),[s,c]=n.useState(0),[d,u]=n.useState(0),p=!!(s&&d);return V(a.scrollbarX,()=>{var e;let r=(null==(e=a.scrollbarX)?void 0:e.offsetHeight)||0;a.onCornerHeightChange(r),u(r)}),V(a.scrollbarY,()=>{var e;let r=(null==(e=a.scrollbarY)?void 0:e.offsetWidth)||0;a.onCornerWidthChange(r),c(r)}),p?(0,o.jsx)(l.sG.div,{...i,ref:r,style:{width:s,height:d,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function X(e){return e?parseInt(e,10):0}function Y(e,r){let t=e/r;return isNaN(t)?0:t}function k(e){let r=Y(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function U(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",o=k(r),n=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-n,i=r.content-r.viewport,a=(0,p.q)(e,"ltr"===t?[0,i]:[-1*i,0]);return B([0,i],[0,l-o])(a)}function B(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let o=(r[1]-r[0])/(e[1]-e[0]);return r[0]+o*(t-e[0])}}var F=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},t={left:e.scrollLeft,top:e.scrollTop},o=0;return!function n(){let l={left:e.scrollLeft,top:e.scrollTop},i=t.left!==l.left,a=t.top!==l.top;(i||a)&&r(),t=l,o=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(o)};function G(e,r){let t=(0,c.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(t,r)},[t,r])}function V(e,r){let t=(0,c.c)(r);(0,u.N)(()=>{let r=0;if(e){let o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,t])}var M=t(61971);function q(e){let{className:r,children:t,...n}=e;return(0,o.jsxs)(m,{"data-slot":"scroll-area",className:(0,M.cn)("relative",r),...n,children:[(0,o.jsx)(x,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] outline-none transition-[color,box-shadow] focus-visible:outline-1 focus-visible:ring-[3px]",children:t}),(0,o.jsx)(J,{}),(0,o.jsx)(I,{})]})}function J(e){let{className:r,orientation:t="vertical",...n}=e;return(0,o.jsx)(C,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,M.cn)("flex touch-none select-none p-px transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",r),...n,children:(0,o.jsx)(z,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}}}]);