// 全局变量
let chatId =
  "chat_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
let uploadedFiles = [];

// DOM元素
let chatMessages,
  messageInput,
  sendBtn,
  fileInput,
  uploadBtn,
  uploadedFilesDiv,
  loadingOverlay,
  charCount;

// 初始化
document.addEventListener("DOMContentLoaded", function () {
  // 获取DOM元素
  chatMessages = document.getElementById("chatMessages");
  messageInput = document.getElementById("messageInput");
  sendBtn = document.getElementById("sendBtn");
  fileInput = document.getElementById("fileInput");
  uploadBtn = document.getElementById("uploadBtn");
  uploadedFilesDiv = document.getElementById("uploadedFiles");
  loadingOverlay = document.getElementById("loadingOverlay");
  charCount = document.querySelector(".char-count");

  // 绑定事件
  sendBtn.addEventListener("click", sendMessage);
  messageInput.addEventListener("keypress", function (e) {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  });
  messageInput.addEventListener("input", function () {
    autoResize();
    updateCharCount();
  });
  uploadBtn.addEventListener("click", function () {
    fileInput.click();
  });
  fileInput.addEventListener("change", handleFileUpload);

  updateCharCount();
});

function autoResize() {
  messageInput.style.height = "auto";
  messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + "px";
}

function updateCharCount() {
  const count = messageInput.value.length;
  charCount.textContent = count + "/2000";
  charCount.style.color = count > 1800 ? "#ff4757" : "#666";
}

// 文件上传处理
async function handleFileUpload(event) {
  const files = Array.from(event.target.files);

  for (const file of files) {
    try {
      showLoading("正在上传文件...");
      const uploadedFile = await uploadFile(file);
      uploadedFiles.push(uploadedFile);
      displayUploadedFile(uploadedFile);
      addMessage("文件上传成功: " + file.name, "bot");
    } catch (error) {
      console.error("文件上传失败:", error);
      addMessage("文件上传失败: " + error.message, "bot");
    } finally {
      hideLoading();
    }
  }

  // 清空文件输入
  fileInput.value = "";
}

async function uploadFile(file) {
  const reader = new FileReader();

  return new Promise((resolve, reject) => {
    reader.onload = async function (e) {
      try {
        const base64 = e.target.result.split(",")[1];

        const response = await fetch("/api/chat/file", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: file.name,
            base64: base64,
            params: JSON.stringify({
              size: file.size,
              type: file.type,
            }),
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error("上传失败: " + response.status + " - " + errorText);
        }

        const result = await response.json();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = function () {
      reject(new Error("文件读取失败"));
    };
    reader.readAsDataURL(file);
  });
}

function displayUploadedFile(file) {
  const fileTag = document.createElement("div");
  fileTag.className = "file-tag";
  fileTag.innerHTML =
    "📄 " +
    file.id +
    ' <span class="remove-file" onclick="removeFile(\'' +
    file.id +
    "')\">&times;</span>";
  uploadedFilesDiv.appendChild(fileTag);
}

function removeFile(fileId) {
  uploadedFiles = uploadedFiles.filter((f) => f.id !== fileId);
  updateUploadedFilesDisplay();
}

function updateUploadedFilesDisplay() {
  uploadedFilesDiv.innerHTML = "";
  uploadedFiles.forEach((file) => displayUploadedFile(file));
}

// 发送消息
async function sendMessage() {
  const message = messageInput.value.trim();
  if (!message) return;

  // 显示用户消息
  addMessage(message, "user");
  messageInput.value = "";
  autoResize();
  updateCharCount();

  try {
    showLoading();
    const response = await callChatAPI(message);
    addMessage(response, "bot");
  } catch (error) {
    console.error("发送消息失败:", error);
    addMessage(
      "抱歉，发生了错误，请稍后重试。错误信息: " + error.message,
      "bot"
    );
  } finally {
    hideLoading();
  }
}

async function callChatAPI(message) {
  // 构建注释数组
  const annotations = [];
  if (uploadedFiles.length > 0) {
    annotations.push({
      type: "document_file",
      data: {
        files: uploadedFiles,
      },
    });
  }

  const requestBody = {
    id: chatId,
    messages: [
      {
        role: "user",
        content: message,
        annotations: annotations,
      },
    ],
    data: {},
  };

  console.log("发送请求:", JSON.stringify(requestBody, null, 2));

  const response = await fetch("/api/chat", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error("API调用失败: " + response.status + " - " + errorText);
  }

  const responseText = await response.text();
  console.log("原始响应:", responseText);

  return processResponse(responseText);
}

// 处理响应，解析引用信息
function processResponse(responseText) {
  try {
    console.log("=== 开始处理响应 ===");
    console.log("原始响应长度:", responseText.length);
    console.log("原始响应前500字符:", responseText.substring(0, 500));

    // 解析流式响应，提取实际内容
    let actualContent = "";
    let citationData = {};

    // 尝试从流式响应中提取tool_output.content
    const toolOutputMatch = responseText.match(
      /"tool_output":\s*{[^}]*"content":\s*"([^"]*(?:\\.[^"]*)*)"/
    );
    if (toolOutputMatch) {
      // 解码Unicode转义序列
      actualContent = toolOutputMatch[1].replace(
        /\\u([0-9a-fA-F]{4})/g,
        (match, code) => {
          return String.fromCharCode(parseInt(code, 16));
        }
      );
      console.log("✅ 从tool_output中提取的内容:", actualContent);
    } else {
      console.log("❌ 未找到tool_output内容，使用原始响应");
      actualContent = responseText;
    }

    // 提取引用数据
    const citationMatch = actualContent.match(/<!-- CITATION_DATA: (.*?) -->/s);
    if (citationMatch) {
      try {
        console.log("✅ 找到引用数据:", citationMatch[1]);
        citationData = JSON.parse(citationMatch[1]);
        console.log("✅ 解析后的引用数据:", citationData);
        // 移除引用数据注释
        actualContent = actualContent
          .replace(/<!-- CITATION_DATA: .*? -->/s, "")
          .trim();
        console.log("✅ 移除注释后的内容:", actualContent);
      } catch (e) {
        console.error("❌ 解析引用数据失败:", e);
        console.log("尝试修复JSON格式...");
        // 尝试修复常见的JSON格式问题
        let fixedJson = citationMatch[1].trim();
        if (!fixedJson.startsWith("{")) {
          fixedJson = "{" + fixedJson;
        }
        if (!fixedJson.endsWith("}")) {
          fixedJson = fixedJson + "}";
        }
        try {
          citationData = JSON.parse(fixedJson);
          console.log("✅ 修复后解析成功:", citationData);
          actualContent = actualContent
            .replace(/<!-- CITATION_DATA: .*? -->/s, "")
            .trim();
        } catch (e2) {
          console.error("❌ 修复后仍然解析失败:", e2);
        }
      }
    } else {
      console.log("❌ 未找到 CITATION_DATA 注释");
      // 检查是否有其他格式的引用
      console.log("检查是否包含【】格式的引用:", actualContent.includes("【"));
      console.log("检查是否包含[1]格式的引用:", actualContent.includes("[1]"));
    }

    // 处理引用，显示排名序号、文档名和内容
    const processedContent = processCitations(actualContent, citationData);

    return processedContent.trim() || "抱歉，没有收到有效回复。";
  } catch (error) {
    console.error("❌ 解析响应失败:", error);
    return "抱歉，解析回复时出现错误。";
  }
}

// 处理引用信息
function processCitations(text, citationData) {
  console.log("=== 处理引用信息 ===");
  console.log("输入文本:", text);
  console.log("引用数据:", citationData);

  // 首先尝试处理 [citation:id] 格式
  const citationRegex = /\[citation:([a-f0-9-]+)\]/gi;
  let processedText = text.replace(citationRegex, function (match, citationId) {
    console.log("✅ 找到 [citation:] 格式引用ID:", citationId);
    return createCitationElement(citationId, citationData[citationId]);
  });

  // 如果没有找到 [citation:] 格式，尝试处理 [数字] 和 【数字】 格式
  if (processedText === text) {
    console.log("未找到 [citation:] 格式，尝试处理数字引用格式");

    // 处理 [1] 格式
    const bracketRegex = /\[(\d+)\]/g;
    processedText = text.replace(bracketRegex, function (match, number) {
      console.log("✅ 找到 [数字] 格式引用:", number);

      // 尝试根据排名找到对应的引用数据
      let citation = null;
      for (const [id, data] of Object.entries(citationData)) {
        if (data.rank === parseInt(number)) {
          citation = data;
          console.log("✅ 根据排名找到引用数据:", citation);
          break;
        }
      }

      return createCitationElement(`rank-${number}`, citation, number);
    });

    // 如果还没有处理，尝试 【数字】 格式
    if (processedText === text) {
      const chineseRegex = /【(\d+)】/g;
      processedText = text.replace(chineseRegex, function (match, number) {
        console.log("✅ 找到 【数字】 格式引用:", number);

        // 尝试根据排名找到对应的引用数据
        let citation = null;
        for (const [id, data] of Object.entries(citationData)) {
          if (data.rank === parseInt(number)) {
            citation = data;
            console.log("✅ 根据排名找到引用数据:", citation);
            break;
          }
        }

        return createCitationElement(`rank-${number}`, citation, number);
      });
    }
  }

  console.log("✅ 处理后的文本:", processedText);
  return processedText;
}

// 创建引用元素
function createCitationElement(citationId, citation, rankNumber = null) {
  if (!citation) {
    console.log("❌ 未找到引用数据，使用简单格式");
    const displayText = rankNumber
      ? `文档-${rankNumber}`
      : `文档-${citationId.substring(0, 8)}`;
    return `<span class="citation" onclick="showCitation('${citationId}', null)">📄 ${displayText}</span>`;
  }

  console.log("✅ 创建引用元素:", citation);

  // 简化显示的文件名
  const displayName = citation.filename
    .replace(/^常见问题类-\d+_/, "")
    .replace(/\.txt$/, "");

  // 格式化相似度分数
  const similarityPercent = Math.round(citation.similarity_score * 100);

  return (
    '<span class="citation" onclick="showCitationDetails(\'' +
    citationId +
    "', '" +
    escapeHtml(JSON.stringify(citation)) +
    "')\">" +
    '<span class="citation-rank">' +
    citation.rank +
    "</span>" +
    '<span class="citation-tooltip">' +
    "排名: #" +
    citation.rank +
    " | 相似度: " +
    similarityPercent +
    "%<br>" +
    escapeHtml(citation.filename) +
    "</span>" +
    '<span class="citation-content">' +
    escapeHtml(citation.content) +
    "</span>" +
    "📄 " +
    escapeHtml(displayName) +
    "</span>"
  );
}

// 添加消息到聊天界面
function addMessage(text, sender) {
  const messageDiv = document.createElement("div");
  messageDiv.className = "message " + sender + "-message";

  const now = new Date();
  const timeString = now.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
  });

  // 对于机器人消息，直接使用HTML（因为已经处理过引用）
  // 对于用户消息，转义HTML
  const messageContent = sender === "bot" ? text : escapeHtml(text);

  messageDiv.innerHTML =
    '<div class="message-content"><div class="message-text">' +
    messageContent +
    '</div><div class="message-time">' +
    timeString +
    "</div></div>";

  chatMessages.appendChild(messageDiv);
  scrollToBottom();
}

function escapeHtml(text) {
  const div = document.createElement("div");
  div.textContent = text;
  return div.innerHTML;
}

function scrollToBottom() {
  chatMessages.scrollTop = chatMessages.scrollHeight;
}

function showLoading(text) {
  if (!text) text = "AI正在思考中...";
  loadingOverlay.querySelector(".loading-text").textContent = text;
  loadingOverlay.style.display = "flex";
  sendBtn.disabled = true;
}

function hideLoading() {
  loadingOverlay.style.display = "none";
  sendBtn.disabled = false;
}

// 显示引用详情
function showCitationDetails(citationId, citationStr) {
  console.log("显示引用详情:", citationId, citationStr);
  let citation = null;

  if (citationStr && typeof citationStr === "string") {
    try {
      citation = JSON.parse(citationStr);
    } catch (e) {
      console.error("解析引用数据失败:", e);
    }
  } else if (citationStr && typeof citationStr === "object") {
    citation = citationStr;
  }

  if (!citation) {
    alert(`引用ID: ${citationId}\n\n这是从知识库中引用的文档内容。`);
    return;
  }

  const similarityPercent = Math.round(citation.similarity_score * 100);
  const displayName = citation.filename
    .replace(/^常见问题类-\d+_/, "")
    .replace(/\.txt$/, "");

  const message =
    `📄 文档引用详情\n\n` +
    `排名序号: #${citation.rank}\n` +
    `文档名称: ${displayName}\n` +
    `完整文件名: ${citation.filename}\n` +
    `相似度分数: ${similarityPercent}%\n` +
    `引用ID: ${citationId}\n\n` +
    `文档内容预览:\n${citation.content}\n\n` +
    `💡 提示：鼠标悬浮在引用块上可以查看内容预览`;

  alert(message);
}

function showCitation(citationId, citation) {
  showCitationDetails(citationId, citation);
}
