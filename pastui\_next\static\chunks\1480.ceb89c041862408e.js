"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1480],{11480:(e,t,a)=>{a.r(t),a.d(t,{Switch:()=>j});var r=a(53891),n=a(73987),s=a(77292),c=a(77310),d=a(80428),i=a(25261),o=a(11425),l=a(14593),u=a(4513),p="Switch",[h,b]=(0,d.A)(p),[f,k]=h(p),g=n.forwardRef((e,t)=>{let{__scopeSwitch:a,name:d,checked:o,defaultChecked:l,required:h,disabled:b,value:k="on",onCheckedChange:g,form:w,...m}=e,[v,j]=n.useState(null),E=(0,c.s)(t,e=>j(e)),N=n.useRef(!1),C=!v||w||!!v.closest("form"),[S,R]=(0,i.i)({prop:o,defaultProp:null!=l&&l,onChange:g,caller:p});return(0,r.jsxs)(f,{scope:a,checked:S,disabled:b,children:[(0,r.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":h,"data-state":y(S),"data-disabled":b?"":void 0,disabled:b,value:k,...m,ref:E,onClick:(0,s.m)(e.onClick,e=>{R(e=>!e),C&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),C&&(0,r.jsx)(x,{control:v,bubbles:!N.current,name:d,value:k,checked:S,required:h,disabled:b,form:w,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var w="SwitchThumb",m=n.forwardRef((e,t)=>{let{__scopeSwitch:a,...n}=e,s=k(w,a);return(0,r.jsx)(u.sG.span,{"data-state":y(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:t})});m.displayName=w;var x=n.forwardRef((e,t)=>{let{__scopeSwitch:a,control:s,checked:d,bubbles:i=!0,...u}=e,p=n.useRef(null),h=(0,c.s)(p,t),b=(0,o.Z)(d),f=(0,l.X)(s);return n.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(b!==d&&t){let a=new Event("click",{bubbles:i});t.call(e,d),e.dispatchEvent(a)}},[b,d,i]),(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:d,...u,tabIndex:-1,ref:h,style:{...u.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var v=a(61971);function j(e){let{className:t,...a}=e;return(0,r.jsx)(g,{"data-slot":"switch",className:(0,v.cn)("data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 shadow-xs peer inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent outline-none transition-all focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(m,{"data-slot":"switch-thumb",className:(0,v.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}}}]);