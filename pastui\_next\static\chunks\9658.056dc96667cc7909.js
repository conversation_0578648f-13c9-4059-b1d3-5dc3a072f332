"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9658],{36704:(e,n,r)=>{r.d(n,{G5:()=>ez,H_:()=>eA,JU:()=>eF,Mz:()=>eT,Pb:()=>eq,UC:()=>eI,UE:()=>S,VF:()=>eU,YJ:()=>eO,ZL:()=>eS,ZP:()=>eX,bL:()=>eL,hN:()=>eK,i3:()=>eV,q7:()=>eN,wv:()=>eB,z6:()=>eG});var t=r(73987),o=r(77292),l=r(333),u=r(77310),a=r(80428),c=r(41105),i=r(72057),s=r(82614),d=r(17172),f=r(10145),p=r(32303),v=r(7600),m=r(71138),h=r(4513),g=r(76653),w=r(7349),x=r(15949),C=r(28823),y=r(97644),M=r(53891),_=["Enter"," "],b=["ArrowUp","PageDown","End"],k=["ArrowDown","PageUp","Home",...b],j={ltr:[..._,"ArrowRight"],rtl:[..._,"ArrowLeft"]},R={ltr:["ArrowLeft"],rtl:["ArrowRight"]},P="Menu",[E,D,L]=(0,l.N)(P),[T,S]=(0,a.A)(P,[L,p.Bk,g.RG]),I=(0,p.Bk)(),O=(0,g.RG)(),[F,N]=T(P),[A,G]=T(P),K=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:l,onOpenChange:u,modal:a=!0}=e,i=I(n),[s,d]=t.useState(null),f=t.useRef(!1),v=(0,x.c)(u),m=(0,c.jH)(l);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,M.jsx)(p.bL,{...i,children:(0,M.jsx)(F,{scope:n,open:r,onOpenChange:v,content:s,onContentChange:d,children:(0,M.jsx)(A,{scope:n,onClose:t.useCallback(()=>v(!1),[v]),isUsingKeyboardRef:f,dir:m,modal:a,children:o})})})};K.displayName=P;var U=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=I(r);return(0,M.jsx)(p.Mz,{...o,...t,ref:n})});U.displayName="MenuAnchor";var B="MenuPortal",[V,q]=T(B,{forceMount:void 0}),X=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,l=N(B,n);return(0,M.jsx)(V,{scope:n,forceMount:r,children:(0,M.jsx)(m.C,{present:r||l.open,children:(0,M.jsx)(v.Z,{asChild:!0,container:o,children:t})})})};X.displayName=B;var z="MenuContent",[Z,H]=T(z),Y=t.forwardRef((e,n)=>{let r=q(z,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,l=N(z,e.__scopeMenu),u=G(z,e.__scopeMenu);return(0,M.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(m.C,{present:t||l.open,children:(0,M.jsx)(E.Slot,{scope:e.__scopeMenu,children:u.modal?(0,M.jsx)(J,{...o,ref:n}):(0,M.jsx)(W,{...o,ref:n})})})})}),J=t.forwardRef((e,n)=>{let r=N(z,e.__scopeMenu),l=t.useRef(null),a=(0,u.s)(n,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,C.Eq)(e)},[]),(0,M.jsx)($,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),W=t.forwardRef((e,n)=>{let r=N(z,e.__scopeMenu);return(0,M.jsx)($,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),Q=(0,w.TL)("MenuContent.ScrollLock"),$=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:a,onOpenAutoFocus:c,onCloseAutoFocus:f,disableOutsidePointerEvents:v,onEntryFocus:m,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:x,onInteractOutside:C,onDismiss:_,disableOutsideScroll:j,...R}=e,P=N(z,r),E=G(z,r),L=I(r),T=O(r),S=D(r),[F,A]=t.useState(null),K=t.useRef(null),U=(0,u.s)(n,K,P.onContentChange),B=t.useRef(0),V=t.useRef(""),q=t.useRef(0),X=t.useRef(null),H=t.useRef("right"),Y=t.useRef(0),J=j?y.A:t.Fragment,W=e=>{var n,r;let t=V.current+e,o=S().filter(e=>!e.disabled),l=document.activeElement,u=null==(n=o.find(e=>e.ref.current===l))?void 0:n.textValue,a=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,l=r?e.indexOf(r):-1,u=(t=Math.max(l,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(u=u.filter(e=>e!==r));let a=u.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==r?a:void 0}(o.map(e=>e.textValue),t,u),c=null==(r=o.find(e=>e.textValue===a))?void 0:r.ref.current;!function e(n){V.current=n,window.clearTimeout(B.current),""!==n&&(B.current=window.setTimeout(()=>e(""),1e3))}(t),c&&setTimeout(()=>c.focus())};t.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,s.Oh)();let $=t.useCallback(e=>{var n,r;return H.current===(null==(n=X.current)?void 0:n.side)&&function(e,n){return!!n&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,l=n.length-1;e<n.length;l=e++){let u=n[e],a=n[l],c=u.x,i=u.y,s=a.x,d=a.y;i>t!=d>t&&r<(s-c)*(t-i)/(d-i)+c&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)}(e,null==(r=X.current)?void 0:r.area)},[]);return(0,M.jsx)(Z,{scope:r,searchRef:V,onItemEnter:t.useCallback(e=>{$(e)&&e.preventDefault()},[$]),onItemLeave:t.useCallback(e=>{var n;$(e)||(null==(n=K.current)||n.focus(),A(null))},[$]),onTriggerLeave:t.useCallback(e=>{$(e)&&e.preventDefault()},[$]),pointerGraceTimerRef:q,onPointerGraceIntentChange:t.useCallback(e=>{X.current=e},[]),children:(0,M.jsx)(J,{...j?{as:Q,allowPinchZoom:!0}:void 0,children:(0,M.jsx)(d.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,o.m)(c,e=>{var n;e.preventDefault(),null==(n=K.current)||n.focus({preventScroll:!0})}),onUnmountAutoFocus:f,children:(0,M.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:v,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:x,onInteractOutside:C,onDismiss:_,children:(0,M.jsx)(g.bL,{asChild:!0,...T,dir:E.dir,orientation:"vertical",loop:l,currentTabStopId:F,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.m)(m,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(p.UC,{role:"menu","aria-orientation":"vertical","data-state":eR(P.open),"data-radix-menu-content":"",dir:E.dir,...L,...R,ref:U,style:{outline:"none",...R.style},onKeyDown:(0,o.m)(R.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&W(e.key));let o=K.current;if(e.target!==o||!k.includes(e.key))return;e.preventDefault();let l=S().filter(e=>!e.disabled).map(e=>e.ref.current);b.includes(e.key)&&l.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(l)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eD(e=>{let n=e.target,r=Y.current!==e.clientX;e.currentTarget.contains(n)&&r&&(H.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Y.displayName=z;var ee=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,M.jsx)(h.sG.div,{role:"group",...t,ref:n})});ee.displayName="MenuGroup";var en=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,M.jsx)(h.sG.div,{...t,ref:n})});en.displayName="MenuLabel";var er="MenuItem",et="menu.itemSelect",eo=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:l,...a}=e,c=t.useRef(null),i=G(er,e.__scopeMenu),s=H(er,e.__scopeMenu),d=(0,u.s)(n,c),f=t.useRef(!1);return(0,M.jsx)(el,{...a,ref:d,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!r&&e){let n=new CustomEvent(et,{bubbles:!0,cancelable:!0});e.addEventListener(et,e=>null==l?void 0:l(e),{once:!0}),(0,h.hO)(e,n),n.defaultPrevented?f.current=!1:i.onClose()}}),onPointerDown:n=>{var r;null==(r=e.onPointerDown)||r.call(e,n),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var n;f.current||null==(n=e.currentTarget)||n.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=""!==s.searchRef.current;r||n&&" "===e.key||_.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=er;var el=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:l=!1,textValue:a,...c}=e,i=H(er,r),s=O(r),d=t.useRef(null),f=(0,u.s)(n,d),[p,v]=t.useState(!1),[m,w]=t.useState("");return t.useEffect(()=>{let e=d.current;if(e){var n;w((null!=(n=e.textContent)?n:"").trim())}},[c.children]),(0,M.jsx)(E.ItemSlot,{scope:r,disabled:l,textValue:null!=a?a:m,children:(0,M.jsx)(g.q7,{asChild:!0,...s,focusable:!l,children:(0,M.jsx)(h.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...c,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eD(e=>{l?i.onItemLeave(e):(i.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eD(e=>i.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),eu=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...l}=e;return(0,M.jsx)(ev,{scope:e.__scopeMenu,checked:r,children:(0,M.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eP(r)?"mixed":r,...l,ref:n,"data-state":eE(r),onSelect:(0,o.m)(l.onSelect,()=>null==t?void 0:t(!!eP(r)||!r),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ea="MenuRadioGroup",[ec,ei]=T(ea,{value:void 0,onValueChange:()=>{}}),es=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,l=(0,x.c)(t);return(0,M.jsx)(ec,{scope:e.__scopeMenu,value:r,onValueChange:l,children:(0,M.jsx)(ee,{...o,ref:n})})});es.displayName=ea;var ed="MenuRadioItem",ef=t.forwardRef((e,n)=>{let{value:r,...t}=e,l=ei(ed,e.__scopeMenu),u=r===l.value;return(0,M.jsx)(ev,{scope:e.__scopeMenu,checked:u,children:(0,M.jsx)(eo,{role:"menuitemradio","aria-checked":u,...t,ref:n,"data-state":eE(u),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=l.onValueChange)?void 0:e.call(l,r)},{checkForDefaultPrevented:!1})})})});ef.displayName=ed;var ep="MenuItemIndicator",[ev,em]=T(ep,{checked:!1}),eh=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,l=em(ep,r);return(0,M.jsx)(m.C,{present:t||eP(l.checked)||!0===l.checked,children:(0,M.jsx)(h.sG.span,{...o,ref:n,"data-state":eE(l.checked)})})});eh.displayName=ep;var eg=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,M.jsx)(h.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});eg.displayName="MenuSeparator";var ew=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=I(r);return(0,M.jsx)(p.i3,{...o,...t,ref:n})});ew.displayName="MenuArrow";var ex="MenuSub",[eC,ey]=T(ex),eM=e=>{let{__scopeMenu:n,children:r,open:o=!1,onOpenChange:l}=e,u=N(ex,n),a=I(n),[c,i]=t.useState(null),[s,d]=t.useState(null),v=(0,x.c)(l);return t.useEffect(()=>(!1===u.open&&v(!1),()=>v(!1)),[u.open,v]),(0,M.jsx)(p.bL,{...a,children:(0,M.jsx)(F,{scope:n,open:o,onOpenChange:v,content:s,onContentChange:d,children:(0,M.jsx)(eC,{scope:n,contentId:(0,f.B)(),triggerId:(0,f.B)(),trigger:c,onTriggerChange:i,children:r})})})};eM.displayName=ex;var e_="MenuSubTrigger",eb=t.forwardRef((e,n)=>{let r=N(e_,e.__scopeMenu),l=G(e_,e.__scopeMenu),a=ey(e_,e.__scopeMenu),c=H(e_,e.__scopeMenu),i=t.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=c,f={__scopeMenu:e.__scopeMenu},p=t.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return t.useEffect(()=>p,[p]),t.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,M.jsx)(U,{asChild:!0,...f,children:(0,M.jsx)(el,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":a.contentId,"data-state":eR(r.open),...e,ref:(0,u.t)(n,a.onTriggerChange),onClick:n=>{var t;null==(t=e.onClick)||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eD(n=>{c.onItemEnter(n),!n.defaultPrevented&&(e.disabled||r.open||i.current||(c.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eD(e=>{var n,t;p();let o=null==(n=r.content)?void 0:n.getBoundingClientRect();if(o){let n=null==(t=r.content)?void 0:t.dataset.side,l="right"===n,u=o[l?"left":"right"],a=o[l?"right":"left"];c.onPointerGraceIntentChange({area:[{x:e.clientX+(l?-5:5),y:e.clientY},{x:u,y:o.top},{x:a,y:o.top},{x:a,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>c.onPointerGraceIntentChange(null),300)}else{if(c.onTriggerLeave(e),e.defaultPrevented)return;c.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,n=>{let t=""!==c.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&j[l.dir].includes(n.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),n.preventDefault()}})})})});eb.displayName=e_;var ek="MenuSubContent",ej=t.forwardRef((e,n)=>{let r=q(z,e.__scopeMenu),{forceMount:l=r.forceMount,...a}=e,c=N(z,e.__scopeMenu),i=G(z,e.__scopeMenu),s=ey(ek,e.__scopeMenu),d=t.useRef(null),f=(0,u.s)(n,d);return(0,M.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(m.C,{present:l||c.open,children:(0,M.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)($,{id:s.contentId,"aria-labelledby":s.triggerId,...a,ref:f,align:"start",side:"rtl"===i.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;i.isUsingKeyboardRef.current&&(null==(n=d.current)||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&c.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{i.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=R[i.dir].includes(e.key);if(n&&r){var t;c.onOpenChange(!1),null==(t=s.trigger)||t.focus(),e.preventDefault()}})})})})})});function eR(e){return e?"open":"closed"}function eP(e){return"indeterminate"===e}function eE(e){return eP(e)?"indeterminate":e?"checked":"unchecked"}function eD(e){return n=>"mouse"===n.pointerType?e(n):void 0}ej.displayName=ek;var eL=K,eT=U,eS=X,eI=Y,eO=ee,eF=en,eN=eo,eA=eu,eG=es,eK=ef,eU=eh,eB=eg,eV=ew,eq=eM,eX=eb,ez=ej},41341:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(75779).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},89111:(e,n,r)=>{r.d(n,{A:()=>t});let t=(0,r(75779).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}}]);