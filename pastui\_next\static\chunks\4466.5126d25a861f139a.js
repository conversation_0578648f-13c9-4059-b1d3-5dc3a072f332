"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4466],{74466:(e,a,t)=>{t.r(a),t.d(a,{Label:()=>d});var l=t(53891),n=t(73987),s=t(4513),r=n.forwardRef((e,a)=>(0,l.jsx)(s.sG.label,{...e,ref:a,onMouseDown:a=>{var t;a.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));r.displayName="Label";var o=t(61971);function d(e){let{className:a,...t}=e;return(0,l.jsx)(r,{"data-slot":"label",className:(0,o.cn)("flex select-none items-center gap-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50",a),...t})}}}]);