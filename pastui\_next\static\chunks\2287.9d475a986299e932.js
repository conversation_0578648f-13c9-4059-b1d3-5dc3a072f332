"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2287,8484],{18484:(e,r,t)=>{t.r(r),t.d(r,{Toggle:()=>s,toggleVariants:()=>l});var a=t(53891),n=t(26774),o=t(52722);t(73987);var i=t(61971);let l=(0,o.F)("inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap",{variants:{variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground"},size:{default:"h-9 px-2 min-w-9",sm:"h-8 px-1.5 min-w-8",lg:"h-10 px-2.5 min-w-10"}},defaultVariants:{variant:"default",size:"default"}});function s(e){let{className:r,variant:t,size:o,...s}=e;return(0,a.jsx)(n.b,{"data-slot":"toggle",className:(0,i.cn)(l({variant:t,size:o,className:r})),...s})}},26774:(e,r,t)=>{t.d(r,{b:()=>u,l:()=>d});var a=t(73987),n=t(77292),o=t(25261),i=t(4513),l=t(53891),s="Toggle",d=a.forwardRef((e,r)=>{let{pressed:t,defaultPressed:a,onPressedChange:d,...u}=e,[c,g]=(0,o.i)({prop:t,onChange:d,defaultProp:null!=a&&a,caller:s});return(0,l.jsx)(i.sG.button,{type:"button","aria-pressed":c,"data-state":c?"on":"off","data-disabled":e.disabled?"":void 0,...u,ref:r,onClick:(0,n.m)(e.onClick,()=>{e.disabled||g(!c)})})});d.displayName=s;var u=d},42287:(e,r,t)=>{t.r(r),t.d(r,{ToggleGroup:()=>R,ToggleGroupItem:()=>N});var a=t(53891),n=t(73987),o=t(80428),i=t(4513),l=t(76653),s=t(26774),d=t(25261),u=t(41105),c="ToggleGroup",[g,p]=(0,o.A)(c,[l.RG]),f=(0,l.RG)(),v=n.forwardRef((e,r)=>{let{type:t,...n}=e;if("single"===t)return(0,a.jsx)(h,{...n,ref:r});if("multiple"===t)return(0,a.jsx)(b,{...n,ref:r});throw Error("Missing prop `type` expected on `".concat(c,"`"))});v.displayName=c;var[m,x]=g(c),h=n.forwardRef((e,r)=>{let{value:t,defaultValue:o,onValueChange:i=()=>{},...l}=e,[s,u]=(0,d.i)({prop:t,defaultProp:null!=o?o:"",onChange:i,caller:c});return(0,a.jsx)(m,{scope:e.__scopeToggleGroup,type:"single",value:n.useMemo(()=>s?[s]:[],[s]),onItemActivate:u,onItemDeactivate:n.useCallback(()=>u(""),[u]),children:(0,a.jsx)(_,{...l,ref:r})})}),b=n.forwardRef((e,r)=>{let{value:t,defaultValue:o,onValueChange:i=()=>{},...l}=e,[s,u]=(0,d.i)({prop:t,defaultProp:null!=o?o:[],onChange:i,caller:c}),g=n.useCallback(e=>u(function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...r,e]}),[u]),p=n.useCallback(e=>u(function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return r.filter(r=>r!==e)}),[u]);return(0,a.jsx)(m,{scope:e.__scopeToggleGroup,type:"multiple",value:s,onItemActivate:g,onItemDeactivate:p,children:(0,a.jsx)(_,{...l,ref:r})})});v.displayName=c;var[j,w]=g(c),_=n.forwardRef((e,r)=>{let{__scopeToggleGroup:t,disabled:n=!1,rovingFocus:o=!0,orientation:s,dir:d,loop:c=!0,...g}=e,p=f(t),v=(0,u.jH)(d),m={role:"group",dir:v,...g};return(0,a.jsx)(j,{scope:t,rovingFocus:o,disabled:n,children:o?(0,a.jsx)(l.bL,{asChild:!0,...p,orientation:s,dir:v,loop:c,children:(0,a.jsx)(i.sG.div,{...m,ref:r})}):(0,a.jsx)(i.sG.div,{...m,ref:r})})}),C="ToggleGroupItem",G=n.forwardRef((e,r)=>{let t=x(C,e.__scopeToggleGroup),o=w(C,e.__scopeToggleGroup),i=f(e.__scopeToggleGroup),s=t.value.includes(e.value),d=o.disabled||e.disabled,u={...e,pressed:s,disabled:d},c=n.useRef(null);return o.rovingFocus?(0,a.jsx)(l.q7,{asChild:!0,...i,focusable:!d,active:s,ref:c,children:(0,a.jsx)(k,{...u,ref:r})}):(0,a.jsx)(k,{...u,ref:r})});G.displayName=C;var k=n.forwardRef((e,r)=>{let{__scopeToggleGroup:t,value:n,...o}=e,i=x(C,t),l={role:"radio","aria-checked":e.pressed,"aria-pressed":void 0},d="single"===i.type?l:void 0;return(0,a.jsx)(s.l,{...d,...o,ref:r,onPressedChange:e=>{e?i.onItemActivate(n):i.onItemDeactivate(n)}})}),z=t(61971),y=t(18484);let T=n.createContext({size:"default",variant:"default"});function R(e){let{className:r,variant:t,size:n,children:o,...i}=e;return(0,a.jsx)(v,{"data-slot":"toggle-group","data-variant":t,"data-size":n,className:(0,z.cn)("group/toggle-group data-[variant=outline]:shadow-xs flex w-fit items-center rounded-md",r),...i,children:(0,a.jsx)(T.Provider,{value:{variant:t,size:n},children:o})})}function N(e){let{className:r,children:t,variant:o,size:i,...l}=e,s=n.useContext(T);return(0,a.jsx)(G,{"data-slot":"toggle-group-item","data-variant":s.variant||o,"data-size":s.size||i,className:(0,z.cn)((0,y.toggleVariants)({variant:s.variant||o,size:s.size||i}),"min-w-0 flex-1 shrink-0 rounded-none shadow-none first:rounded-l-md last:rounded-r-md focus:z-10 focus-visible:z-10 data-[variant=outline]:border-l-0 data-[variant=outline]:first:border-l",r),...l,children:t})}}}]);